# API Documentation

This folder contains API-related documentation and testing resources for the Broku Sales Dashboard.

## Contents

- `broku-sales-dashboard.postman_collection.json` - Complete Postman collection for API testing
- `README.md` - This file

## Postman Collection

The Postman collection includes all current API endpoints organized by feature:

### Authentication
- Register User
- Login User  
- Verify Token
- Get Current User Profile

### Users Management
- Get All Users (Admin)
- Get User Profile
- Update User Profile
- Change Password
- Get Users by Restaurant
- Get User Statistics
- CRUD operations for users

### Restaurants Management
- Get All Restaurants
- Get Active Restaurants
- Search Restaurants
- CRUD operations for restaurants
- Bulk operations

### Sales Management
- Get All Sales
- Sales Summary and Reports
- Sales Analytics
- CRUD operations for sales records
- Duplicate checking

### Dashboard
- Admin Dashboard Data
- User Dashboard Data
- Sales Trends and Breakdowns
- Recent Activity
- Metrics Summary

### Analysis
- Headquarters Analytics
- Branch Analytics
- KPI Progress
- Dashboard Analytics

### Audit Logs
- Get All Audit Logs
- Audit Log Statistics
- User Activity Logs
- Resource Audit Logs
- Create Audit Log Entries

### System
- Health Check
- API Information

### Metadata
- Get User Roles
- Get Departments

## Usage

1. Import the Postman collection into Postman
2. Set up environment variables:
   - `baseUrl`: http://localhost:3000/api/v1
   - `token`: Your authentication token (auto-set after login)
   - `restaurantId`, `userId`, `salesId`, etc.: Set as needed for testing

3. Start with the Authentication endpoints to get a token
4. The login endpoint automatically sets the token variable for subsequent requests

## Authentication

All endpoints except health check and API info require Bearer token authentication. The token is automatically extracted and set from the login response.
