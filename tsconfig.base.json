{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM"],
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    // Relaxed: Allow unused locals and parameters during development
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    // Relaxed: Allow undefined assignment to optional properties
    "exactOptionalPropertyTypes": false,
    "noImplicitOverride": true,
    // Relaxed: Allow dot notation for index signatures (e.g., process.env.NODE_ENV)
    "noPropertyAccessFromIndexSignature": false,
    // Relaxed: Don't require undefined checks for indexed access
    "noUncheckedIndexedAccess": false,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    "noFallthroughCasesInSwitch": true
  }
}
