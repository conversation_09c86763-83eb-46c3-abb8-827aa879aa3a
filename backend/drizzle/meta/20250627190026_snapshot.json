{"version": "5", "dialect": "mysql", "id": "d12ce5e9-34ef-4af8-ba00-2a0c9537075a", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"restaurants": {"name": "restaurants", "columns": {"restaurant_id": {"name": "restaurant_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "opening_time": {"name": "opening_time", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "closing_time": {"name": "closing_time", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"name_idx": {"name": "name_idx", "columns": ["name"], "isUnique": false}, "is_active_idx": {"name": "is_active_idx", "columns": ["is_active"], "isUnique": false}, "deleted_at_idx": {"name": "deleted_at_idx", "columns": ["deleted_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"restaurants_restaurant_id": {"name": "restaurants_restaurant_id", "columns": ["restaurant_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sales": {"name": "sales", "columns": {"sales_id": {"name": "sales_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "restaurant_id": {"name": "restaurant_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "sales_date": {"name": "sales_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "cash_amount": {"name": "cash_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "card_amount": {"name": "card_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "online_payment_amount": {"name": "online_payment_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "total_sales": {"name": "total_sales", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_discounts": {"name": "total_discounts", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "recorded_by_user_id": {"name": "recorded_by_user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"restaurant_id_idx": {"name": "restaurant_id_idx", "columns": ["restaurant_id"], "isUnique": false}, "sales_date_idx": {"name": "sales_date_idx", "columns": ["sales_date"], "isUnique": false}, "recorded_by_user_id_idx": {"name": "recorded_by_user_id_idx", "columns": ["recorded_by_user_id"], "isUnique": false}, "deleted_at_idx": {"name": "deleted_at_idx", "columns": ["deleted_at"], "isUnique": false}, "restaurant_sales_date_idx": {"name": "restaurant_sales_date_idx", "columns": ["restaurant_id", "sales_date"], "isUnique": false}}, "foreignKeys": {"sales_restaurant_id_restaurants_restaurant_id_fk": {"name": "sales_restaurant_id_restaurants_restaurant_id_fk", "tableFrom": "sales", "tableTo": "restaurants", "columnsFrom": ["restaurant_id"], "columnsTo": ["restaurant_id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_recorded_by_user_id_users_user_id_fk": {"name": "sales_recorded_by_user_id_users_user_id_fk", "tableFrom": "sales", "tableTo": "users", "columnsFrom": ["recorded_by_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sales_sales_id": {"name": "sales_sales_id", "columns": ["sales_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "enum('admin','staff','user')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "restaurant_id": {"name": "restaurant_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"restaurant_id_idx": {"name": "restaurant_id_idx", "columns": ["restaurant_id"], "isUnique": false}}, "foreignKeys": {"users_restaurant_id_restaurants_restaurant_id_fk": {"name": "users_restaurant_id_restaurants_restaurant_id_fk", "tableFrom": "users", "tableTo": "restaurants", "columnsFrom": ["restaurant_id"], "columnsTo": ["restaurant_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"users_user_id": {"name": "users_user_id", "columns": ["user_id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}