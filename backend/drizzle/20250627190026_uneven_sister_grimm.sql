CREATE TABLE `restaurants` (
	`restaurant_id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`address` varchar(500) NOT NULL,
	`phone_number` varchar(20) NOT NULL,
	`opening_time` varchar(10) NOT NULL,
	`closing_time` varchar(10) NOT NULL,
	`is_active` boolean NOT NULL DEFAULT true,
	`description` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` timestamp,
	CONSTRAINT `restaurants_restaurant_id` PRIMARY KEY(`restaurant_id`)
);
--> statement-breakpoint
CREATE TABLE `sales` (
	`sales_id` int AUTO_INCREMENT NOT NULL,
	`restaurant_id` int NOT NULL,
	`sales_date` date NOT NULL,
	`cash_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`card_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`online_payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`total_sales` decimal(10,2) NOT NULL,
	`total_discounts` decimal(10,2) NOT NULL DEFAULT '0.00',
	`recorded_by_user_id` int NOT NULL,
	`notes` text,
	`recorded_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` timestamp,
	CONSTRAINT `sales_sales_id` PRIMARY KEY(`sales_id`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`user_id` int AUTO_INCREMENT NOT NULL,
	`username` varchar(100) NOT NULL,
	`password` varchar(255) NOT NULL,
	`role` enum('admin','staff','user') NOT NULL DEFAULT 'user',
	`full_name` varchar(255) NOT NULL,
	`restaurant_id` int,
	CONSTRAINT `users_user_id` PRIMARY KEY(`user_id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`)
);
--> statement-breakpoint
ALTER TABLE `sales` ADD CONSTRAINT `sales_restaurant_id_restaurants_restaurant_id_fk` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants`(`restaurant_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sales` ADD CONSTRAINT `sales_recorded_by_user_id_users_user_id_fk` FOREIGN KEY (`recorded_by_user_id`) REFERENCES `users`(`user_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `users` ADD CONSTRAINT `users_restaurant_id_restaurants_restaurant_id_fk` FOREIGN KEY (`restaurant_id`) REFERENCES `restaurants`(`restaurant_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX `name_idx` ON `restaurants` (`name`);--> statement-breakpoint
CREATE INDEX `is_active_idx` ON `restaurants` (`is_active`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `restaurants` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `restaurant_id_idx` ON `sales` (`restaurant_id`);--> statement-breakpoint
CREATE INDEX `sales_date_idx` ON `sales` (`sales_date`);--> statement-breakpoint
CREATE INDEX `recorded_by_user_id_idx` ON `sales` (`recorded_by_user_id`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `sales` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `restaurant_sales_date_idx` ON `sales` (`restaurant_id`,`sales_date`);--> statement-breakpoint
CREATE INDEX `restaurant_id_idx` ON `users` (`restaurant_id`);