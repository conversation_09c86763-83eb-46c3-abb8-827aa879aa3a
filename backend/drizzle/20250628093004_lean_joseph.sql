-- Add email column as nullable first
ALTER TABLE `users`
ADD `email` varchar(255);
--> statement-breakpoint
-- Update existing users with email addresses based on username
UPDATE `users`
SET `email` = CONCAT(`username`, '@example.com')
WHERE `email` IS NULL
    OR `email` = '';
--> statement-breakpoint
-- Make email column NOT NULL
ALTER TABLE `users`
MODIFY `email` varchar(255) NOT NULL;
--> statement-breakpoint
-- Add unique constraint
ALTER TABLE `users`
ADD CONSTRAINT `users_email_unique` UNIQUE(`email`);