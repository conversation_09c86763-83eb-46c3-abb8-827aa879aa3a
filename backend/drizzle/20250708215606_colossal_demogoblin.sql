CREATE TABLE `audit_logs` (
	`id` varchar(36) NOT NULL,
	`user_id` int,
	`action` varchar(100) NOT NULL,
	`resource_type` varchar(50),
	`resource_id` varchar(100),
	`old_values` json,
	`new_values` json,
	`ip_address` varchar(45),
	`user_agent` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`deleted_at` timestamp,
	CONSTRAINT `audit_logs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `audit_logs` ADD CONSTRAINT `audit_logs_user_id_users_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX `user_id_idx` ON `audit_logs` (`user_id`);--> statement-breakpoint
CREATE INDEX `action_idx` ON `audit_logs` (`action`);--> statement-breakpoint
CREATE INDEX `resource_type_idx` ON `audit_logs` (`resource_type`);--> statement-breakpoint
CREATE INDEX `resource_id_idx` ON `audit_logs` (`resource_id`);--> statement-breakpoint
CREATE INDEX `created_at_idx` ON `audit_logs` (`created_at`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `audit_logs` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `user_action_idx` ON `audit_logs` (`user_id`,`action`);--> statement-breakpoint
CREATE INDEX `resource_idx` ON `audit_logs` (`resource_type`,`resource_id`);