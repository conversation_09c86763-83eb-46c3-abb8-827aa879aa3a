import { eq, and, like, or, count, desc, asc, isNull, isNotNull, gte, lte, sql } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { db, type Database } from '../../infrastructure/db/client';
import {
  auditLogs,
  type AuditLog,
  type AuditLogWithUser,
  type CreateAuditLogData,
  type UpdateAuditLogData,
  type AuditLogQueryParams,
  type AuditLogStats,
  type AuditLogFilterOptions,
} from '../models/audit-log.model';
import { users } from '../models/user.model';
import { dbLogger } from '../../infrastructure/logger/pino';
import { paginate } from '../../infrastructure/db/utils';
import {
  IAuditLogRepository,
  type PaginatedResult,
} from '../../business/interfaces/repositories/IAuditLogRepository';

export class AuditLogRepository implements IAuditLogRepository {
  constructor(private readonly database: Database = db) { }

  async create(data: CreateAuditLogData): Promise<AuditLog> {
    try {
      const auditLogData = {
        ...data,
        id: data.id || randomUUID(),
        createdAt: new Date(),
      };

      await this.database
        .insert(auditLogs)
        .values(auditLogData);

      const createdLog = await this.findById(auditLogData.id);
      if (!createdLog) {
        throw new Error('Failed to create audit log');
      }

      dbLogger.info('Audit log created', { id: auditLogData.id, action: data.action });
      return createdLog;
    } catch (error) {
      dbLogger.error('Error creating audit log:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<AuditLog | null> {
    try {
      const [result] = await this.database
        .select()
        .from(auditLogs)
        .where(eq(auditLogs.id, id))
        .limit(1);

      return result || null;
    } catch (error) {
      dbLogger.error('Error finding audit log by ID:', error);
      throw error;
    }
  }

  async findByIdWithUser(id: string): Promise<AuditLogWithUser | null> {
    try {
      const [result] = await this.database
        .select({
          id: auditLogs.id,
          userId: auditLogs.userId,
          action: auditLogs.action,
          resourceType: auditLogs.resourceType,
          resourceId: auditLogs.resourceId,
          oldValues: auditLogs.oldValues,
          newValues: auditLogs.newValues,
          ipAddress: auditLogs.ipAddress,
          userAgent: auditLogs.userAgent,
          createdAt: auditLogs.createdAt,
          deletedAt: auditLogs.deletedAt,
          performedByName: users.fullName,
          performedByEmail: users.email,
        })
        .from(auditLogs)
        .leftJoin(users, eq(auditLogs.userId, users.userId))
        .where(eq(auditLogs.id, id))
        .limit(1);

      return result || null;
    } catch (error) {
      dbLogger.error('Error finding audit log with user by ID:', error);
      throw error;
    }
  }

  async findAll(params: AuditLogQueryParams = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    try {
      const conditions = this.buildWhereConditions(params);

      const sortableColumns = {
        createdAt: auditLogs.createdAt,
        action: auditLogs.action,
        userId: auditLogs.userId,
      };

      const sortColumn = sortableColumns[params.sortBy || 'createdAt'];

      const mainQuery = this.database
        .select({
          id: auditLogs.id,
          userId: auditLogs.userId,
          action: auditLogs.action,
          resourceType: auditLogs.resourceType,
          resourceId: auditLogs.resourceId,
          oldValues: auditLogs.oldValues,
          newValues: auditLogs.newValues,
          ipAddress: auditLogs.ipAddress,
          userAgent: auditLogs.userAgent,
          createdAt: auditLogs.createdAt,
          deletedAt: auditLogs.deletedAt,
          performedByName: users.fullName,
          performedByEmail: users.email,
        })
        .from(auditLogs)
        .leftJoin(users, eq(auditLogs.userId, users.userId))
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(
          params.sortOrder === 'desc'
            ? desc(sortColumn)
            : asc(sortColumn)
        );

      const countQuery = this.database
        .select({ count: count() })
        .from(auditLogs)
        .leftJoin(users, eq(auditLogs.userId, users.userId))
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const result = await paginate(mainQuery, countQuery, {
        page: params.page || 1,
        limit: params.limit || 50,
      });

      return {
        data: result.data as AuditLogWithUser[],
        pagination: result.pagination,
      };
    } catch (error) {
      dbLogger.error('Error finding all audit logs:', error);
      throw error;
    }
  }

  async findByUserId(userId: number, params: Omit<AuditLogQueryParams, 'userId'> = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    return this.findAll({ ...params, userId });
  }

  async findByResource(resourceType: string, resourceId: string, params: Omit<AuditLogQueryParams, 'resourceType' | 'resourceId'> = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    return this.findAll({ ...params, resourceType, resourceId });
  }

  async findByAction(action: string, params: Omit<AuditLogQueryParams, 'action'> = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    return this.findAll({ ...params, action });
  }

  async findByDateRange(startDate: Date, endDate: Date, params: Omit<AuditLogQueryParams, 'dateFrom' | 'dateTo'> = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    return this.findAll({
      ...params,
      dateFrom: startDate.toISOString(),
      dateTo: endDate.toISOString(),
    });
  }

  async update(id: string, data: UpdateAuditLogData): Promise<AuditLog | null> {
    try {
      await this.database
        .update(auditLogs)
        .set(data)
        .where(eq(auditLogs.id, id));

      return this.findById(id);
    } catch (error) {
      dbLogger.error('Error updating audit log:', error);
      throw error;
    }
  }

  async softDelete(id: string): Promise<boolean> {
    try {
      const result = await this.update(id, { deletedAt: new Date() });
      return !!result;
    } catch (error) {
      dbLogger.error('Error soft deleting audit log:', error);
      throw error;
    }
  }

  async restore(id: string): Promise<boolean> {
    try {
      const result = await this.update(id, { deletedAt: null });
      return !!result;
    } catch (error) {
      dbLogger.error('Error restoring audit log:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      await this.database
        .delete(auditLogs)
        .where(eq(auditLogs.id, id));

      return true;
    } catch (error) {
      dbLogger.error('Error deleting audit log:', error);
      throw error;
    }
  }

  private buildWhereConditions(params: AuditLogQueryParams) {
    const conditions = [];

    // Include deleted filter
    if (!params.includeDeleted) {
      conditions.push(isNull(auditLogs.deletedAt));
    }

    // Search filter
    if (params.search) {
      const searchTerm = `%${params.search}%`;
      conditions.push(
        or(
          like(auditLogs.action, searchTerm),
          like(auditLogs.resourceType, searchTerm),
          like(auditLogs.resourceId, searchTerm),
          like(auditLogs.ipAddress, searchTerm),
          like(users.fullName, searchTerm),
          like(users.email, searchTerm)
        )
      );
    }

    // Action filter
    if (params.action) {
      conditions.push(eq(auditLogs.action, params.action));
    }

    // User ID filter
    if (params.userId) {
      conditions.push(eq(auditLogs.userId, params.userId));
    }

    // Resource type filter
    if (params.resourceType) {
      conditions.push(eq(auditLogs.resourceType, params.resourceType));
    }

    // Resource ID filter
    if (params.resourceId) {
      conditions.push(eq(auditLogs.resourceId, params.resourceId));
    }

    // Date range filters
    if (params.dateFrom) {
      conditions.push(gte(auditLogs.createdAt, new Date(params.dateFrom)));
    }

    if (params.dateTo) {
      conditions.push(lte(auditLogs.createdAt, new Date(params.dateTo)));
    }

    return conditions;
  }

  async getStats(params: Pick<AuditLogQueryParams, 'dateFrom' | 'dateTo' | 'userId' | 'resourceType'> = {}): Promise<AuditLogStats> {
    try {
      const conditions = this.buildWhereConditions(params);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Total logs
      const [totalResult] = await this.database
        .select({ count: count() })
        .from(auditLogs)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      // Today's logs
      const [todayResult] = await this.database
        .select({ count: count() })
        .from(auditLogs)
        .where(and(
          gte(auditLogs.createdAt, today),
          ...(conditions.length > 0 ? conditions : [])
        ));

      // Week's logs
      const [weekResult] = await this.database
        .select({ count: count() })
        .from(auditLogs)
        .where(and(
          gte(auditLogs.createdAt, weekAgo),
          ...(conditions.length > 0 ? conditions : [])
        ));

      // Month's logs
      const [monthResult] = await this.database
        .select({ count: count() })
        .from(auditLogs)
        .where(and(
          gte(auditLogs.createdAt, monthAgo),
          ...(conditions.length > 0 ? conditions : [])
        ));

      // Top actions
      const topActions = await this.database
        .select({
          action: auditLogs.action,
          count: count(),
        })
        .from(auditLogs)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .groupBy(auditLogs.action)
        .orderBy(desc(count()))
        .limit(10);

      // Top users
      const topUsers = await this.database
        .select({
          userId: auditLogs.userId,
          userName: users.fullName,
          count: count(),
        })
        .from(auditLogs)
        .leftJoin(users, eq(auditLogs.userId, users.userId))
        .where(and(
          isNotNull(auditLogs.userId),
          ...(conditions.length > 0 ? conditions : [])
        ))
        .groupBy(auditLogs.userId, users.fullName)
        .orderBy(desc(count()))
        .limit(10);

      return {
        totalLogs: totalResult?.count || 0,
        todayLogs: todayResult?.count || 0,
        weekLogs: weekResult?.count || 0,
        monthLogs: monthResult?.count || 0,
        topActions: topActions.map(item => ({
          action: item.action,
          count: item.count,
        })),
        topUsers: topUsers.map(item => ({
          userId: item.userId!,
          userName: item.userName || 'Unknown',
          count: item.count,
        })),
      };
    } catch (error) {
      dbLogger.error('Error getting audit log stats:', error);
      throw error;
    }
  }

  async getFilterOptions(): Promise<AuditLogFilterOptions> {
    try {
      // Get unique actions
      const actions = await this.database
        .selectDistinct({ action: auditLogs.action })
        .from(auditLogs)
        .where(isNull(auditLogs.deletedAt));

      // Get users who have audit logs
      const auditUsers = await this.database
        .selectDistinct({
          userId: auditLogs.userId,
          userName: users.fullName,
        })
        .from(auditLogs)
        .leftJoin(users, eq(auditLogs.userId, users.userId))
        .where(and(
          isNull(auditLogs.deletedAt),
          isNotNull(auditLogs.userId)
        ));

      // Get unique resource types
      const resourceTypes = await this.database
        .selectDistinct({ resourceType: auditLogs.resourceType })
        .from(auditLogs)
        .where(and(
          isNull(auditLogs.deletedAt),
          isNotNull(auditLogs.resourceType)
        ));

      return {
        actions: actions.map(item => ({
          value: item.action,
          label: item.action.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          color: this.getActionColor(item.action),
        })),
        users: auditUsers.map(item => ({
          userId: item.userId!,
          userName: item.userName || 'Unknown',
        })),
        resourceTypes: resourceTypes.map(item => ({
          value: item.resourceType!,
          label: item.resourceType!.replace(/\b\w/g, l => l.toUpperCase()),
        })),
      };
    } catch (error) {
      dbLogger.error('Error getting filter options:', error);
      throw error;
    }
  }

  async count(params: Omit<AuditLogQueryParams, 'page' | 'limit' | 'sortBy' | 'sortOrder'> = {}): Promise<number> {
    try {
      const conditions = this.buildWhereConditions(params);

      const [result] = await this.database
        .select({ count: count() })
        .from(auditLogs)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      return result?.count || 0;
    } catch (error) {
      dbLogger.error('Error counting audit logs:', error);
      throw error;
    }
  }

  async exists(id: string): Promise<boolean> {
    const result = await this.findById(id);
    return !!result;
  }

  async bulkCreate(data: CreateAuditLogData[]): Promise<AuditLog[]> {
    try {
      const auditLogData = data.map(item => ({
        ...item,
        id: item.id || randomUUID(),
        createdAt: new Date(),
      }));

      await this.database
        .insert(auditLogs)
        .values(auditLogData);

      // Fetch created logs
      const ids = auditLogData.map(item => item.id);
      const createdLogs = await this.database
        .select()
        .from(auditLogs)
        .where(sql`${auditLogs.id} IN (${ids.map(id => `'${id}'`).join(',')})`);

      dbLogger.info('Bulk created audit logs', { count: auditLogData.length });
      return createdLogs;
    } catch (error) {
      dbLogger.error('Error bulk creating audit logs:', error);
      throw error;
    }
  }

  async cleanupOldLogs(olderThanDays: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await this.database
        .delete(auditLogs)
        .where(lte(auditLogs.createdAt, cutoffDate));

      dbLogger.info('Cleaned up old audit logs', { olderThanDays, deletedCount: result });
      return result as unknown as number;
    } catch (error) {
      dbLogger.error('Error cleaning up old audit logs:', error);
      throw error;
    }
  }

  private getActionColor(action: string): string {
    if (action.includes('created')) return 'bg-green-100 text-green-800';
    if (action.includes('updated')) return 'bg-blue-100 text-blue-800';
    if (action.includes('deleted')) return 'bg-red-100 text-red-800';
    if (action.includes('login')) return 'bg-purple-100 text-purple-800';
    if (action.includes('logout')) return 'bg-gray-100 text-gray-800';
    return 'bg-gray-100 text-gray-800';
  }
}

// Export singleton instance
export const auditLogRepository = new AuditLogRepository();
