import { eq, isNull, and, desc, asc, like, or, count, ne } from 'drizzle-orm';
import { db, type Database } from '../../infrastructure/db/client';
import {
  restaurants,
  type Restaurant as RestaurantEntity,
  type CreateRestaurantPayload,
  type UpdateRestaurantPayload,
  type ActiveRestaurant as ActiveRestaurantEntity
} from '../models/restaurant.model';
import { dbLogger } from '../../infrastructure/logger/pino';
import { paginate } from '../../infrastructure/db/utils';
import {
  IRestaurantRepository,
  type Restaurant,
  type ActiveRestaurant,
  type RestaurantQueryParams,
  type CreateRestaurantData,
  type UpdateRestaurantData,
  type PaginatedResult
} from '../../business/interfaces/repositories/IRestaurantRepository';

export class RestaurantRepository implements IRestaurantRepository {
  constructor(private readonly database: Database = db) { }

  // Entity to domain model mapping
  private mapToRestaurant(entity: RestaurantEntity): Restaurant {
    return {
      restaurantId: entity.restaurantId,
      name: entity.name,
      address: entity.address,
      phone: entity.phoneNumber,
      description: entity.description,
      isActive: entity.isActive,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    };
  }

  private mapToActiveRestaurant(entity: ActiveRestaurantEntity): ActiveRestaurant {
    return {
      restaurantId: entity.restaurantId,
      name: entity.name,
      address: entity.address,
      isActive: entity.isActive,
    };
  }

  // Implementation of IRestaurantRepository interface
  async findAll(query: RestaurantQueryParams): Promise<PaginatedResult<Restaurant>> {
    try {
      const conditions = [];

      if (query.isActive !== undefined) {
        conditions.push(eq(restaurants.isActive, query.isActive));
      }

      if (query.search) {
        conditions.push(
          or(
            like(restaurants.name, `%${query.search}%`),
            like(restaurants.address, `%${query.search}%`),
            like(restaurants.description, `%${query.search}%`)
          )
        );
      }

      // Always exclude soft deleted restaurants
      conditions.push(isNull(restaurants.deletedAt));

      const sortableColumns = {
        name: restaurants.name,
        createdAt: restaurants.createdAt,
        updatedAt: restaurants.updatedAt,
      };
      const sortColumn = sortableColumns[query.sortBy || 'name'];

      const mainQuery = this.database
        .select()
        .from(restaurants)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(
          query.sortOrder === 'desc'
            ? desc(sortColumn)
            : asc(sortColumn)
        );

      const countQuery = this.database
        .select({ count: count() })
        .from(restaurants)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const result = await paginate(mainQuery, countQuery, {
        page: query.page || 1,
        limit: query.limit || 10,
      });

      return {
        data: (result.data as RestaurantEntity[]).map(entity => this.mapToRestaurant(entity)),
        pagination: result.pagination,
      };
    } catch (error) {
      dbLogger.error('Error finding all restaurants:', error);
      throw error;
    }
  }

  async create(restaurantData: CreateRestaurantData): Promise<Restaurant> {
    try {
      dbLogger.info('Creating new restaurant', { name: restaurantData.name });

      // Convert CreateRestaurantData to CreateRestaurantPayload
      const payload: CreateRestaurantPayload = {
        name: restaurantData.name,
        address: restaurantData.address,
        phoneNumber: restaurantData.phone || '',
        description: restaurantData.description,
        isActive: restaurantData.isActive ?? true,
        openingTime: '09:00', // Default values - these should be configurable
        closingTime: '22:00',
      };

      const result = await this.database
        .insert(restaurants)
        .values(payload);

      const insertId = result[0].insertId;
      const createdRestaurant = await this.findById(Number(insertId));
      if (!createdRestaurant) {
        throw new Error('Failed to retrieve created restaurant');
      }

      dbLogger.info('Restaurant created successfully', { restaurantId: createdRestaurant.restaurantId });
      return createdRestaurant;
    } catch (error) {
      dbLogger.error('Error creating restaurant:', error);
      throw error;
    }
  }

  async findById(id: number): Promise<Restaurant | null> {
    try {
      const [restaurant] = await this.database
        .select()
        .from(restaurants)
        .where(eq(restaurants.restaurantId, id))
        .limit(1);

      return restaurant ? this.mapToRestaurant(restaurant) : null;
    } catch (error) {
      dbLogger.error('Error finding restaurant by ID:', error);
      throw error;
    }
  }

  async findActiveById(id: number): Promise<ActiveRestaurant | null> {
    try {
      const [restaurant] = await this.database
        .select()
        .from(restaurants)
        .where(and(
          eq(restaurants.restaurantId, id),
          isNull(restaurants.deletedAt),
          eq(restaurants.isActive, true)
        ))
        .limit(1);

      return restaurant as ActiveRestaurant || null;
    } catch (error) {
      dbLogger.error('Error finding active restaurant by ID:', error);
      throw error;
    }
  }



  async findAllActive(): Promise<ActiveRestaurant[]> {
    try {
      const restaurantList = await this.database
        .select()
        .from(restaurants)
        .where(and(
          isNull(restaurants.deletedAt),
          eq(restaurants.isActive, true)
        ))
        .orderBy(asc(restaurants.name));

      return restaurantList.map(restaurant => this.mapToActiveRestaurant(restaurant as ActiveRestaurantEntity));
    } catch (error) {
      dbLogger.error('Error finding all active restaurants:', error);
      throw error;
    }
  }

  async findByName(name: string): Promise<Restaurant | null> {
    try {
      const [restaurant] = await this.database
        .select()
        .from(restaurants)
        .where(eq(restaurants.name, name))
        .limit(1);

      return restaurant ? this.mapToRestaurant(restaurant) : null;
    } catch (error) {
      dbLogger.error('Error finding restaurant by name:', error);
      throw error;
    }
  }

  async update(id: number, data: Partial<UpdateRestaurantData>): Promise<Restaurant | null> {
    try {
      dbLogger.info('Updating restaurant', { restaurantId: id });

      // Convert UpdateRestaurantData to database format
      const updateData: Partial<UpdateRestaurantPayload> = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.address !== undefined) updateData.address = data.address;
      if (data.phone !== undefined) updateData.phoneNumber = data.phone;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;

      await this.database
        .update(restaurants)
        .set(updateData)
        .where(eq(restaurants.restaurantId, id));

      const updatedRestaurant = await this.findById(id);
      if (updatedRestaurant) {
        dbLogger.info('Restaurant updated successfully', { restaurantId: id });
      }

      return updatedRestaurant;
    } catch (error) {
      dbLogger.error('Error updating restaurant:', error);
      throw error;
    }
  }

  async softDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Soft deleting restaurant', { restaurantId: id });

      const result = await this.database
        .update(restaurants)
        .set({
          deletedAt: new Date(),
          isActive: false
        })
        .where(eq(restaurants.restaurantId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Restaurant soft deleted successfully', { restaurantId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error soft deleting restaurant:', error);
      throw error;
    }
  }

  async restore(id: number): Promise<boolean> {
    try {
      dbLogger.info('Restoring restaurant', { restaurantId: id });

      const result = await this.database
        .update(restaurants)
        .set({
          deletedAt: null,
          isActive: true
        })
        .where(eq(restaurants.restaurantId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Restaurant restored successfully', { restaurantId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error restoring restaurant:', error);
      throw error;
    }
  }

  async hardDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Hard deleting restaurant', { restaurantId: id });

      const result = await this.database
        .delete(restaurants)
        .where(eq(restaurants.restaurantId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Restaurant hard deleted successfully', { restaurantId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error hard deleting restaurant:', error);
      throw error;
    }
  }

  async nameExists(name: string, excludeId?: number): Promise<boolean> {
    try {
      const conditions = [eq(restaurants.name, name)];

      if (excludeId) {
        // If excludeId is provided, we want to exclude that restaurant from the check
        conditions.push(ne(restaurants.restaurantId, excludeId));
      }

      const [restaurant] = await this.database
        .select()
        .from(restaurants)
        .where(conditions.length > 1 ? and(...conditions) : conditions[0])
        .limit(1);

      return !!restaurant;
    } catch (error) {
      dbLogger.error('Error checking if restaurant name exists:', error);
      throw error;
    }
  }
}

export const restaurantRepository = new RestaurantRepository();
