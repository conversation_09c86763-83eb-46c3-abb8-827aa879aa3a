import { mysqlTable, varchar, int, mysqlEnum, index, timestamp, boolean } from 'drizzle-orm/mysql-core';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import { restaurants } from './restaurant.model';

// User table schema
export const users = mysqlTable('users', {
  userId: int('user_id').primaryKey().autoincrement(),
  username: varchar('username', { length: 100 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }).notNull(),
  role: mysqlEnum('role', ['admin', 'staff', 'user']).notNull().default('user'),
  fullName: varchar('full_name', { length: 255 }).notNull(),
  restaurantId: int('restaurant_id').references(() => restaurants.restaurantId),
  isActive: boolean('is_active').notNull().default(true),
  lastLoginAt: timestamp('last_login_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow(),
}, (table) => ({
  restaurantIdIdx: index('restaurant_id_idx').on(table.restaurantId),
}));

// Type definitions
export type User = InferSelectModel<typeof users>;
export type NewUser = InferInsertModel<typeof users>;

// User without password (for API responses)
export type SafeUser = Omit<User, 'password'>;

// User creation payload
export type CreateUserPayload = Omit<NewUser, 'userId'>;

// User update payload
export type UpdateUserPayload = Partial<Omit<NewUser, 'userId'>>;
