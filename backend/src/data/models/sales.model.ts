import { mysqlTable, timestamp, int, decimal, date, text, index } from 'drizzle-orm/mysql-core';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import { restaurants } from './restaurant.model';
import { users } from './user.model';

// Sales table schema
export const sales = mysqlTable('sales', {
  salesId: int('sales_id').primaryKey().autoincrement(),
  restaurantId: int('restaurant_id').notNull().references(() => restaurants.restaurantId),
  salesDate: date('sales_date').notNull(),
  cashAmount: decimal('cash_amount', { precision: 10, scale: 2 }).notNull().default('0.00'),
  cardAmount: decimal('card_amount', { precision: 10, scale: 2 }).notNull().default('0.00'),
  onlinePaymentAmount: decimal('online_payment_amount', { precision: 10, scale: 2 }).notNull().default('0.00'),
  totalSales: decimal('total_sales', { precision: 10, scale: 2 }).notNull(),
  totalDiscounts: decimal('total_discounts', { precision: 10, scale: 2 }).notNull().default('0.00'),
  recordedByUserId: int('recorded_by_user_id').notNull().references(() => users.userId),
  notes: text('notes'),
  recordedAt: timestamp('recorded_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow().notNull(),
  deletedAt: timestamp('deleted_at'),
}, (table) => ({
  restaurantIdIdx: index('restaurant_id_idx').on(table.restaurantId),
  salesDateIdx: index('sales_date_idx').on(table.salesDate),
  recordedByUserIdIdx: index('recorded_by_user_id_idx').on(table.recordedByUserId),
  deletedAtIdx: index('deleted_at_idx').on(table.deletedAt),
  restaurantSalesDateIdx: index('restaurant_sales_date_idx').on(table.restaurantId, table.salesDate),
}));

// Type definitions
export type Sales = InferSelectModel<typeof sales>;
export type NewSales = InferInsertModel<typeof sales>;

// Sales creation payload
export type CreateSalesPayload = Omit<NewSales, 'salesId' | 'recordedAt' | 'updatedAt' | 'deletedAt'>;

// Sales update payload
export type UpdateSalesPayload = Partial<Omit<NewSales, 'salesId' | 'recordedAt' | 'updatedAt' | 'deletedAt'>>;

// Sales with soft delete support
export type ActiveSales = Sales & { deletedAt: null };

// Sales summary type for reporting
export type SalesSummary = {
  totalSales: string;
  totalCash: string;
  totalCard: string;
  totalOnline: string;
  totalDiscounts: string;
  recordCount: number;
};
