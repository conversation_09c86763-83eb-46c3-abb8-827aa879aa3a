import { mysqlTable, varchar, timestamp, int, boolean, text, index } from 'drizzle-orm/mysql-core';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';

// Restaurant table schema
export const restaurants = mysqlTable('restaurants', {
  restaurantId: int('restaurant_id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  address: varchar('address', { length: 500 }).notNull(),
  phoneNumber: varchar('phone_number', { length: 20 }).notNull(),
  openingTime: varchar('opening_time', { length: 10 }).notNull(), // Format: "HH:MM"
  closingTime: varchar('closing_time', { length: 10 }).notNull(), // Format: "HH:MM"
  isActive: boolean('is_active').notNull().default(true),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow().notNull(),
  deletedAt: timestamp('deleted_at'),
}, (table) => ({
  nameIdx: index('name_idx').on(table.name),
  isActiveIdx: index('is_active_idx').on(table.isActive),
  deletedAtIdx: index('deleted_at_idx').on(table.deletedAt),
}));

// Type definitions
export type Restaurant = InferSelectModel<typeof restaurants>;
export type NewRestaurant = InferInsertModel<typeof restaurants>;

// Restaurant creation payload
export type CreateRestaurantPayload = Omit<NewRestaurant, 'restaurantId' | 'createdAt' | 'updatedAt' | 'deletedAt'>;

// Restaurant update payload
export type UpdateRestaurantPayload = Partial<Omit<NewRestaurant, 'restaurantId' | 'createdAt' | 'updatedAt' | 'deletedAt'>>;

// Restaurant with soft delete support
export type ActiveRestaurant = Restaurant & { deletedAt: null };
