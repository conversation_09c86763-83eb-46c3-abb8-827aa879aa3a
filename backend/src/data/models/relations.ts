import { relations } from 'drizzle-orm';
import { users } from './user.model';
import { restaurants } from './restaurant.model';
import { sales } from './sales.model';
import { auditLogs } from './audit-log.model';

// Restaurant relations
export const restaurantsRelations = relations(restaurants, ({ many }) => ({
  users: many(users),
  sales: many(sales),
}));

// User relations
export const usersRelations = relations(users, ({ one, many }) => ({
  restaurant: one(restaurants, {
    fields: [users.restaurantId],
    references: [restaurants.restaurantId],
  }),
  recordedSales: many(sales),
  auditLogs: many(auditLogs),
}));

// Sales relations
export const salesRelations = relations(sales, ({ one }) => ({
  restaurant: one(restaurants, {
    fields: [sales.restaurantId],
    references: [restaurants.restaurantId],
  }),
  recordedByUser: one(users, {
    fields: [sales.recordedByUserId],
    references: [users.userId],
  }),
}));

// Audit Log relations
export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  user: one(users, {
    fields: [auditLogs.userId],
    references: [users.userId],
  }),
}));
