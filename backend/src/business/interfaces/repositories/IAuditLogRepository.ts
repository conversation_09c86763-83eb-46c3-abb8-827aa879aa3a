import {
  AuditLog,
  AuditLogWithUser,
  CreateAuditLogData,
  UpdateAuditLogData,
  AuditLogQueryParams,
  AuditLogStats,
  AuditLogFilterOptions,
} from '../../../data/models/audit-log.model';

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface IAuditLogRepository {
  /**
   * Create a new audit log entry
   */
  create(data: CreateAuditLogData): Promise<AuditLog>;

  /**
   * Find audit log by ID
   */
  findById(id: string): Promise<AuditLog | null>;

  /**
   * Find audit logs with user information
   */
  findByIdWithUser(id: string): Promise<AuditLogWithUser | null>;

  /**
   * Find all audit logs with filtering, sorting, and pagination
   */
  findAll(params: AuditLogQueryParams): Promise<PaginatedResult<AuditLogWithUser>>;

  /**
   * Find audit logs for a specific user
   */
  findByUserId(userId: number, params?: Omit<AuditLogQueryParams, 'userId'>): Promise<PaginatedResult<AuditLogWithUser>>;

  /**
   * Find audit logs for a specific resource
   */
  findByResource(resourceType: string, resourceId: string, params?: Omit<AuditLogQueryParams, 'resourceType' | 'resourceId'>): Promise<PaginatedResult<AuditLogWithUser>>;

  /**
   * Find audit logs by action
   */
  findByAction(action: string, params?: Omit<AuditLogQueryParams, 'action'>): Promise<PaginatedResult<AuditLogWithUser>>;

  /**
   * Find audit logs within date range
   */
  findByDateRange(startDate: Date, endDate: Date, params?: Omit<AuditLogQueryParams, 'dateFrom' | 'dateTo'>): Promise<PaginatedResult<AuditLogWithUser>>;

  /**
   * Update audit log (mainly for soft deletes)
   */
  update(id: string, data: UpdateAuditLogData): Promise<AuditLog | null>;

  /**
   * Soft delete audit log
   */
  softDelete(id: string): Promise<boolean>;

  /**
   * Restore soft deleted audit log
   */
  restore(id: string): Promise<boolean>;

  /**
   * Hard delete audit log (permanent)
   */
  delete(id: string): Promise<boolean>;

  /**
   * Get audit log statistics
   */
  getStats(params?: Pick<AuditLogQueryParams, 'dateFrom' | 'dateTo' | 'userId' | 'resourceType'>): Promise<AuditLogStats>;

  /**
   * Get filter options for dropdowns
   */
  getFilterOptions(): Promise<AuditLogFilterOptions>;

  /**
   * Count audit logs by criteria
   */
  count(params?: Omit<AuditLogQueryParams, 'page' | 'limit' | 'sortBy' | 'sortOrder'>): Promise<number>;

  /**
   * Check if audit log exists
   */
  exists(id: string): Promise<boolean>;

  /**
   * Bulk create audit logs
   */
  bulkCreate(data: CreateAuditLogData[]): Promise<AuditLog[]>;

  /**
   * Clean up old audit logs (for maintenance)
   */
  cleanupOldLogs(olderThanDays: number): Promise<number>;
}
