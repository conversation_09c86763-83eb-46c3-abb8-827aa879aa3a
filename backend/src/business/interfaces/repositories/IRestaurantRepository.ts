// Repository interface abstraction for database-agnostic restaurant operations
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RestaurantQueryParams {
  isActive?: boolean;
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  minRating?: number;
  maxPrice?: number;
  cuisine?: string;
}

export interface CreateRestaurantData {
  name: string;
  address: string;
  phone?: string;
  description?: string;
  isActive?: boolean;
}

export interface UpdateRestaurantData {
  name?: string;
  address?: string;
  phone?: string;
  description?: string;
  isActive?: boolean;
}

// Database-agnostic restaurant repository interface
export interface IRestaurantRepository {
  // Query operations
  findAll(query: RestaurantQueryParams): Promise<PaginatedResult<Restaurant>>;
  findById(id: number): Promise<Restaurant | null>;
  findByName(name: string): Promise<Restaurant | null>;
  findAllActive(): Promise<ActiveRestaurant[]>;
  
  // Command operations
  create(restaurantData: CreateRestaurantData): Promise<Restaurant>;
  update(id: number, data: Partial<UpdateRestaurantData>): Promise<Restaurant | null>;
  softDelete(id: number): Promise<boolean>;
  restore(id: number): Promise<boolean>;
  
  // Utility operations
  nameExists(name: string, excludeId?: number): Promise<boolean>;
}

// Domain models (separate from database entities)
export interface Restaurant {
  restaurantId: number;
  name: string;
  address: string;
  phone: string | null;
  description: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface ActiveRestaurant {
  restaurantId: number;
  name: string;
  address: string;
  isActive: boolean;
}
