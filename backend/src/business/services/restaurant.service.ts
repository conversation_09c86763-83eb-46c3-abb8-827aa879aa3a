import { restaurantRepository } from '../../data/repositories/restaurant.repository';
import { type CreateRestaurantRequest, type UpdateRestaurantRequest, type RestaurantQuery, type BulkRestaurantAction, type RestaurantStatus } from '../validators/restaurant.validator';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError, ConflictError, BadRequestError } from '../../infrastructure/errors';
import {
  IRestaurantRepository,
  type Restaurant,
  type ActiveRestaurant,
  type RestaurantQueryParams,
  type CreateRestaurantData,
  type UpdateRestaurantData,
  type PaginatedResult
} from '../../business/interfaces/repositories/IRestaurantRepository';

export class RestaurantService {
  constructor(private readonly restaurantRepo: IRestaurantRepository = restaurantRepository) { }

  async getAll(query: RestaurantQuery): Promise<PaginatedResult<Restaurant>> {
    type FilterType = 'rating' | 'price' | 'cuisine' | 'search' | 'status';
    
    const logContext = {
      query,
      appliedFilters: {
        hasAdvancedFilters: !!(query.minRating || query.maxPrice || query.cuisine),
        filterTypes: [] as FilterType[]
      }
    };

    // Track which filters are being used
    if (query.minRating) logContext.appliedFilters.filterTypes.push('rating');
    if (query.maxPrice) logContext.appliedFilters.filterTypes.push('price');
    if (query.cuisine) logContext.appliedFilters.filterTypes.push('cuisine');
    if (query.search) logContext.appliedFilters.filterTypes.push('search');
    if (query.isActive !== undefined) logContext.appliedFilters.filterTypes.push('status');

    apiLogger.info('Fetching restaurants with filters', logContext);

    try {
      // Validate query parameters
      this.validateQueryParams(query);

      // Convert RestaurantQuery to RestaurantQueryParams
      const queryParams: RestaurantQueryParams = {
        ...(query.isActive !== undefined && { isActive: query.isActive }),
        ...(query.search && { search: query.search }),
        ...(query.sortBy && { sortBy: query.sortBy as 'name' | 'createdAt' | 'updatedAt' }),
        ...(query.sortOrder && { sortOrder: query.sortOrder }),
        ...(query.page && { page: query.page }),
        ...(query.limit && { limit: query.limit }),
        ...(query.minRating && { minRating: query.minRating }),
        ...(query.maxPrice && { maxPrice: query.maxPrice }),
        ...(query.cuisine && { cuisine: query.cuisine }),
      };

      const result = await this.restaurantRepo.findAll(queryParams);
      
      apiLogger.info('Successfully retrieved restaurants', {
        totalRecords: result.pagination.total,
        appliedFilters: logContext.appliedFilters
      });
      
      return result;
    } catch (error) {
      apiLogger.error('Error fetching restaurants:', {
        error,
        query: logContext.query,
        appliedFilters: logContext.appliedFilters
      });
      throw new AppError('Failed to retrieve restaurants.');
    }
  }

  private validateQueryParams(query: RestaurantQuery): void {
    // Validate pagination parameters
    if (query.page && query.page < 1) {
      throw new BadRequestError('Page must be greater than 0');
    }
    if (query.limit && (query.limit < 1 || query.limit > 100)) {
      throw new BadRequestError('Limit must be between 1 and 100');
    }

    // Validate advanced filter parameters
    if (query.minRating !== undefined) {
      if (query.minRating < 0 || query.minRating > 5) {
        throw new BadRequestError('Rating must be between 0 and 5');
      }
    }

    if (query.maxPrice !== undefined) {
      if (query.maxPrice <= 0) {
        throw new BadRequestError('Maximum price must be greater than 0');
      }
    }

    if (query.cuisine !== undefined) {
      if (query.cuisine.trim().length === 0) {
        throw new BadRequestError('Cuisine cannot be empty');
      }
      if (query.cuisine.length > 100) {
        throw new BadRequestError('Cuisine name is too long');
      }
    }

    // Validate sort parameters
    if (query.sortBy && !['name', 'createdAt', 'updatedAt'].includes(query.sortBy)) {
      throw new BadRequestError('Invalid sort field');
    }
    if (query.sortOrder && !['asc', 'desc'].includes(query.sortOrder)) {
      throw new BadRequestError('Sort order must be either "asc" or "desc"');
    }
  }

  async getById(id: number): Promise<Restaurant> {
    apiLogger.info('Fetching restaurant by ID', { restaurantId: id });
    const restaurant = await this.restaurantRepo.findById(id);
    if (!restaurant) {
      throw new NotFoundError('Restaurant not found');
    }
    return restaurant;
  }

  async create(restaurantData: CreateRestaurantRequest): Promise<Restaurant> {
    apiLogger.info('Creating new restaurant', { name: restaurantData.name });

    const existingRestaurant = await this.restaurantRepo.findByName(restaurantData.name);
    if (existingRestaurant) {
      throw new ConflictError('Restaurant with this name already exists');
    }

    try {
      // Convert CreateRestaurantRequest to CreateRestaurantData
      const createData: CreateRestaurantData = {
        name: restaurantData.name,
        address: restaurantData.address,
        phone: restaurantData.phoneNumber,
        ...(restaurantData.description && { description: restaurantData.description }),
        isActive: restaurantData.isActive,
      };

      const restaurant = await this.restaurantRepo.create(createData);
      apiLogger.info('Restaurant created successfully', { restaurantId: restaurant.restaurantId });
      return restaurant;
    } catch (error) {
      apiLogger.error('Error creating restaurant:', error);
      throw new AppError('Failed to create restaurant.');
    }
  }

  async update(id: number, updateData: UpdateRestaurantRequest): Promise<Restaurant> {
    apiLogger.info('Updating restaurant', { restaurantId: id, updateData });

    const existingRestaurant = await this.restaurantRepo.findById(id);
    if (!existingRestaurant) {
      throw new NotFoundError('Restaurant not found');
    }

    if (updateData.name && updateData.name !== existingRestaurant.name) {
      const nameExists = await this.restaurantRepo.nameExists(updateData.name, id);
      if (nameExists) {
        throw new ConflictError('Restaurant with this name already exists');
      }
    }

    // Convert UpdateRestaurantRequest to UpdateRestaurantData
    const updateDataConverted: Partial<UpdateRestaurantData> = {
      ...(updateData.name && { name: updateData.name }),
      ...(updateData.address && { address: updateData.address }),
      ...(updateData.phoneNumber && { phone: updateData.phoneNumber }),
      ...(updateData.description !== undefined && { description: updateData.description }),
      ...(updateData.isActive !== undefined && { isActive: updateData.isActive }),
    };

    const updatedRestaurant = await this.restaurantRepo.update(id, updateDataConverted);
    if (!updatedRestaurant) {
      throw new AppError('Failed to update restaurant.');
    }

    apiLogger.info('Restaurant updated successfully', { restaurantId: id });
    return updatedRestaurant;
  }

  async delete(id: number): Promise<void> {
    apiLogger.info('Soft deleting restaurant', { restaurantId: id });
    const success = await this.restaurantRepo.softDelete(id);
    if (!success) {
      throw new NotFoundError('Restaurant not found or already deleted.');
    }
    apiLogger.info('Restaurant soft deleted successfully', { restaurantId: id });
  }

  async restore(id: number): Promise<void> {
    apiLogger.info('Restoring restaurant', { restaurantId: id });
    const success = await this.restaurantRepo.restore(id);
    if (!success) {
      throw new NotFoundError('Restaurant not found or not deleted.');
    }
    apiLogger.info('Restaurant restored successfully', { restaurantId: id });
  }

  async updateStatus(id: number, status: RestaurantStatus): Promise<Restaurant> {
    apiLogger.info('Updating restaurant status', { restaurantId: id, status });
    const updatedRestaurant = await this.restaurantRepo.update(id, { isActive: status.isActive });
    if (!updatedRestaurant) {
      throw new NotFoundError('Restaurant not found');
    }
    apiLogger.info('Restaurant status updated successfully', { restaurantId: id, isActive: status.isActive });
    return updatedRestaurant;
  }

  async getActive(): Promise<ActiveRestaurant[]> {
    apiLogger.info('Fetching active restaurants');
    try {
      return await this.restaurantRepo.findAllActive();
    } catch (error) {
      apiLogger.error('Error fetching active restaurants:', error);
      throw new AppError('Failed to retrieve active restaurants.');
    }
  }

  async bulkAction(action: BulkRestaurantAction['action'], restaurantIds: number[]): Promise<any> {
    apiLogger.info('Performing bulk action on restaurants', { action, restaurantIds });
    let successCount = 0;
    const results = [];

    for (const restaurantId of restaurantIds) {
      try {
        let success = false;
        switch (action) {
          case 'activate':
            success = !!(await this.restaurantRepo.update(restaurantId, { isActive: true }));
            break;
          case 'deactivate':
            success = !!(await this.restaurantRepo.update(restaurantId, { isActive: false }));
            break;
          case 'delete':
            success = await this.restaurantRepo.softDelete(restaurantId);
            break;
        }
        if (success) successCount++;
        results.push({ restaurantId, success, error: null });
      } catch (error) {
        results.push({
          restaurantId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    apiLogger.info('Bulk action completed', { action, total: restaurantIds.length, successful: successCount });
    return {
      message: `Bulk ${action} completed. ${successCount}/${restaurantIds.length} operations successful.`,
      results,
    };
  }
}

export const restaurantService = new RestaurantService();