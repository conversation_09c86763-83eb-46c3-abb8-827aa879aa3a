import { salesRepository } from '../../data/repositories/sales.repository';
import { type CreateSalesRequest, type UpdateSalesRequest, type SalesQuery, type SalesReport, type SalesAnalytics } from '../validators/sales.validator';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError, ConflictError, BadRequestError } from '../../infrastructure/errors';
import {
  ISalesRepository,
  type Sales,
  type SalesQueryParams,
  type CreateSalesData,
  type UpdateSalesData,
  type SalesSummary,
  type PaginatedResult
} from '../../business/interfaces/repositories/ISalesRepository';

export class SalesService {
  constructor(private readonly salesRepo: ISalesRepository = salesRepository) { }

  async getAll(query: SalesQuery): Promise<PaginatedResult<Sales>> {
    apiLogger.info('Fetching sales records', { query });

    // Validate query parameters
    this.validateQueryParams(query);

    // Convert SalesQuery to SalesQueryParams
    const queryParams: SalesQueryParams = {
      ...(query.restaurantId && { restaurantId: query.restaurantId }),
      ...(query.recordedBy && { recordedBy: query.recordedBy }),
      ...(query.startDate && { startDate: query.startDate }),
      ...(query.endDate && { endDate: query.endDate }),
      ...(query.sortBy && { sortBy: query.sortBy as 'salesDate' | 'totalSales' | 'recordedAt' }),
      ...(query.sortOrder && { sortOrder: query.sortOrder }),
      ...(query.page && { page: query.page }),
      ...(query.limit && { limit: query.limit }),
    };

    try {
      const result = await this.salesRepo.findAll(queryParams);
      return result;
    } catch (error) {
      apiLogger.error('Error fetching sales:', error);
      throw new AppError('Failed to retrieve sales records.');
    }
  }

  private validateQueryParams(query: SalesQuery): void {
    if (query.page && query.page < 1) {
      throw new BadRequestError('Page must be greater than 0');
    }
    if (query.limit && (query.limit < 1 || query.limit > 100)) {
      throw new BadRequestError('Limit must be between 1 and 100');
    }
    if (query.startDate && query.endDate && new Date(query.startDate) > new Date(query.endDate)) {
      throw new BadRequestError('Start date must be before end date');
    }
  }

  async getById(id: number): Promise<Sales> {
    const salesRecord = await this.salesRepo.findById(id);
    if (!salesRecord) {
      throw new NotFoundError('Sales record not found');
    }
    return salesRecord;
  }

  async create(salesData: CreateSalesRequest, userId: number): Promise<Sales> {
    const existingSales = await this.salesRepo.findDuplicateEntry(
      salesData.restaurantId,
      salesData.salesDate
    );
    if (existingSales) {
      throw new ConflictError('Sales record for this restaurant and date already exists');
    }

    // Convert CreateSalesRequest to CreateSalesData
    const salesWithUser: CreateSalesData = {
      restaurantId: salesData.restaurantId,
      salesDate: new Date(salesData.salesDate),
      totalSales: salesData.totalSales.toFixed(2),
      cashAmount: salesData.cashAmount.toFixed(2),
      cardAmount: salesData.cardAmount.toFixed(2),
      onlinePaymentAmount: salesData.onlinePaymentAmount.toFixed(2),
      totalDiscounts: salesData.totalDiscounts.toFixed(2),
      ...(salesData.notes && { notes: salesData.notes }),
      recordedByUserId: userId,
    };

    try {
      const salesRecord = await this.salesRepo.create(salesWithUser);
      apiLogger.info('Sales record created successfully', { salesId: salesRecord.salesId });
      return salesRecord;
    } catch (error) {
      apiLogger.error('Error creating sales record:', error);
      throw new AppError('Failed to create sales record.');
    }
  }

  async update(id: number, updateData: UpdateSalesRequest): Promise<Sales> {
    await this.getById(id); // Ensure record exists

    // Convert UpdateSalesRequest to UpdateSalesData
    const payload: Partial<UpdateSalesData> = {};
    if (updateData.salesDate) payload.salesDate = new Date(updateData.salesDate);
    if (updateData.cashAmount) payload.cashAmount = updateData.cashAmount.toFixed(2);
    if (updateData.cardAmount) payload.cardAmount = updateData.cardAmount.toFixed(2);
    if (updateData.onlinePaymentAmount) payload.onlinePaymentAmount = updateData.onlinePaymentAmount.toFixed(2);
    if (updateData.totalDiscounts) payload.totalDiscounts = updateData.totalDiscounts.toFixed(2);
    if (updateData.notes !== undefined) payload.notes = updateData.notes;

    // Recalculate total sales if any payment amounts are updated
    if (updateData.cashAmount !== undefined || updateData.cardAmount !== undefined || updateData.onlinePaymentAmount !== undefined) {
      const existingRecord = await this.getById(id);
      const cash = updateData.cashAmount ?? parseFloat(existingRecord.cashAmount);
      const card = updateData.cardAmount ?? parseFloat(existingRecord.cardAmount);
      const online = updateData.onlinePaymentAmount ?? parseFloat(existingRecord.onlinePaymentAmount);
      payload.totalSales = (cash + card + online).toFixed(2);
    }

    const updatedSales = await this.salesRepo.update(id, payload);
    if (!updatedSales) {
      throw new AppError('Failed to update sales record.');
    }
    apiLogger.info('Sales record updated successfully', { salesId: id });
    return updatedSales;
  }

  async delete(id: number): Promise<void> {
    const success = await this.salesRepo.softDelete(id);
    if (!success) {
      throw new NotFoundError('Sales record not found or already deleted.');
    }
    apiLogger.info('Sales record soft deleted successfully', { salesId: id });
  }

  async restore(id: number): Promise<void> {
    const success = await this.salesRepo.restore(id);
    if (!success) {
      throw new NotFoundError('Sales record not found or not deleted.');
    }
    apiLogger.info('Sales record restored successfully', { salesId: id });
  }

  async getSummary(startDate: string, endDate: string, restaurantId?: number): Promise<SalesSummary> {
    try {
      return await this.salesRepo.getSalesSummary(startDate, endDate, restaurantId);
    } catch (error) {
      apiLogger.error('Error fetching sales summary:', error);
      throw new AppError('Failed to retrieve sales summary.');
    }
  }

  async findDuplicate(restaurantId: number, salesDate: string): Promise<Sales | null> {
    return this.salesRepo.findDuplicateEntry(restaurantId, salesDate);
  }

  async getReport(reportParams: SalesReport): Promise<any> {
    apiLogger.info('Generating sales report', reportParams);

    const salesData = await this.salesRepo.findByDateRange(
      reportParams.startDate,
      reportParams.endDate,
      reportParams.restaurantId
    );

    const summary = await this.salesRepo.getSalesSummary(
      reportParams.startDate,
      reportParams.endDate,
      reportParams.restaurantId
    );

    const groupedData = this.groupSalesData(salesData, reportParams.groupBy);

    return {
      summary,
      groupedData,
      details: reportParams.includeDetails ? salesData : undefined,
      period: {
        startDate: reportParams.startDate,
        endDate: reportParams.endDate,
        groupBy: reportParams.groupBy,
      },
    };
  }

  async getAnalytics(analyticsParams: SalesAnalytics): Promise<any> {
    apiLogger.info('Fetching sales analytics', analyticsParams);

    const { startDate, endDate } = this.calculateDateRange(analyticsParams);

    const currentPeriodSummary = await this.salesRepo.getSalesSummary(
      startDate,
      endDate,
      analyticsParams.restaurantId
    );

    let comparisonData = null;
    if (analyticsParams.compareWith) {
      const { startDate: compStartDate, endDate: compEndDate } = this.calculateComparisonDateRange(
        analyticsParams,
        startDate,
        endDate
      );

      comparisonData = await this.salesRepo.getSalesSummary(
        compStartDate,
        compEndDate,
        analyticsParams.restaurantId
      );
    }

    return {
      currentPeriod: currentPeriodSummary,
      comparisonPeriod: comparisonData,
      period: analyticsParams.period,
      dateRange: { startDate, endDate },
    };
  }

  async bulkAction(action: 'delete' | 'export', salesIds: number[]): Promise<{ message: string; results: any[] }> {
    apiLogger.info('Performing bulk action on sales', { action, salesIds });

    let results = [];
    let successCount = 0;

    for (const salesId of salesIds) {
      try {
        let success = false;
        switch (action) {
          case 'delete':
            success = await this.salesRepo.softDelete(salesId);
            break;
          case 'export':
            // Export functionality would be implemented here
            success = true; // Placeholder
            break;
        }
        if (success) successCount++;
        results.push({ salesId, success, error: null });
      } catch (error) {
        results.push({
          salesId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    apiLogger.info('Bulk action completed', { action, total: salesIds.length, successful: successCount });
    return {
      message: `Bulk ${action} completed. ${successCount}/${salesIds.length} operations successful.`,
      results,
    };
  }

  private groupSalesData(salesData: any[], groupBy: 'day' | 'week' | 'month') {
    const grouped: Record<string, any[]> = {};

    salesData.forEach(sale => {
      let key: string;
      const date = new Date(sale.salesDate);

      switch (groupBy) {
        case 'day':
          key = sale.salesDate;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().substring(0, 10); // Use substring for robustness
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        default:
          key = sale.salesDate;
      }

      if (!grouped[key]) {
        grouped[key] = [];
      }
      (grouped[key] as any[]).push(sale); // Explicitly cast for TypeScript
    });

    return grouped;
  }

  private calculateDateRange(params: SalesAnalytics): { startDate: string; endDate: string } {
    const today = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (params.period) {
      case 'today':
        startDate = today;
        endDate = today;
        break;
      case 'yesterday':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(today);
        endDate.setDate(today.getDate() - 1);
        break;
      case 'week':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - today.getDay());
        endDate = new Date(today);
        break;
      case 'month':
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case 'quarter':
        const quarter = Math.floor(today.getMonth() / 3);
        startDate = new Date(today.getFullYear(), quarter * 3, 1);
        endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
        break;
      case 'year':
        startDate = new Date(today.getFullYear(), 0, 1);
        endDate = new Date(today.getFullYear(), 11, 31);
        break;
      case 'custom':
        if (!params.startDate || !params.endDate) {
          throw new BadRequestError('Start date and end date are required for custom period');
        }
        startDate = new Date(params.startDate);
        endDate = new Date(params.endDate);
        break;
      default:
        throw new BadRequestError('Invalid period specified');
    }

    const startString = !isNaN(startDate.getTime()) ? (startDate.toISOString().split('T')[0] as string) : '';
    const endString = !isNaN(endDate.getTime()) ? (endDate.toISOString().split('T')[0] as string) : '';

    return {
      startDate: startString,
      endDate: endString,
    };
  }

  private calculateComparisonDateRange(
    analyticsParams: SalesAnalytics,
    currentStartDate: string,
    currentEndDate: string
  ): { startDate: string; endDate: string } {
    const start = new Date(currentStartDate);
    const end = new Date(currentEndDate);
    let compStartDate: Date;
    let compEndDate: Date;

    if (analyticsParams.compareWith === 'previous_period') {
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end day

      compStartDate = new Date(start);
      compStartDate.setDate(start.getDate() - diffDays);
      compEndDate = new Date(end);
      compEndDate.setDate(end.getDate() - diffDays);
    } else if (analyticsParams.compareWith === 'previous_year') {
      compStartDate = new Date(start);
      compStartDate.setFullYear(start.getFullYear() - 1);
      compEndDate = new Date(end);
      compEndDate.setFullYear(end.getFullYear() - 1);
    } else {
      throw new BadRequestError('Invalid comparison period specified');
    }

    const compStartString = !isNaN(compStartDate.getTime()) ? (compStartDate.toISOString().split('T')[0] as string) : '';
    const compEndString = !isNaN(compEndDate.getTime()) ? (compEndDate.toISOString().split('T')[0] as string) : '';

    return {
      startDate: compStartString,
      endDate: compEndString,
    };
  }
}

export const salesService = new SalesService();