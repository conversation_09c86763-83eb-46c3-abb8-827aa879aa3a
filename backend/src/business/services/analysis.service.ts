import NodeCache from 'node-cache';
import { salesRepository } from '../../data/repositories/sales.repository';
import { restaurantRepository } from '../../data/repositories/restaurant.repository';
import { apiLogger } from '../../infrastructure/logger/pino';
import { ISalesRepository } from '../interfaces/repositories/ISalesRepository';
import { IRestaurantRepository, Restaurant } from '../interfaces/repositories/IRestaurantRepository';
import { AppError, NotFoundError } from '../../infrastructure/errors';
import { config } from '../../infrastructure/config';
import { AnalyticsQuery, KpiProgressQuery, BranchAnalyticsQuery } from '../validators/analysis.validator';
import { formatCurrency, getMonthName, getYear, getChangeDirection } from '../../shared/utils/format-utils';
import { calculateDateRange } from '../../shared/utils/date-utils';

// Consolidated and improved response interfaces
export interface MonthlyRevenue {
  month: string;
  monthName: string;
  year: number;
  totalRevenue: number;
  formattedRevenue: string;
  changePercentage: number | null;
  changeDirection: 'up' | 'down' | 'neutral' | null;
}

export interface BranchAnalytics {
  restaurantId: number;
  restaurantName: string;
  totalRevenue: number;
  formattedTotalRevenue: string;
  monthlyRevenue: MonthlyRevenue[];
  revenueStats: {
    averageMonthlyRevenue: number;
    highestMonth: { month: string; revenue: number } | null;
    lowestMonth: { month: string; revenue: number } | null;
    totalMonths: number;
  };
}

export interface KpiProgressItem {
  month: string;
  monthName: string;
  year: number;
  achievedAmount: number;
  formattedAchievedAmount: string;
  targetAmount: number;
  formattedTargetAmount: string;
  difference: number;
  formattedDifference: string;
  differencePercentage: number;
  formattedDifferencePercentage: string;
  status: 'above_target' | 'below_target' | 'on_target';
  progressPercentage: number;
}

export interface KpiProgress {
  restaurantId: number;
  restaurantName: string;
  kpiProgress: KpiProgressItem[];
  summary: {
    totalMonths: number;
    monthsAboveTarget: number;
    monthsBelowTarget: number;
    averageAchievement: number;
    overallProgressPercentage: number;
  };
  monthlyTarget: number;
  formattedMonthlyTarget: string;
}

export interface OverallRevenueData {
  overview: {
    actualRevenue: number;
    forecastRevenue: number;
    achievementPercentage: number;
  };
  chartData: Array<{ month: string; actual: number; forecast: number }>;
  performanceData: Array<{
    restaurant: string;
    forecast: number;
    target: number;
    actual: number;
    risk: 'low' | 'medium' | 'high';
    achievement: number;
    trend: 'up' | 'down' | 'neutral';
  }>;
}

export class AnalysisService {
  private readonly cache: NodeCache;
  private readonly cacheTtl: number;
  private readonly analysisConfig = {
    monthlyTarget: 25000,
    currency: { symbol: 'RM', locale: 'en-MY' },
    cache: { ttl: 600, checkperiod: 120 },
    dateRange: { defaultStart: '2000-01-01' },
    forecastBaseMultiplier: 1.2,
  };

  constructor(
    private readonly salesRepo: ISalesRepository = salesRepository,
    private readonly restaurantRepo: IRestaurantRepository = restaurantRepository,
  ) {
    this.cache = new NodeCache({
      stdTTL: this.analysisConfig.cache.ttl,
      checkperiod: this.analysisConfig.cache.checkperiod,
    });
    this.cacheTtl = config.nodeEnv === 'development' ? 30 : this.analysisConfig.cache.ttl;
  }

  public async getDashboardAnalytics(query: AnalyticsQuery): Promise<OverallRevenueData> {
    const cacheKey = `dashboard_analytics_${query.period}_${query.includeForecasts}`;
    const cachedData = this.cache.get<OverallRevenueData>(cacheKey);
    if (cachedData) {
      apiLogger.info('✅ [Cache] Returning cached dashboard analytics.');
      return cachedData;
    }

    apiLogger.info('⏳ [Analytics] Fetching fresh dashboard analytics...');
    try {
      const data = await this.generateOverallRevenueData(query);
      this.cache.set(cacheKey, data, this.cacheTtl);
      apiLogger.info('✅ [Analytics] Successfully fetched and cached dashboard analytics.');
      return data;
    } catch (error) {
      apiLogger.error('❌ [Analytics] Error fetching dashboard analytics:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve dashboard analytics due to an internal error.', 500);
    }
  }

  public async getBranchAnalytics(query: BranchAnalyticsQuery): Promise<BranchAnalytics> {
    const { restaurantId } = query;
    apiLogger.info(`Fetching branch analytics for restaurant ${restaurantId}`);

    const restaurant = await this.restaurantRepo.findById(restaurantId);
    if (!restaurant) {
      throw new NotFoundError('Restaurant not found');
    }

    const sales = await this.salesRepo.findByDateRange(
      this.analysisConfig.dateRange.defaultStart,
      new Date().toISOString().split('T')[0]!,
      restaurantId,
    );

    const totalRevenue = sales.reduce((acc, sale) => acc + parseFloat(sale.totalSales), 0);
    const monthlyData = this.groupSalesByMonth(sales);
    const monthlyRevenue = this.calculateMonthlyRevenue(monthlyData);
    const revenueStats = this.calculateRevenueStats(monthlyRevenue);

    return {
      restaurantId,
      restaurantName: restaurant.name,
      totalRevenue,
      formattedTotalRevenue: formatCurrency(totalRevenue, this.analysisConfig.currency),
      monthlyRevenue,
      revenueStats,
    };
  }

  public async getKpiProgress(query: KpiProgressQuery): Promise<KpiProgress> {
    const { restaurantId } = query;
    apiLogger.info(`Fetching KPI progress for restaurant ${restaurantId}`);

    const restaurant = await this.restaurantRepo.findById(restaurantId);
    if (!restaurant) {
      throw new NotFoundError(`Restaurant with ID ${restaurantId} not found.`);
    }

    const sales = await this.salesRepo.findByDateRange(
      this.analysisConfig.dateRange.defaultStart,
      new Date().toISOString().split('T')[0]!,
      restaurantId,
    );

    const monthlyData = this.groupSalesByMonth(sales);
    const kpiProgress = this.calculateKpiProgress(monthlyData);
    const summary = this.summarizeKpiProgress(kpiProgress);

    return {
      restaurantId,
      restaurantName: restaurant.name,
      kpiProgress,
      summary,
      monthlyTarget: this.analysisConfig.monthlyTarget,
      formattedMonthlyTarget: formatCurrency(this.analysisConfig.monthlyTarget, this.analysisConfig.currency),
    };
  }

  private async generateOverallRevenueData(query: AnalyticsQuery): Promise<OverallRevenueData> {
    const { startDate, endDate } = calculateDateRange(query.period);
    const restaurants = (await this.restaurantRepo.findAll({ isActive: true, limit: 1000 })).data;
    const overallSummary = await this.salesRepo.getSalesSummary(startDate, endDate);
    const totalActualRevenue = parseFloat(overallSummary.totalSales);
    const totalForecastRevenue = totalActualRevenue * this.analysisConfig.forecastBaseMultiplier;

    const overview = {
      actualRevenue: totalActualRevenue,
      forecastRevenue: totalForecastRevenue,
      achievementPercentage: totalForecastRevenue > 0 ? Math.round((totalActualRevenue / totalForecastRevenue) * 100) : 0,
    };

    const chartData = await this.generateChartData(startDate, endDate, restaurants);
    const performanceData = await this.generatePerformanceData(restaurants, startDate, endDate);

    return { overview, chartData, performanceData };
  }



  private async generateChartData(startDate: string, endDate: string, restaurants: Restaurant[]): Promise<OverallRevenueData['chartData']> {
    const sales = await this.salesRepo.findByDateRange(startDate, endDate);
    const totalMonthlyForecast = restaurants.reduce((total, r) => total + this.getMonthlyTargetForRestaurant(r.name), 0);
    const monthlyRevenue = this.groupSalesByMonth(sales);

    const chartData: OverallRevenueData['chartData'] = [];
    const currentMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1);
    const endMonth = new Date();

    while (currentMonth <= endMonth) {
      const monthKey = currentMonth.toISOString().substring(0, 7);
      chartData.push({
        month: monthKey,
        actual: parseFloat((monthlyRevenue[monthKey] || 0).toFixed(2)),
        forecast: parseFloat(totalMonthlyForecast.toFixed(2)),
      });
      currentMonth.setMonth(currentMonth.getMonth() + 1);
    }
    return chartData;
  }

  private async generatePerformanceData(restaurants: Restaurant[], startDate: string, endDate: string): Promise<OverallRevenueData['performanceData']> {
    const performanceData: OverallRevenueData['performanceData'] = [];
    for (const restaurant of restaurants) {
      const sales = await this.salesRepo.findByDateRange(startDate, endDate, restaurant.restaurantId);
      const totalActual = sales.reduce((acc, sale) => acc + parseFloat(sale.totalSales), 0);
      const forecast = this.calculateForecast(totalActual, restaurant.name);
      const target = forecast * 0.85;
      const achievementPercentage = forecast > 0 ? (totalActual / forecast) * 100 : 0;
      const risk = this.calculateRiskLevel(achievementPercentage);

      const monthlyData = this.groupSalesByMonth(sales);
      const months = Object.keys(monthlyData).sort();
      const currentMonthRevenue = months.length > 0 ? monthlyData[months[months.length - 1]!]! : 0;
      const previousMonthRevenue = months.length > 1 ? monthlyData[months[months.length - 2]!]! : 0;
      const trend = this.calculateTrend(currentMonthRevenue, previousMonthRevenue);
      const monthlyTarget = this.getMonthlyTargetForRestaurant(restaurant.name);
      const achievement = monthlyTarget > 0 ? Math.min(Math.round((currentMonthRevenue / monthlyTarget) * 100), 100) : 0;

      performanceData.push({
        restaurant: restaurant.name,
        forecast: parseFloat(forecast.toFixed(2)),
        target: parseFloat(target.toFixed(2)),
        actual: parseFloat(totalActual.toFixed(2)),
        risk,
        achievement,
        trend,
      });
    }
    return performanceData.sort((a, b) => b.actual - a.actual);
  }

  private groupSalesByMonth(sales: any[]): { [key: string]: number } {
    return sales.reduce((acc, sale) => {
      const month = sale.salesDate.substring(0, 7);
      acc[month] = (acc[month] || 0) + parseFloat(sale.totalSales);
      return acc;
    }, {} as { [key: string]: number });
  }

  private calculateMonthlyRevenue(monthlyData: { [key: string]: number }): MonthlyRevenue[] {
    const sortedMonths = Object.keys(monthlyData).sort();
    let lastMonthRevenue: number | null = null;
    return sortedMonths.map(month => {
      const revenue = monthlyData[month]!;
      let changePercentage: number | null = null;
      if (lastMonthRevenue !== null && lastMonthRevenue !== 0) {
        changePercentage = ((revenue - lastMonthRevenue) / lastMonthRevenue) * 100;
      }
      lastMonthRevenue = revenue;
      return {
        month,
        monthName: getMonthName(month),
        year: getYear(month),
        totalRevenue: revenue,
        formattedRevenue: formatCurrency(revenue, this.analysisConfig.currency),
        changePercentage: changePercentage ? parseFloat(changePercentage.toFixed(2)) : null,
        changeDirection: getChangeDirection(changePercentage),
      };
    });
  }

  private calculateRevenueStats(monthlyRevenue: MonthlyRevenue[]) {
    const revenues = monthlyRevenue.map(m => m.totalRevenue);
    const averageMonthlyRevenue = revenues.length > 0 ? revenues.reduce((a, b) => a + b, 0) / revenues.length : 0;
    const highestMonth = monthlyRevenue.length > 0 ? monthlyRevenue.reduce((max, current) => (current.totalRevenue > max.totalRevenue ? current : max)) : null;
    const lowestMonth = monthlyRevenue.length > 0 ? monthlyRevenue.reduce((min, current) => (current.totalRevenue < min.totalRevenue ? current : min)) : null;
    return {
      averageMonthlyRevenue: parseFloat(averageMonthlyRevenue.toFixed(2)),
      highestMonth: highestMonth ? { month: highestMonth.month, revenue: highestMonth.totalRevenue } : null,
      lowestMonth: lowestMonth ? { month: lowestMonth.month, revenue: lowestMonth.totalRevenue } : null,
      totalMonths: monthlyRevenue.length,
    };
  }

  private calculateKpiProgress(monthlyData: { [key: string]: number }): KpiProgressItem[] {
    return Object.keys(monthlyData).sort().map(month => {
      const achievedAmount = monthlyData[month]!;
      const target = this.analysisConfig.monthlyTarget;
      const difference = achievedAmount - target;
      const differencePercentage = target ? (difference / target) * 100 : 0;
      const progressPercentage = target ? (achievedAmount / target) * 100 : 0;
      let status: 'above_target' | 'below_target' | 'on_target' = 'below_target';
      if (achievedAmount > target) status = 'above_target';
      else if (achievedAmount === target) status = 'on_target';
      return {
        month,
        monthName: getMonthName(month),
        year: getYear(month),
        achievedAmount,
        formattedAchievedAmount: formatCurrency(achievedAmount, this.analysisConfig.currency),
        targetAmount: target,
        formattedTargetAmount: formatCurrency(target, this.analysisConfig.currency),
        difference,
        formattedDifference: formatCurrency(Math.abs(difference), this.analysisConfig.currency),
        differencePercentage: parseFloat(differencePercentage.toFixed(2)),
        formattedDifferencePercentage: `${differencePercentage.toFixed(2)}%`,
        status,
        progressPercentage: parseFloat(progressPercentage.toFixed(2)),
      };
    });
  }

  private summarizeKpiProgress(kpiProgress: KpiProgressItem[]) {
    const totalMonths = kpiProgress.length;
    const monthsAboveTarget = kpiProgress.filter(kpi => kpi.status === 'above_target').length;
    const monthsBelowTarget = kpiProgress.filter(kpi => kpi.status === 'below_target').length;
    const averageAchievement = totalMonths > 0 ? kpiProgress.reduce((sum, kpi) => sum + kpi.achievedAmount, 0) / totalMonths : 0;
    const overallProgressPercentage = this.analysisConfig.monthlyTarget ? (averageAchievement / this.analysisConfig.monthlyTarget) * 100 : 0;
    return {
      totalMonths,
      monthsAboveTarget,
      monthsBelowTarget,
      averageAchievement: parseFloat(averageAchievement.toFixed(2)),
      overallProgressPercentage: parseFloat(overallProgressPercentage.toFixed(2)),
    };
  }

  private getMonthlyTargetForRestaurant(restaurantName: string): number {
    if (restaurantName.includes('Express')) return this.analysisConfig.monthlyTarget * 0.8;
    if (restaurantName.includes('Downtown')) return this.analysisConfig.monthlyTarget * 1.2;
    return this.analysisConfig.monthlyTarget;
  }

  private calculateForecast(actualRevenue: number, restaurantName: string): number {
    let multiplier = this.analysisConfig.forecastBaseMultiplier;
    if (restaurantName.includes('Express')) multiplier = 1.15;
    else if (restaurantName.includes('Downtown')) multiplier = 1.25;
    else if (restaurantName.includes('Uptown')) multiplier = 1.22;
    return actualRevenue * multiplier;
  }

  private calculateRiskLevel(achievementPercentage: number): 'low' | 'medium' | 'high' {
    if (achievementPercentage >= 85) return 'low';
    if (achievementPercentage >= 70) return 'medium';
    return 'high';
  }

  private calculateTrend(currentMonth: number, previousMonth: number): 'up' | 'down' | 'neutral' {
    const changePercentage = previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0;
    if (changePercentage > 5) return 'up';
    if (changePercentage < -5) return 'down';
    return 'neutral';
  }
}

export const analysisService = new AnalysisService();