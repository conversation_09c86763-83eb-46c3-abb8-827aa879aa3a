import { auditLogRepository } from '../../data/repositories/audit-log.repository';
import { apiLogger } from '../../infrastructure/logger/pino';
import { NotFoundError, BadRequestError } from '../../infrastructure/errors';
import { filterUndefined } from '../../shared/utils/object-utils';
import {
  IAuditLogRepository,
  type PaginatedResult,
} from '../interfaces/repositories/IAuditLogRepository';
import {
  type AuditLog,
  type AuditLogWithUser,
  type CreateAuditLogData,
  type AuditLogQueryParams,
  type AuditLogStats,
  type AuditLogFilterOptions,
  AUDIT_ACTIONS,
  RESOURCE_TYPES,
} from '../../data/models/audit-log.model';

export class AuditLogService {
  constructor(private readonly auditLogRepo: IAuditLogRepository = auditLogRepository) { }

  /**
   * Create a new audit log entry
   */
  async createAuditLog(data: CreateAuditLogData): Promise<AuditLog> {
    try {
      apiLogger.info('Creating audit log', { action: data.action, userId: data.userId });

      // Validate required fields
      if (!data.action) {
        throw new BadRequestError('Action is required');
      }

      const auditLog = await this.auditLogRepo.create(data);

      apiLogger.info('Audit log created successfully', { id: auditLog.id, action: data.action });
      return auditLog;
    } catch (error) {
      apiLogger.error('Error creating audit log:', error);
      throw error;
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id: string): Promise<AuditLogWithUser> {
    try {
      apiLogger.info('Fetching audit log by ID', { id });

      const auditLog = await this.auditLogRepo.findByIdWithUser(id);
      if (!auditLog) {
        throw new NotFoundError(`Audit log with ID ${id} not found`);
      }

      return auditLog;
    } catch (error) {
      apiLogger.error('Error fetching audit log by ID:', error);
      throw error;
    }
  }

  /**
   * Get all audit logs with filtering, sorting, and pagination
   */
  async getAllAuditLogs(params: AuditLogQueryParams = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    try {
      apiLogger.info('Fetching audit logs', params);

      // Set default values
      const queryParams = {
        page: 1,
        limit: 50,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
        includeDeleted: false,
        ...params,
      };

      // Validate pagination parameters
      if (queryParams.page !== undefined && queryParams.page < 1) {
        throw new BadRequestError('Page must be greater than 0');
      }

      if (queryParams.limit !== undefined && (queryParams.limit < 1 || queryParams.limit > 1000)) {
        throw new BadRequestError('Limit must be between 1 and 1000');
      }

      const result = await this.auditLogRepo.findAll(queryParams);

      apiLogger.info('Audit logs fetched successfully', {
        count: result.data.length,
        total: result.pagination.total
      });

      return result;
    } catch (error) {
      apiLogger.error('Error fetching audit logs:', error);
      throw error;
    }
  }

  /**
   * Get audit logs for a specific user
   */
  async getUserAuditLogs(userId: number, params: Omit<AuditLogQueryParams, 'userId'> = {}): Promise<PaginatedResult<AuditLogWithUser>> {
    try {
      apiLogger.info('Fetching user audit logs', { userId, ...params });

      if (userId <= 0) {
        throw new BadRequestError('Invalid user ID');
      }

      return this.auditLogRepo.findByUserId(userId, params);
    } catch (error) {
      apiLogger.error('Error fetching user audit logs:', error);
      throw error;
    }
  }

  /**
   * Get audit logs for a specific resource
   */
  async getResourceAuditLogs(
    resourceType: string,
    resourceId: string,
    params: Omit<AuditLogQueryParams, 'resourceType' | 'resourceId'> = {}
  ): Promise<PaginatedResult<AuditLogWithUser>> {
    try {
      apiLogger.info('Fetching resource audit logs', { resourceType, resourceId, ...params });

      if (!resourceType || !resourceId) {
        throw new BadRequestError('Resource type and ID are required');
      }

      return this.auditLogRepo.findByResource(resourceType, resourceId, params);
    } catch (error) {
      apiLogger.error('Error fetching resource audit logs:', error);
      throw error;
    }
  }

  /**
   * Get audit log statistics
   */
  async getAuditLogStats(params: Pick<AuditLogQueryParams, 'dateFrom' | 'dateTo' | 'userId' | 'resourceType'> = {}): Promise<AuditLogStats> {
    try {
      apiLogger.info('Fetching audit log statistics', params);

      const stats = await this.auditLogRepo.getStats(params);

      apiLogger.info('Audit log statistics fetched successfully', { totalLogs: stats.totalLogs });
      return stats;
    } catch (error) {
      apiLogger.error('Error fetching audit log statistics:', error);
      throw error;
    }
  }

  /**
   * Get filter options for dropdowns
   */
  async getFilterOptions(): Promise<AuditLogFilterOptions> {
    try {
      apiLogger.info('Fetching audit log filter options');

      const options = await this.auditLogRepo.getFilterOptions();

      apiLogger.info('Filter options fetched successfully', {
        actionsCount: options.actions.length,
        usersCount: options.users.length,
        resourceTypesCount: options.resourceTypes.length,
      });

      return options;
    } catch (error) {
      apiLogger.error('Error fetching filter options:', error);
      throw error;
    }
  }

  /**
   * Soft delete audit log
   */
  async deleteAuditLog(id: string): Promise<boolean> {
    try {
      apiLogger.info('Soft deleting audit log', { id });

      const exists = await this.auditLogRepo.exists(id);
      if (!exists) {
        throw new NotFoundError(`Audit log with ID ${id} not found`);
      }

      const result = await this.auditLogRepo.softDelete(id);

      if (result) {
        apiLogger.info('Audit log soft deleted successfully', { id });
      }

      return result;
    } catch (error) {
      apiLogger.error('Error soft deleting audit log:', error);
      throw error;
    }
  }

  /**
   * Restore soft deleted audit log
   */
  async restoreAuditLog(id: string): Promise<boolean> {
    try {
      apiLogger.info('Restoring audit log', { id });

      const exists = await this.auditLogRepo.exists(id);
      if (!exists) {
        throw new NotFoundError(`Audit log with ID ${id} not found`);
      }

      const result = await this.auditLogRepo.restore(id);

      if (result) {
        apiLogger.info('Audit log restored successfully', { id });
      }

      return result;
    } catch (error) {
      apiLogger.error('Error restoring audit log:', error);
      throw error;
    }
  }

  /**
   * Bulk create audit logs
   */
  async bulkCreateAuditLogs(data: CreateAuditLogData[]): Promise<AuditLog[]> {
    try {
      apiLogger.info('Bulk creating audit logs', { count: data.length });

      if (data.length === 0) {
        throw new BadRequestError('No audit log data provided');
      }

      if (data.length > 1000) {
        throw new BadRequestError('Cannot create more than 1000 audit logs at once');
      }

      // Validate each entry
      for (const entry of data) {
        if (!entry.action) {
          throw new BadRequestError('All audit log entries must have an action');
        }
      }

      const auditLogs = await this.auditLogRepo.bulkCreate(data);

      apiLogger.info('Audit logs bulk created successfully', { count: auditLogs.length });
      return auditLogs;
    } catch (error) {
      apiLogger.error('Error bulk creating audit logs:', error);
      throw error;
    }
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldAuditLogs(olderThanDays: number): Promise<number> {
    try {
      apiLogger.info('Cleaning up old audit logs', { olderThanDays });

      if (olderThanDays < 30) {
        throw new BadRequestError('Cannot delete audit logs newer than 30 days');
      }

      const deletedCount = await this.auditLogRepo.cleanupOldLogs(olderThanDays);

      apiLogger.info('Old audit logs cleaned up successfully', { deletedCount, olderThanDays });
      return deletedCount;
    } catch (error) {
      apiLogger.error('Error cleaning up old audit logs:', error);
      throw error;
    }
  }

  /**
   * Helper method to log user actions
   */
  async logUserAction(
    userId: number | null,
    action: string,
    resourceType?: string,
    resourceId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createAuditLog(filterUndefined({
      userId,
      action,
      resourceType,
      resourceId,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
    }) as CreateAuditLogData);
  }

  /**
   * Get available audit actions
   */
  getAvailableActions() {
    return Object.values(AUDIT_ACTIONS);
  }

  /**
   * Get available resource types
   */
  getAvailableResourceTypes() {
    return Object.values(RESOURCE_TYPES);
  }
}

// Export singleton instance
export const auditLogService = new AuditLogService();
