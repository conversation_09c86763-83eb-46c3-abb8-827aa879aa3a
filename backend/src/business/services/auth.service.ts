import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { config } from '../../infrastructure/config';
import { userRepository } from '../../data/repositories/user.repository';
import {
  IUserRepository,
  type SafeUser,
  type CreateUserData,
} from '../../business/interfaces/repositories/IUserRepository';
import { type RegisterRequest, type LoginRequest, type JwtPayload } from '../validators/auth.validator';
import { authLogger } from '../../infrastructure/logger/pino';

export class AuthService {
  private readonly saltRounds = 12;

  constructor(private readonly userRepo: IUserRepository = userRepository) { }

  async register(userData: RegisterRequest): Promise<{ user: SafeUser; token: string }> {
    try {
      authLogger.info('Attempting to register user', { username: userData.username });

      // Check if user already exists
      const existingUser = await this.userRepo.findByUsername(userData.username);
      if (existingUser) {
        throw new Error('User with this username already exists');
      }

      // Hash password
      const hashedPassword = await this.hashPassword(userData.password);

      // Create user
      const createData: CreateUserData = {
        username: userData.username,
        email: userData.email,
        password: hashedPassword,
        fullName: userData.fullName,
        role: userData.role,
        ...(userData.restaurantId && { restaurantId: userData.restaurantId }),
      };

      const user = await this.userRepo.create(createData);

      // Generate JWT token
      const token = this.generateToken({
        userId: user.userId,
        username: user.username,
        email: user.email,
        role: user.role as 'admin' | 'staff' | 'user',
        restaurantId: user.restaurantId ?? undefined, // Convert null to undefined
      });

      authLogger.info('User registered successfully', { userId: user.userId });
      return { user, token };
    } catch (error) {
      authLogger.error('Registration failed:', error);
      throw error;
    }
  }

  async login(credentials: LoginRequest): Promise<{ user: SafeUser; token: string }> {
    try {
      authLogger.info('Attempting to login user', { email: credentials.email });

      // Find user by email
      const user = await this.userRepo.findByEmail(credentials.email);
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Verify password
      const isPasswordValid = await this.verifyPassword(credentials.password, user.password);
      if (!isPasswordValid) {
        throw new Error('Invalid username or password');
      }

      // Generate JWT token
      const token = this.generateToken({
        userId: user.userId,
        username: user.username,
        email: user.email,
        role: user.role as 'admin' | 'staff' | 'user',
        restaurantId: user.restaurantId || undefined,
      });

      const safeUser = await this.userRepo.findSafeById(user.userId);
      if (!safeUser) {
        throw new Error('User not found');
      }

      authLogger.info('User logged in successfully', { userId: user.userId });
      return { user: safeUser, token };
    } catch (error) {
      authLogger.error('Login failed:', error);
      throw error;
    }
  }

  async verifyToken(token: string): Promise<JwtPayload> {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        algorithms: ['HS256'], // Ensure algorithm is specified for verification
      }) as JwtPayload;
      return decoded;
    } catch (error) {
      authLogger.error('Token verification failed:', error);
      throw new Error('Invalid or expired token');
    }
  }

  public async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  public async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  private generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    return jwt.sign(
      {
        userId: payload.userId,
        username: payload.username,
        email: payload.email,
        role: payload.role,
        restaurantId: payload.restaurantId,
      },
      config.jwt.secret as jwt.Secret, // Explicitly cast to jwt.Secret
      {
        expiresIn: config.jwt.expiresIn,
      } as jwt.SignOptions, // Explicitly cast to SignOptions
    );
  }
}

export const authService = new AuthService();
