# WhatsApp Integration Service

## 1. Overview

This document provides a comprehensive overview of the WhatsApp integration service, built using the WhiskeySockets/Baileys library. It is designed to be a lightweight, modern, and robust service for real-time communication, using the recommended pairing code authentication method.

### 1.1. Architecture

The service is built around a single, consolidated `whatsapp.service.ts` file that manages the connection, session, and authentication lifecycle. This simplified design enhances maintainability and clarity.

```mermaid
graph TD
    subgraph "WhatsApp Service Module"
        A[whatsapp.service.ts]
        B[scheduler.ts] -- uses --> A
        C[utils.ts] -- used by --> B
    end

    subgraph "Application"
        D(Other Services) -- can use --> A
    end

    subgraph "Infrastructure"
        E[pino-logger] --> A;
        F[config] --> A;
    end

    subgraph "External"
        G(Baileys Library) <--> A;
        H(WhatsApp Servers) <--> G;
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style C fill:#ccf,stroke:#333,stroke-width:2px
```

## 2. Key Components

### 2.1. `whatsapp.service.ts`

-   **Responsibility:** The core of the integration. It manages the `WASocket` instance, handles the full connection lifecycle (including authentication and reconnection), and exposes functions for sending messages.
-   **Key Features:**
    -   **Pairing Code Authentication:** Uses the modern pairing code method for a secure and user-friendly first-time setup.
    -   **Session Management:** Leverages `useMultiFileAuthState` for robust and persistent session handling.
    -   **Connection Management:** Implements automatic reconnection logic using `DisconnectReason` to handle unexpected disconnections.
    -   **Event Handling:** Listens for connection updates, credential changes, and incoming messages within a single, cohesive service.

### 2.2. `scheduler.ts`

-   **Responsibility:** Schedules and sends the daily sales summary to a pre-configured recipient.
-   **Key Features:**
    -   **`node-schedule`:** Uses a reliable scheduling library to run jobs at specific times.
    -   **Dynamic Content:** Fetches the latest sales data and formats it before sending via the `whatsapp.service`.

### 2.3. `utils.ts`

- **Responsibility:** Provides helper functions for formatting message content.
- **Key Features:**
  - **`formatDailySalesSummary`:** Creates a human-readable summary of daily sales data.

## 3. Configuration

All configuration is managed through environment variables. Create a `.env` file in the root `broku-sales-dashboard` directory.

| Variable | Description | Default | Required |
| --- | --- | --- | --- |
| `WHATSAPP_ENABLED` | Enables or disables the WhatsApp service. | `false` | Yes |
| `WHATSAPP_SESSION_DIR` | The directory to store session files. | `./whatsapp_session` | Yes |
| `WHATSAPP_RECIPIENT_NUMBER` | The phone number to send daily summaries to. | `""` (empty) | For scheduler |
| `WHATSAPP_PAIRING_CODE_PHONE_NUMBER` | The phone number to use for pairing code auth. | `""` (empty) | For first auth |

## 4. Setup and Installation

1.  **Install Dependencies:**
    ```bash
    npm install
    ```

2.  **Configure Environment:**
    -   Copy `.env.example` to `.env` in the root directory and fill in the required variables.
    -   Set `WHATSAPP_ENABLED=true`.
    -   Provide your WhatsApp phone number (including country code, without "+") for `WHATSAPP_PAIRING_CODE_PHONE_NUMBER`.

3.  **Run the Application:**
    ```bash
    npm run dev
    ```

4.  **Authenticate:**
    -   On the first run, the service will attempt to connect using the pairing code method.
    -   The service will log the pairing code to the console (e.g., `Your WhatsApp pairing code is: XXXX-XXXX`).
    -   On your phone, go to **WhatsApp > Linked Devices > Link a device > Link with phone number instead**.
    -   Enter the code shown in the console to complete the authentication.
    -   The session will be saved in the `WHATSAPP_SESSION_DIR` directory.

## 5. Usage

### Sending a message

To send a message, import and use the `sendMessage` function from the WhatsApp service.

```typescript
import { sendMessage } from 'business/services/whatsapp';

async function sendWelcomeMessage(recipient: string) {
  try {
    await sendMessage(recipient, { text: 'Welcome to our service!' });
    console.log('Welcome message sent successfully.');
  } catch (error) {
    console.error('Failed to send welcome message:', error);
  }
}
```

## 6. Security Best Practices

-   **Session Persistence:** Session files are managed by Baileys' `useMultiFileAuthState`, which ensures secure storage.
-   **Error Handling:** The service uses `@hapi/boom` for structured error handling, ensuring that issues are logged with detailed context.
-   **Reconnection Logic:** The client will only attempt to reconnect if the disconnection was not due to being logged out, preventing persistent reconnection loops on authentication failure.

## 7. Troubleshooting

-   **"Connection closed. You are logged out" error:**
    -   **Cause:** The session has been invalidated (e.g., you logged out from the linked device).
    -   **Solution:** Delete the `WHATSAPP_SESSION_DIR` and re-authenticate by following the pairing code steps.
-   **Failed to request pairing code:**
    -   **Cause:** The `WHATSAPP_PAIRING_CODE_PHONE_NUMBER` may be incorrect or missing.
    -   **Solution:** Ensure the phone number is correctly set in your `.env` file.
-   **Messages not sending:**
    -   **Cause:** The WhatsApp socket may not be connected.
    -   **Solution:** Check the application logs for connection errors. Ensure `WHATSAPP_ENABLED` is `true`.

## 8. Production Deployment

-   **Session Directory:** Ensure the `WHATSAPP_SESSION_DIR` is a persistent volume so the session is not lost on container restarts.
-   **Logging:** Set `LOG_LEVEL` to `info` or `warn` in production to reduce log noise while still capturing important events.
-   **Monitoring:** Monitor the application logs for connection status updates and errors.