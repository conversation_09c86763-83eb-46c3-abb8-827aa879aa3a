import { apiLogger } from 'infrastructure/logger/pino';
import { config } from 'infrastructure/config';
import fs from 'fs/promises';
import path from 'path';

export interface WhatsAppDebugInfo {
  timestamp: string;
  sessionExists: boolean;
  sessionFiles: string[];
  configStatus: {
    enabled: boolean;
    sessionDir: string;
    recipientNumber?: string;
  };
  systemInfo: {
    nodeVersion: string;
    platform: string;
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
  };
  baileys: {
    version: string;
    userAgent: string[];
  };
}

export interface ErrorAnalysis {
  errorType: 'PAIRING_FAILED' | 'CONNECTION_FAILED' | 'WEBSOCKET_ERROR' | 'AUTH_ERROR' | 'NETWORK_ERROR' | 'UNKNOWN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  possibleCauses: string[];
  recommendedActions: string[];
  debugSteps: string[];
}

/**
 * Collect comprehensive debug information
 */
export async function collectDebugInfo(): Promise<WhatsAppDebugInfo> {
  const sessionDir = config.whatsapp.sessionDir;
  let sessionFiles: string[] = [];
  let sessionExists = false;

  try {
    const files = await fs.readdir(sessionDir);
    sessionFiles = files;
    sessionExists = files.length > 0;
  } catch (error) {
    apiLogger.warn({ error, sessionDir }, 'Could not read session directory');
  }

  return {
    timestamp: new Date().toISOString(),
    sessionExists,
    sessionFiles,
    configStatus: {
      enabled: config.whatsapp.enabled,
      sessionDir: config.whatsapp.sessionDir,
      recipientNumber: config.whatsapp.recipientNumber,
    },
    systemInfo: {
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    },
    baileys: {
      version: '6.7.5', // From package.json
      userAgent: ['Broku Sales Dashboard', 'Chrome', '1.0.0'],
    },
  };
}

/**
 * Analyze error and provide debugging guidance
 */
export function analyzeError(error: Error | string, context?: any): ErrorAnalysis {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorStack = typeof error === 'string' ? undefined : error.stack;

  // Pairing-related errors
  if (errorMessage.includes('pairing') || errorMessage.includes('code')) {
    if (errorMessage.includes('timeout')) {
      return {
        errorType: 'PAIRING_FAILED',
        severity: 'HIGH',
        possibleCauses: [
          'Network connectivity issues',
          'WhatsApp servers are overloaded',
          'Phone number format is incorrect',
          'Rate limiting by WhatsApp',
          'Firewall blocking outbound connections',
        ],
        recommendedActions: [
          'Verify internet connection',
          'Check phone number format (Malaysian: 60xxxxxxxxx)',
          'Wait 5-10 minutes before retrying',
          'Check firewall settings',
          'Try from a different network',
        ],
        debugSteps: [
          'Test network connectivity: curl -I https://web.whatsapp.com',
          'Verify phone number format',
          'Check server logs for rate limiting messages',
          'Monitor network traffic during pairing attempt',
        ],
      };
    }

    if (errorMessage.includes('already paired') || errorMessage.includes('registered')) {
      return {
        errorType: 'AUTH_ERROR',
        severity: 'MEDIUM',
        possibleCauses: [
          'Device is already registered with WhatsApp',
          'Session files exist from previous pairing',
          'Multiple pairing attempts without cleanup',
        ],
        recommendedActions: [
          'Clear session directory',
          'Use disconnect endpoint before re-pairing',
          'Check for existing session files',
        ],
        debugSteps: [
          'List files in session directory',
          'Check creds.json for registration status',
          'Clear session and retry pairing',
        ],
      };
    }

    return {
      errorType: 'PAIRING_FAILED',
      severity: 'HIGH',
      possibleCauses: [
        'Invalid phone number format',
        'Network connectivity issues',
        'WhatsApp API changes',
        'Session state corruption',
      ],
      recommendedActions: [
        'Validate phone number format',
        'Clear session directory',
        'Check network connectivity',
        'Update Baileys library',
      ],
      debugSteps: [
        'Validate phone number with regex',
        'Test basic network connectivity',
        'Check Baileys version compatibility',
      ],
    };
  }

  // Connection-related errors
  if (errorMessage.includes('connection') || errorMessage.includes('websocket') || errorMessage.includes('socket')) {
    if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
      return {
        errorType: 'CONNECTION_FAILED',
        severity: 'HIGH',
        possibleCauses: [
          'Network timeout',
          'Firewall blocking WebSocket connections',
          'WhatsApp servers unreachable',
          'DNS resolution issues',
        ],
        recommendedActions: [
          'Check internet connectivity',
          'Verify firewall allows WebSocket connections',
          'Try different DNS servers',
          'Check proxy settings',
        ],
        debugSteps: [
          'Test WebSocket connectivity to WhatsApp servers',
          'Check DNS resolution for web.whatsapp.com',
          'Monitor network latency',
          'Test from different network',
        ],
      };
    }

    if (errorMessage.includes('401') || errorMessage.includes('403')) {
      return {
        errorType: 'AUTH_ERROR',
        severity: 'CRITICAL',
        possibleCauses: [
          'Invalid authentication credentials',
          'Session expired or corrupted',
          'WhatsApp account banned or restricted',
          'Multiple device limit exceeded',
        ],
        recommendedActions: [
          'Re-pair the device',
          'Clear session directory',
          'Check WhatsApp account status',
          'Verify device limit not exceeded',
        ],
        debugSteps: [
          'Check session credentials validity',
          'Verify WhatsApp account status',
          'Test pairing with different phone number',
        ],
      };
    }

    return {
      errorType: 'CONNECTION_FAILED',
      severity: 'HIGH',
      possibleCauses: [
        'Network connectivity issues',
        'WebSocket connection problems',
        'Server-side issues',
        'Session state problems',
      ],
      recommendedActions: [
        'Check network connectivity',
        'Restart the service',
        'Clear session and re-pair',
        'Check server logs for more details',
      ],
      debugSteps: [
        'Test basic network connectivity',
        'Check WebSocket connection logs',
        'Monitor connection state changes',
      ],
    };
  }

  // WebSocket-specific errors
  if (errorMessage.includes('noise') || errorMessage.includes('frame') || errorMessage.includes('decode')) {
    return {
      errorType: 'WEBSOCKET_ERROR',
      severity: 'HIGH',
      possibleCauses: [
        'Protocol version mismatch',
        'Corrupted WebSocket frames',
        'Network packet loss',
        'Baileys library compatibility issues',
      ],
      recommendedActions: [
        'Update Baileys library to latest version',
        'Check network stability',
        'Clear session and re-establish connection',
        'Monitor for packet loss',
      ],
      debugSteps: [
        'Check Baileys library version',
        'Monitor WebSocket frame integrity',
        'Test network stability',
        'Check for library updates',
      ],
    };
  }

  // Network-related errors
  if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNRESET')) {
    return {
      errorType: 'NETWORK_ERROR',
      severity: 'HIGH',
      possibleCauses: [
        'DNS resolution failure',
        'Network connectivity issues',
        'Firewall blocking connections',
        'Proxy configuration problems',
      ],
      recommendedActions: [
        'Check DNS settings',
        'Verify internet connectivity',
        'Check firewall configuration',
        'Test without proxy',
      ],
      debugSteps: [
        'Test DNS resolution: nslookup web.whatsapp.com',
        'Test connectivity: ping web.whatsapp.com',
        'Check firewall logs',
        'Test with different DNS servers',
      ],
    };
  }

  // Default analysis for unknown errors
  return {
    errorType: 'UNKNOWN',
    severity: 'MEDIUM',
    possibleCauses: [
      'Unexpected error condition',
      'Library compatibility issues',
      'Configuration problems',
      'System resource constraints',
    ],
    recommendedActions: [
      'Check server logs for more details',
      'Verify system resources',
      'Update dependencies',
      'Contact support with error details',
    ],
    debugSteps: [
      'Enable debug logging',
      'Check system resources (CPU, memory)',
      'Verify all dependencies are up to date',
      'Reproduce error with minimal test case',
    ],
  };
}

/**
 * Generate debugging report
 */
export async function generateDebugReport(error?: Error | string, context?: any): Promise<string> {
  const debugInfo = await collectDebugInfo();
  const errorAnalysis = error ? analyzeError(error, context) : null;

  const report = `
# WhatsApp Integration Debug Report
Generated: ${debugInfo.timestamp}

## System Information
- Node.js Version: ${debugInfo.systemInfo.nodeVersion}
- Platform: ${debugInfo.systemInfo.platform}
- Uptime: ${Math.floor(debugInfo.systemInfo.uptime / 60)} minutes
- Memory Usage: ${Math.round(debugInfo.systemInfo.memoryUsage.rss / 1024 / 1024)} MB

## WhatsApp Configuration
- Service Enabled: ${debugInfo.configStatus.enabled}
- Session Directory: ${debugInfo.configStatus.sessionDir}
- Recipient Number: ${debugInfo.configStatus.recipientNumber || 'Not configured'}

## Session Status
- Session Exists: ${debugInfo.sessionExists}
- Session Files: ${debugInfo.sessionFiles.length > 0 ? debugInfo.sessionFiles.join(', ') : 'None'}

## Baileys Library
- Version: ${debugInfo.baileys.version}
- User Agent: ${debugInfo.baileys.userAgent.join(' / ')}

${errorAnalysis ? `
## Error Analysis
- Error Type: ${errorAnalysis.errorType}
- Severity: ${errorAnalysis.severity}

### Possible Causes
${errorAnalysis.possibleCauses.map(cause => `- ${cause}`).join('\n')}

### Recommended Actions
${errorAnalysis.recommendedActions.map(action => `- ${action}`).join('\n')}

### Debug Steps
${errorAnalysis.debugSteps.map(step => `- ${step}`).join('\n')}
` : ''}

## Next Steps
1. Review the error analysis above
2. Follow the recommended actions
3. Execute the debug steps to gather more information
4. Check server logs for additional context
5. If issues persist, contact support with this report

---
Report generated by Broku Sales Dashboard WhatsApp Integration
`;

  return report.trim();
}

/**
 * Log structured debug information
 */
export async function logDebugInfo(error?: Error | string, context?: any): Promise<void> {
  const debugInfo = await collectDebugInfo();
  const errorAnalysis = error ? analyzeError(error, context) : null;

  apiLogger.info({
    debugInfo,
    errorAnalysis,
    context,
  }, 'WhatsApp debug information collected');
}