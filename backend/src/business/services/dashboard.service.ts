import { format, subDays, subMonths } from 'date-fns';
import { userRepository } from '../../data/repositories/user.repository';
import { restaurantRepository } from '../../data/repositories/restaurant.repository';
import { salesRepository } from '../../data/repositories/sales.repository';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError } from '../../infrastructure/errors';
import { calculateDateRange } from '../../shared/utils/date-utils';
import {
  type UserDashboardQuery,
  type AdminDashboardQuery,
  type RecentActivityQuery,
  type SalesBreakdownQuery,
  type RecentActivityItem,
  type DashboardMetrics,
  type SalesTrendData,
  type RestaurantPerformance,
  type UserActivityData,
  type UserDashboardData,
} from '../validators/dashboard.validator';
import {
  type IUserRepository,
  type GlobalUserStats,
} from '../interfaces/repositories/IUserRepository';
import {
  type IRestaurantRepository,
} from '../interfaces/repositories/IRestaurantRepository';
import {
  type ISalesRepository,
  type SalesSummary,
} from '../interfaces/repositories/ISalesRepository';

export class DashboardService {
  constructor(
    private readonly userRepo: IUserRepository = userRepository,
    private readonly restaurantRepo: IRestaurantRepository = restaurantRepository,
    private readonly salesRepo: ISalesRepository = salesRepository
  ) { }

  async getAdminDashboardData(query: AdminDashboardQuery): Promise<{
    metrics: DashboardMetrics;
    salesTrend: SalesTrendData[];
    restaurantPerformance: RestaurantPerformance[];
    userActivity: UserActivityData[];
    recentActivity: RecentActivityItem[];
  }> {
    try {
      apiLogger.info('Fetching admin dashboard data', { query });

      const { startDate, endDate } = calculateDateRange(query.period);

      // Fetch all required data in parallel
      const [
        userStats,
        restaurantStats,
        salesSummary,
        salesTrend,
        restaurantPerformance,
        recentActivityData,
      ] = await Promise.all([
        this.getUserStats(),
        this.getRestaurantStats(),
        this.getSalesSummary(startDate, endDate),
        this.getSalesTrend(startDate, endDate),
        this.getRestaurantPerformance(startDate, endDate),
        this.getRecentActivity({ limit: query.limit, type: 'all' }),
      ]);

      const metrics: DashboardMetrics = {
        totalUsers: userStats.totalUsers,
        totalRestaurants: restaurantStats.totalRestaurants,
        totalSales: salesSummary.recordCount,
        totalRevenue: parseFloat(salesSummary.totalSales),
        activeUsers: userStats.activeUsers,
        inactiveUsers: userStats.inactiveUsers,
        newUsersThisMonth: userStats.newUsersThisMonth,
        recentSales: (await this.getRecentSales(5)).data,
        topRestaurants: await this.getTopRestaurants(5, startDate, endDate),
      };

      const userActivity: UserActivityData[] = [
        {
          name: 'Active Users',
          value: userStats.activeUsers,
          percentage: userStats.totalUsers > 0 ? Math.round((userStats.activeUsers / userStats.totalUsers) * 100) : 0,
        },
        {
          name: 'Inactive Users',
          value: userStats.inactiveUsers,
          percentage: userStats.totalUsers > 0 ? Math.round((userStats.inactiveUsers / userStats.totalUsers) * 100) : 0,
        },
      ];

      return {
        metrics,
        salesTrend,
        restaurantPerformance: restaurantPerformance.slice(0, 50), // Limit for performance
        userActivity,
        recentActivity: recentActivityData.data,
      };
    } catch (error) {
      apiLogger.error('Error fetching admin dashboard data:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve admin dashboard data', 500);
    }
  }

  async getUserDashboardData(userId: number, query: UserDashboardQuery): Promise<UserDashboardData> {
    try {
      apiLogger.info('Fetching user dashboard data', { userId, query });

      const { restaurantId } = query;

      const user = await this.userRepo.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      let startDate: string, endDate: string;
      try {
        // Check for both start and end date in customDateRange to avoid partial ranges
        if (query.customDateRange?.startDate && query.customDateRange?.endDate) {
          // Validate custom date range
          const start = new Date(query.customDateRange.startDate);
          const end = new Date(query.customDateRange.endDate);

          if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            throw new AppError('Invalid date format in custom date range', 400);
          }

          if (start >= end) {
            throw new AppError('Start date must be before end date', 400);
          }

          startDate = query.customDateRange.startDate;
          endDate = query.customDateRange.endDate;
          apiLogger.info('Using custom date range', { startDate, endDate });
        } else {
          // Ensure period is valid before calculating date range
          if (!query.period) {
            apiLogger.warn('No period provided, using default "month"');
            query.period = 'month';
          }

          const dateRange = calculateDateRange(query.period);
          startDate = dateRange.startDate;
          endDate = dateRange.endDate;
          apiLogger.info('Using period-based date range', { startDate, endDate, period: query.period });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        apiLogger.error('Invalid period provided for date range calculation', {
          period: query.period,
          error: errorMessage,
          customDateRange: query.customDateRange
        });

        if (error instanceof AppError) {
          throw error;
        }

        throw new AppError(`Invalid period specified for dashboard query: ${errorMessage}`, 400);
      }

      const targetRestaurantId = restaurantId || user.restaurantId;

      // If user has no restaurant association, return empty dashboard data
      if (!targetRestaurantId) {
        apiLogger.info('User has no restaurant association, returning empty dashboard data', { userId });
        return {
          todaySales: 0,
          weeklySales: 0,
          monthlySales: 0,
          salesTarget: 0,
          targetAchievement: 0,
          recentSales: [],
          performance: {
            thisWeek: 0,
            lastWeek: 0,
            change: 0,
            changeDirection: 'neutral' as const
          },
        };
      }

      // Wrap each sub-operation in Promise.all with error handling
      let userPerformance;
      try {
        userPerformance = await this.getUserPerformance(userId, targetRestaurantId);
      } catch (error) {
        userPerformance = {
          thisWeek: 0,
          lastWeek: 0,
          change: 0,
          changeDirection: 'neutral' as const
        };
        apiLogger.warn('Error retrieving user performance:', error);
      }

      let userSales;
      try {
        userSales = await this.getUserSalesData(userId, startDate, endDate, targetRestaurantId);
      } catch (error) {
        userSales = {
          todaySales: 0,
          weeklySales: 0,
          monthlySales: 0
        };
        apiLogger.warn('Error retrieving user sales data:', error);
      }

      let recentSalesData: Array<{
        id: number;
        amount: number;
        date: string;
        restaurantName: string;
      }> = [];
      try {
        const response = await this.getUserRecentSales(userId, 5);
        recentSalesData = response.data;
      } catch (error) {
        recentSalesData = [];
        apiLogger.warn('Error retrieving recent sales:', error);
      }

      let salesTarget;
      try {
        const response = await this.getUserSalesTarget(userId, targetRestaurantId);
        salesTarget = response.data.salesTarget;
      } catch (error) {
        salesTarget = 0;
        apiLogger.warn('Error retrieving sales target:', error);
      }

      const targetAchievement = salesTarget > 0 ? Math.round((userSales.monthlySales / salesTarget) * 100) : 0;

      return {
        todaySales: userSales.todaySales,
        weeklySales: userSales.weeklySales,
        monthlySales: userSales.monthlySales,
        salesTarget: salesTarget,
        targetAchievement: targetAchievement,
        recentSales: recentSalesData,
        performance: {
          ...userPerformance
        },
      };
    } catch (error) {
      apiLogger.error('Error fetching user dashboard data:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve user dashboard data. Some dashboard elements may be incomplete.', 500);
    }
  }

  async getUserSalesTrend(userId: number, query: UserDashboardQuery): Promise<SalesTrendData[]> {
    try {
      apiLogger.info('Fetching user sales trend data', { userId, query });

      const user = await this.userRepo.findById(userId);
      if (!user || !user.restaurantId) {
        throw new NotFoundError('User or associated restaurant not found');
      }

      let startDate: string, endDate: string;
      try {
        const dateRange = calculateDateRange(query.period);
        startDate = dateRange.startDate;
        endDate = dateRange.endDate;
      } catch (error) {
        apiLogger.error('Invalid period provided for sales trend calculation', { period: query.period, error });
        throw new AppError('Invalid period specified for sales trend query', 400);
      }

      const sales = await this.salesRepo.findByDateRange(startDate, endDate, user.restaurantId);

      const salesByDate = sales.reduce((acc, sale) => {
        const date = format(new Date(sale.salesDate), 'yyyy-MM-dd');
        if (!acc[date]) {
          acc[date] = 0;
        }
        acc[date] += parseFloat(sale.totalSales);
        return acc;
      }, {} as Record<string, number>);

      return Object.entries(salesByDate)
        .map(([date, value]) => ({
          name: format(new Date(date), 'MMM dd'),
          value,
          date: new Date(date).toISOString(),
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    } catch (error) {
      apiLogger.error('Error fetching user sales trend data:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve user sales trend data', 500);
    }
  }

  async getUserSalesBreakdown(userId: number, query: SalesBreakdownQuery): Promise<Array<{
    name: string;
    value: number;
    rawValue: number;
  }>> {
    try {
      apiLogger.info('Fetching user sales breakdown data', { userId, query });

      // Validate input parameters
      if (!userId || userId <= 0) {
        throw new AppError('Invalid user ID provided', 400);
      }

      if (!query.breakdownType) {
        apiLogger.warn('No breakdown type provided, using default "payment"');
        query.breakdownType = 'payment';
      }

      const user = await this.userRepo.findById(userId);
      if (!user) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }

      if (!user.restaurantId) {
        apiLogger.info('User has no restaurant association, returning empty breakdown data', { userId });
        return [];
      }

      let startDate: string, endDate: string;
      try {
        const dateRange = calculateDateRange(query.period);
        startDate = dateRange.startDate;
        endDate = dateRange.endDate;
      } catch (error) {
        apiLogger.error('Invalid period provided for sales breakdown calculation', { period: query.period, error });
        throw new AppError('Invalid period specified for sales breakdown query', 400);
      }

      const sales = await this.salesRepo.findByDateRange(startDate, endDate, user.restaurantId);

      if (sales.length === 0) {
        apiLogger.info('No sales data found for the specified period', {
          userId,
          restaurantId: user.restaurantId,
          startDate,
          endDate,
          breakdownType: query.breakdownType
        });
        return [];
      }

      apiLogger.debug('Retrieved sales data for breakdown', {
        salesCount: sales.length,
        breakdownType: query.breakdownType
      });

      let breakdown: Record<string, number> = {};

      try {
        switch (query.breakdownType) {
          case 'payment':
            breakdown = this.calculatePaymentBreakdown(sales);
            break;
          case 'product':
            breakdown = this.calculateProductBreakdown(sales, query.productCategory);
            break;
          case 'category':
            breakdown = this.calculateCategoryBreakdown(sales, query.productCategory);
            break;
          case 'time':
            breakdown = this.calculateTimeBreakdown(sales);
            break;
          case 'location':
            breakdown = this.calculateLocationBreakdown(sales);
            break;
          default:
            apiLogger.warn('Unsupported breakdown type, defaulting to payment', {
              breakdownType: query.breakdownType
            });
            breakdown = this.calculatePaymentBreakdown(sales);
        }
      } catch (breakdownError) {
        apiLogger.error('Error calculating breakdown', {
          breakdownType: query.breakdownType,
          error: breakdownError
        });
        throw new AppError(`Failed to calculate ${query.breakdownType} breakdown`, 500);
      }

      // Validate breakdown results
      if (Object.keys(breakdown).length === 0) {
        apiLogger.warn('No breakdown data calculated', {
          breakdownType: query.breakdownType,
          salesCount: sales.length
        });
        return [];
      }

      const totalSales = Object.values(breakdown).reduce((sum, current) => sum + current, 0);

      if (totalSales <= 0) {
        apiLogger.warn('Total sales is zero or negative', {
          totalSales,
          breakdownType: query.breakdownType
        });
        return [];
      }

      const result = Object.entries(breakdown)
        .map(([name, rawValue]) => ({
          name,
          rawValue: Math.round(rawValue * 100) / 100, // Round to 2 decimal places
          value: totalSales > 0 ? Math.round((rawValue / totalSales) * 100) : 0,
        }))
        .filter(item => item.rawValue > 0) // Filter out zero values
        .sort((a, b) => b.value - a.value);

      apiLogger.info('Sales breakdown calculated successfully', {
        breakdownType: query.breakdownType,
        itemCount: result.length,
        totalSales
      });

      return result;
    } catch (error) {
      apiLogger.error('Error fetching user sales breakdown data:', error);
      if (error instanceof AppError) throw error;
      throw new AppError('Failed to retrieve user sales breakdown data', 500);
    }
  }

  private async getUserStats(): Promise<GlobalUserStats> {
    try {
      return await this.userRepo.getGlobalStats();
    } catch (error) {
      apiLogger.error('Error fetching user statistics:', error);
      throw new AppError('Failed to retrieve user statistics', 500);
    }
  }

  private async getRestaurantStats(): Promise<{ totalRestaurants: number; activeRestaurants: number }> {
    try {
      const restaurants = await this.restaurantRepo.findAll({ limit: 1000 });

      // Adjust for proper result handling (accessing .data on PaginatedResult)
      const activeRestaurants = restaurants.data.filter(r => r.isActive).length;

      return {
        totalRestaurants: restaurants.data.length,
        activeRestaurants,
      };
    } catch (error) {
      apiLogger.error('Error fetching restaurant statistics:', error);
      throw new AppError('Failed to retrieve restaurant statistics', 500);
    }
  }

  private async getSalesSummary(startDate: string, endDate: string): Promise<SalesSummary> {
    return await this.salesRepo.getSalesSummary(startDate, endDate);
  }

  private async getSalesTrend(startDate: string, endDate: string): Promise<SalesTrendData[]> {
    const sales = await this.salesRepo.findByDateRange(startDate, endDate);

    // Group sales by date and calculate daily totals
    const salesByDate = sales.reduce((acc, sale) => {
      const date = format(new Date(sale.salesDate), 'yyyy-MM-dd');
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date] += parseFloat(sale.totalSales);
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(salesByDate)
      .map(([date, value]) => ({
        name: format(new Date(date), 'MMM dd'),
        value,
        date: new Date(date).toISOString(),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  private async getRestaurantPerformance(startDate: string, endDate: string): Promise<RestaurantPerformance[]> {
    // Assuming findAll returns PaginatedResult with .data array
    const restaurantsData = await this.restaurantRepo.findAll({ isActive: true });

    const performance: RestaurantPerformance[] = [];

    for (const restaurant of restaurantsData.data) {
      const sales = await this.salesRepo.findByDateRange(startDate, endDate, restaurant.restaurantId);
      const totalRevenue = sales.reduce((acc, sale) => acc + parseFloat(sale.totalSales), 0);
      const salesCount = sales.length;

      performance.push({
        name: restaurant.name,
        value: totalRevenue,
        restaurantId: restaurant.restaurantId,
        salesCount,
        averageSale: salesCount > 0 ? totalRevenue / salesCount : 0,
      });
    }

    return performance.sort((a, b) => b.value - a.value);
  }

  private async getRecentActivity({ limit, type }: RecentActivityQuery): Promise<{ data: RecentActivityItem[] }> {
    // This is a simplified implementation - in a real system, you'd have an activity log table
    const activities: RecentActivityItem[] = [];

    // Filter by type if specified
    if (type === 'all' || type === 'sale') {
      // Get recent sales
      const recentSalesData = await this.salesRepo.findAll({
        limit: type === 'sale' ? limit : Math.floor(limit / 2),
        restaurantId: null,
        sortBy: 'recordedAt',
        sortOrder: 'desc',
      });

      for (const sale of recentSalesData.data) {
        const restaurant = await this.restaurantRepo.findById(sale.restaurantId);
        activities.push({
          id: `sale-${sale.salesId}`,
          type: 'sale',
          message: `Sale of $${parseFloat(sale.totalSales).toFixed(2)} recorded at ${restaurant?.name || 'Unknown Restaurant'}`,
          timestamp: sale.recordedAt.toISOString(),
          amount: parseFloat(sale.totalSales),
          restaurantId: sale.restaurantId,
          restaurantName: restaurant?.name,
        });
      }
    }

    if (type === 'all' || type === 'user') {
      // Get recent users (simplified - would need a proper activity log)
      const recentUsersData = await this.userRepo.findAll({
        limit: type === 'user' ? limit : Math.floor(limit / 2),
      });

      for (const user of recentUsersData.data) {
        if (new Date(user.createdAt) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) { // Corrected filtering
          activities.push({
            id: `user-${user.userId}`,
            type: 'user',
            message: `New user ${user.fullName} (${user.role}) created`,
            timestamp: user.createdAt.toISOString(),
            user: user.fullName,
          });
        }
      }
    }

    if (type === 'all' || type === 'restaurant') {
      // Get recent restaurants
      const recentRestaurantsData = await this.restaurantRepo.findAll({
        limit: type === 'restaurant' ? limit : Math.floor(limit / 3),
      });

      for (const restaurant of recentRestaurantsData.data) {
        activities.push({
          id: `restaurant-${restaurant.restaurantId}`,
          type: 'restaurant',
          message: `New restaurant "${restaurant.name}" added`,
          timestamp: restaurant.createdAt.toISOString(),
          restaurantId: restaurant.restaurantId,
          restaurantName: restaurant.name,
        });
      }
    }

    return {
      data: activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, limit),
    };
  }

  private async getRecentSales(limit: number): Promise<{
    data: Array<{
      id: number,
      amount: number,
      date: string,
      restaurantName: string,
    }>
  }> {
    const salesData = await this.salesRepo.findAll({
      limit,
      sortBy: 'recordedAt',
      sortOrder: 'desc',
    });

    const recentSales = [];

    for (const sale of salesData.data) {
      const restaurant = await this.restaurantRepo.findById(sale.restaurantId);
      recentSales.push({
        id: sale.salesId,
        amount: parseFloat(sale.totalSales),
        date: format(new Date(sale.salesDate), 'yyyy-MM-dd'),
        restaurantName: restaurant?.name || 'Unknown Restaurant',
      });
    }

    return { data: recentSales };
  }

  private async getTopRestaurants(limit: number, startDate: string, endDate: string): Promise<Array<{
    id: number,
    name: string,
    totalRevenue: number,
    salesCount: number,
  }>> {
    const restaurantsData = await this.restaurantRepo.findAll({ isActive: true });
    const topRestaurants = [];

    for (const restaurant of restaurantsData.data) {
      const sales = await this.salesRepo.findByDateRange(startDate, endDate, restaurant.restaurantId);
      const totalRevenue = sales.reduce((acc, sale) => acc + parseFloat(sale.totalSales), 0);

      topRestaurants.push({
        id: restaurant.restaurantId,
        name: restaurant.name,
        totalRevenue,
        salesCount: sales.length,
      });
    }

    return topRestaurants
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, limit);
  }

  private async getUserSalesData(userId: number, startDate: string, endDate: string, restaurantId?: number | null): Promise<{
    todaySales: number,
    weeklySales: number,
    monthlySales: number,
  }> {
    try {
      // If no restaurant ID, return zero values
      if (!restaurantId) {
        return {
          todaySales: 0,
          weeklySales: 0,
          monthlySales: 0,
        };
      }

      const now = new Date();
      const today = format(now, 'yyyy-MM-dd');
      const weekAgo = format(subDays(now, 7), 'yyyy-MM-dd');
      const monthAgo = format(subMonths(now, 1), 'yyyy-MM-dd');

      // Get sales for different periods
      const [todaySales, weeklySales, monthlySales] = await Promise.all([
        this.salesRepo.findByDateRange(today, today, restaurantId),
        this.salesRepo.findByDateRange(weekAgo, endDate, restaurantId),
        this.salesRepo.findByDateRange(monthAgo, endDate, restaurantId),
      ]);

      // Safely calculate totals with validation
      const calculateTotal = (sales: any[]) => {
        return sales.reduce((acc, sale) => {
          const amount = parseFloat(sale.totalSales);
          return acc + (isNaN(amount) ? 0 : amount);
        }, 0);
      };

      return {
        todaySales: calculateTotal(todaySales),
        weeklySales: calculateTotal(weeklySales),
        monthlySales: calculateTotal(monthlySales),
      };
    } catch (error) {
      apiLogger.error('Error in getUserSalesData', { userId, startDate, endDate, restaurantId, error });
      // Return zero values on error
      return {
        todaySales: 0,
        weeklySales: 0,
        monthlySales: 0,
      };
    }
  }

  private async getUserPerformance(userId: number, restaurantId?: number | null): Promise<{
    thisWeek: number,
    lastWeek: number,
    change: number,
    changeDirection: "up" | "down" | "neutral",
  }> {
    try {
      // If no restaurant ID, return zero values
      if (!restaurantId) {
        return {
          thisWeek: 0,
          lastWeek: 0,
          change: 0,
          changeDirection: 'neutral',
        };
      }

      const now = new Date();
      const thisWeekStart = format(subDays(now, 7), 'yyyy-MM-dd');
      const lastWeekStart = format(subDays(now, 14), 'yyyy-MM-dd');
      const lastWeekEnd = format(subDays(now, 7), 'yyyy-MM-dd');
      const today = format(now, 'yyyy-MM-dd');

      const [thisWeekSales, lastWeekSales] = await Promise.all([
        this.salesRepo.findByDateRange(thisWeekStart, today, restaurantId),
        this.salesRepo.findByDateRange(lastWeekStart, lastWeekEnd, restaurantId),
      ]);

      // Safely calculate totals with validation
      const calculateTotal = (sales: any[]) => {
        return sales.reduce((acc, sale) => {
          const amount = parseFloat(sale.totalSales);
          return acc + (isNaN(amount) ? 0 : amount);
        }, 0);
      };

      const thisWeek = calculateTotal(thisWeekSales);
      const lastWeek = calculateTotal(lastWeekSales);

      const rawChange = lastWeek > 0 ? ((thisWeek - lastWeek) / lastWeek) * 100 : 0;
      const change = Math.round(rawChange);
      const changeDirection: 'up' | 'down' | 'neutral' = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral';

      return {
        thisWeek,
        lastWeek,
        change,
        changeDirection,
      };
    } catch (error) {
      apiLogger.error('Error in getUserPerformance', { userId, restaurantId, error });
      // Return default values on error
      return {
        thisWeek: 0,
        lastWeek: 0,
        change: 0,
        changeDirection: 'neutral',
      };
    }
  }

  private async getUserRecentSales(userId: number, limit: number): Promise<{
    data: Array<{
      id: number,
      amount: number,
      date: string,
      restaurantName: string,
    }>
  }> {
    try {
      // Validate user exists
      const user = await this.userRepo.findById(userId);
      if (!user) {
        apiLogger.warn('User not found for getUserRecentSales', { userId });
        return { data: [] };
      }

      const restaurantId = user.restaurantId;

      // If user has no restaurant association, return empty data
      if (!restaurantId) {
        apiLogger.info('User has no restaurant association, returning empty recent sales', { userId });
        return { data: [] };
      }

      const salesData = await this.salesRepo.findAll({
        limit,
        restaurantId: restaurantId,
        sortBy: 'recordedAt',
        sortOrder: 'desc',
      });

      const recentSales = [];
      for (const sale of salesData.data) {
        try {
          // Validate sale data before processing
          if (!sale.salesId || !sale.totalSales || !sale.salesDate || !sale.restaurantId) {
            apiLogger.warn('Invalid sale data found, skipping', {
              salesId: sale.salesId,
              totalSales: sale.totalSales,
              salesDate: sale.salesDate,
              restaurantId: sale.restaurantId
            });
            continue;
          }

          // Safely parse amount
          const amount = parseFloat(sale.totalSales);
          if (isNaN(amount)) {
            apiLogger.warn('Invalid totalSales value, skipping sale', {
              salesId: sale.salesId,
              totalSales: sale.totalSales
            });
            continue;
          }

          // Safely parse date
          let formattedDate: string;
          try {
            const saleDate = new Date(sale.salesDate);
            if (isNaN(saleDate.getTime())) {
              throw new Error('Invalid date');
            }
            formattedDate = format(saleDate, 'yyyy-MM-dd');
          } catch (dateError) {
            apiLogger.warn('Invalid salesDate value, skipping sale', {
              salesId: sale.salesId,
              salesDate: sale.salesDate,
              error: dateError
            });
            continue;
          }

          // Get restaurant info
          const restaurant = await this.restaurantRepo.findById(sale.restaurantId);

          recentSales.push({
            id: sale.salesId,
            amount: amount,
            date: formattedDate,
            restaurantName: restaurant?.name || 'Unknown Restaurant',
          });
        } catch (saleProcessingError) {
          apiLogger.warn('Error processing individual sale, skipping', {
            salesId: sale.salesId,
            error: saleProcessingError
          });
          continue;
        }
      }

      return { data: recentSales };
    } catch (error) {
      apiLogger.error('Error in getUserRecentSales', { userId, limit, error });
      // Return empty data instead of throwing to prevent cascade failures
      return { data: [] };
    }
  }

  private async getUserSalesTarget(userId: number, restaurantId?: number | null): Promise<{ data: { salesTarget: number } }> {
    try {
      // This is a simplified implementation - in a real system, you'd have a targets table
      // For now, we'll use a default target based on restaurant performance
      if (restaurantId) {
        const now = new Date();
        const monthAgo = format(subMonths(now, 1), 'yyyy-MM-dd');
        const today = format(now, 'yyyy-MM-dd');

        const sales = await this.salesRepo.findByDateRange(monthAgo, today, restaurantId);

        // Safely calculate average monthly sales
        const averageMonthly = sales.reduce((acc, sale) => {
          const saleAmount = parseFloat(sale.totalSales);
          return acc + (isNaN(saleAmount) ? 0 : saleAmount);
        }, 0);

        // Set target as 110% of last month's performance, with a minimum of 1000
        const salesTarget = Math.max(Math.round(averageMonthly * 1.1), 1000);

        return { data: { salesTarget } };
      }

      // Default target for users without restaurant association
      return { data: { salesTarget: 5000 } };
    } catch (error) {
      apiLogger.error('Error in getUserSalesTarget', { userId, restaurantId, error });
      // Return default target on error
      return { data: { salesTarget: 5000 } };
    }
  }

  /**
   * Calculate payment method breakdown from sales data
   */
  private calculatePaymentBreakdown(sales: any[]): Record<string, number> {
    return sales.reduce((acc, sale) => {
      try {
        const cashAmount = parseFloat(sale.cashAmount || '0');
        const cardAmount = parseFloat(sale.cardAmount || '0');
        const onlineAmount = parseFloat(sale.onlinePaymentAmount || '0');

        if (cashAmount > 0) {
          acc['Cash'] = (acc['Cash'] || 0) + cashAmount;
        }
        if (cardAmount > 0) {
          acc['Card'] = (acc['Card'] || 0) + cardAmount;
        }
        if (onlineAmount > 0) {
          acc['Online'] = (acc['Online'] || 0) + onlineAmount;
        }
      } catch (error) {
        apiLogger.warn('Error processing sale for payment breakdown', {
          saleId: sale.salesId,
          error
        });
      }
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Calculate product breakdown from sales data
   */
  private calculateProductBreakdown(sales: any[], productCategory?: string): Record<string, number> {
    // This is a simplified implementation
    // In a real system, you'd have product details in the sales data
    return sales.reduce((acc, sale) => {
      try {
        const totalSales = parseFloat(sale.totalSales || '0');
        const category = productCategory || 'General';
        acc[category] = (acc[category] || 0) + totalSales;
      } catch (error) {
        apiLogger.warn('Error processing sale for product breakdown', {
          saleId: sale.salesId,
          error
        });
      }
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Calculate category breakdown from sales data
   */
  private calculateCategoryBreakdown(sales: any[], productCategory?: string): Record<string, number> {
    // Similar to product breakdown but grouped by categories
    return this.calculateProductBreakdown(sales, productCategory);
  }

  /**
   * Calculate time-based breakdown from sales data
   */
  private calculateTimeBreakdown(sales: any[]): Record<string, number> {
    return sales.reduce((acc, sale) => {
      try {
        const saleDate = new Date(sale.salesDate);
        const hour = saleDate.getHours();
        const totalSales = parseFloat(sale.totalSales || '0');

        let timeSlot: string;
        if (hour < 6) timeSlot = 'Night (12AM-6AM)';
        else if (hour < 12) timeSlot = 'Morning (6AM-12PM)';
        else if (hour < 18) timeSlot = 'Afternoon (12PM-6PM)';
        else timeSlot = 'Evening (6PM-12AM)';

        acc[timeSlot] = (acc[timeSlot] || 0) + totalSales;
      } catch (error) {
        apiLogger.warn('Error processing sale for time breakdown', {
          saleId: sale.salesId,
          error
        });
      }
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Calculate location-based breakdown from sales data
   */
  private calculateLocationBreakdown(sales: any[]): Record<string, number> {
    // This would require restaurant location data
    // For now, we'll group by restaurant
    return sales.reduce((acc, sale) => {
      try {
        const totalSales = parseFloat(sale.totalSales || '0');
        const location = `Restaurant ${sale.restaurantId}`;
        acc[location] = (acc[location] || 0) + totalSales;
      } catch (error) {
        apiLogger.warn('Error processing sale for location breakdown', {
          saleId: sale.salesId,
          error
        });
      }
      return acc;
    }, {} as Record<string, number>);
  }
}

export const dashboardService = new DashboardService();
