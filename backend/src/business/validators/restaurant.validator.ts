import { z } from 'zod';

// Base restaurant name validation
const restaurantNameSchema = z
  .string()
  .min(1, 'Restaurant name is required')
  .max(255, 'Restaurant name must be less than 255 characters')
  .trim();

// Address validation
const addressSchema = z
  .string()
  .min(1, 'Address is required')
  .max(500, 'Address must be less than 500 characters')
  .trim();

// Phone number validation
const phoneNumberSchema = z
  .string()
  .min(1, 'Phone number is required')
  .max(20, 'Phone number must be less than 20 characters')
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format');

// Time validation (HH:MM format)
const timeSchema = z
  .string()
  .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Time must be in HH:MM format (24-hour)');

// Description validation
const descriptionSchema = z
  .string()
  .max(1000, 'Description must be less than 1000 characters')
  .optional();

// Boolean validation for active status
const isActiveSchema = z.boolean().default(true);

// Create restaurant validation
export const createRestaurantSchema = z.object({
  name: restaurantNameSchema,
  address: addressSchema,
  phoneNumber: phoneNumberSchema,
  openingTime: timeSchema,
  closingTime: timeSchema,
  isActive: isActiveSchema,
  description: descriptionSchema,
}).refine((data) => {
  if (!data.openingTime || !data.closingTime) return true;
  const [openHour, openMin] = data.openingTime.split(':').map(Number);
  const [closeHour, closeMin] = data.closingTime.split(':').map(Number);
  if (openHour === undefined || openMin === undefined || closeHour === undefined || closeMin === undefined) {
    return false;
  }
  const openMinutes = openHour * 60 + openMin;
  const closeMinutes = closeHour * 60 + closeMin;
  return closeMinutes > openMinutes;
}, {
  message: 'Closing time must be after opening time',
  path: ['closingTime'],
});

// Update restaurant validation (all fields optional except validation rules)
export const updateRestaurantSchema = z.object({
  name: restaurantNameSchema.optional(),
  address: addressSchema.optional(),
  phoneNumber: phoneNumberSchema.optional(),
  openingTime: timeSchema.optional(),
  closingTime: timeSchema.optional(),
  isActive: z.boolean().optional(),
  description: descriptionSchema,
}).refine((data) => {
  if (!data.openingTime || !data.closingTime) return true;
  const [openHour, openMin] = data.openingTime.split(':').map(Number);
  const [closeHour, closeMin] = data.closingTime.split(':').map(Number);
  if (openHour === undefined || openMin === undefined || closeHour === undefined || closeMin === undefined) {
    return false;
  }
  const openMinutes = openHour * 60 + openMin;
  const closeMinutes = closeHour * 60 + closeMin;
  return closeMinutes > openMinutes;
}, {
  message: 'Closing time must be after opening time',
  path: ['closingTime'],
});

// Restaurant ID parameter validation
export const restaurantIdParamSchema = z.object({
  id: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().positive('Restaurant ID must be a positive number')
  ),
});

// Restaurant query parameters validation
export const restaurantQuerySchema = z.object({
  page: z.preprocess((val) => Number(val), z.number().int().min(1)).default(1),
  limit: z.preprocess((val) => Number(val), z.number().int().min(1).max(100)).default(10),
  search: z.string().max(255).optional(),
  isActive: z.preprocess((val) => val === 'true', z.boolean()).optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  minRating: z.preprocess((val) => Number(val), z.number().min(0).max(5)).optional(),
  maxPrice: z.preprocess((val) => Number(val), z.number().positive()).optional(),
  cuisine: z.string().trim().optional(),
});

// Restaurant search validation
export const restaurantSearchSchema = z.object({
  q: z.string().min(1, 'Search query is required').max(255),
  includeInactive: z.preprocess((val) => val === 'true', z.boolean()).default(false),
});

// Bulk operation validation
export const bulkRestaurantActionSchema = z.object({
  action: z.enum(['activate', 'deactivate', 'delete']),
  restaurantIds: z.array(z.number().positive()).min(1, 'At least one restaurant ID is required'),
});

// Restaurant status update validation
export const restaurantStatusSchema = z.object({
  isActive: z.boolean(),
});

// Type exports
export type CreateRestaurantRequest = z.infer<typeof createRestaurantSchema>;
export type UpdateRestaurantRequest = z.infer<typeof updateRestaurantSchema>;
export type RestaurantIdParam = z.infer<typeof restaurantIdParamSchema>;
export type RestaurantQuery = z.infer<typeof restaurantQuerySchema>;
export type RestaurantSearch = z.infer<typeof restaurantSearchSchema>;
export type BulkRestaurantAction = z.infer<typeof bulkRestaurantActionSchema>;
export type RestaurantStatus = z.infer<typeof restaurantStatusSchema>;
