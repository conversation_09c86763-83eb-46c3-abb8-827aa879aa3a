import { z } from 'zod';

// Decimal amount validation (for monetary values)
const amountSchema = z
  .number()
  .min(0, 'Amount must be non-negative')
  .max(999999.99, 'Amount must be less than 1,000,000')
  .multipleOf(0.01, 'Amount must have at most 2 decimal places');

// Alternative string-based amount validation for form inputs
const amountStringSchema = z
  .string()
  .regex(/^\d+(\.\d{1,2})?$/, 'Amount must be a valid decimal with at most 2 decimal places')
  .transform((val) => parseFloat(val))
  .refine((val) => val >= 0 && val <= 999999.99, 'Amount must be between 0 and 999,999.99');

// Date validation (YYYY-MM-DD format)
const dateSchema = z
  .string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
  .refine((date) => {
    const parsedDate = new Date(date);
    return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
  }, 'Invalid date');

// Restaurant ID validation
const restaurantIdSchema = z.number().positive('Restaurant ID must be a positive number');

// User ID validation
// const userIdSchema = z.number().positive('User ID must be a positive number');

// Notes validation
const notesSchema = z
  .string()
  .max(1000, 'Notes must be less than 1000 characters')
  .optional();

// Create sales validation
export const createSalesSchema = z.object({
  restaurantId: restaurantIdSchema,
  salesDate: dateSchema,
  cashAmount: amountSchema.default(0),
  cardAmount: amountSchema.default(0),
  onlinePaymentAmount: amountSchema.default(0),
  totalDiscounts: amountSchema.default(0),
  notes: notesSchema,
}).transform((data) => ({
  ...data,
  totalSales: data.cashAmount + data.cardAmount + data.onlinePaymentAmount,
})).refine(
  (data) => data.totalSales > 0,
  {
    message: 'Total sales must be greater than 0',
    path: ['totalSales'],
  }
).refine(
  (data) => data.totalDiscounts <= data.totalSales,
  {
    message: 'Total discounts cannot exceed total sales',
    path: ['totalDiscounts'],
  }
);

// Alternative create sales schema for form inputs (string amounts)
export const createSalesFormSchema = z.object({
  restaurantId: z.preprocess((val) => Number(val), restaurantIdSchema),
  salesDate: dateSchema,
  cashAmount: z.preprocess((val) => val || '0', amountStringSchema),
  cardAmount: z.preprocess((val) => val || '0', amountStringSchema),
  onlinePaymentAmount: z.preprocess((val) => val || '0', amountStringSchema),
  totalDiscounts: z.preprocess((val) => val || '0', amountStringSchema),
  notes: notesSchema,
}).transform((data) => ({
  ...data,
  totalSales: data.cashAmount + data.cardAmount + data.onlinePaymentAmount,
})).refine(
  (data) => data.totalSales > 0,
  {
    message: 'Total sales must be greater than 0',
    path: ['totalSales'],
  }
).refine(
  (data) => data.totalDiscounts <= data.totalSales,
  {
    message: 'Total discounts cannot exceed total sales',
    path: ['totalDiscounts'],
  }
);

// Update sales validation
export const updateSalesSchema = z.object({
  salesDate: dateSchema.optional(),
  cashAmount: amountSchema.optional(),
  cardAmount: amountSchema.optional(),
  onlinePaymentAmount: amountSchema.optional(),
  totalDiscounts: amountSchema.optional(),
  notes: notesSchema,
}).transform((data) => {
  // Calculate total sales if any amount fields are provided
  if (data.cashAmount !== undefined || data.cardAmount !== undefined || data.onlinePaymentAmount !== undefined) {
    const cash = data.cashAmount ?? 0;
    const card = data.cardAmount ?? 0;
    const online = data.onlinePaymentAmount ?? 0;
    return {
      ...data,
      totalSales: cash + card + online,
    };
  }
  return data;
});

// Sales ID parameter validation
export const salesIdParamSchema = z.object({
  id: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().positive('Sales ID must be a positive number')
  ),
});

// Sales query parameters validation
export const salesQuerySchema = z.object({
  page: z.preprocess((val) => Number(val), z.number().int().min(1)).default(1),
  limit: z.preprocess((val) => Number(val), z.number().int().min(1).max(100)).default(10),
  restaurantId: z.preprocess((val) => val ? Number(val) : undefined, z.number().int().positive()).optional(),
  startDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  recordedBy: z.preprocess((val) => val ? Number(val) : undefined, z.number().int().positive()).optional(),
  sortBy: z.enum(['salesDate', 'totalSales', 'recordedAt']).default('salesDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate);
    }
    return true;
  },
  {
    message: 'Start date must be before or equal to end date',
    path: ['endDate'],
  }
);

// Sales report parameters validation
export const salesReportSchema = z.object({
  startDate: dateSchema,
  endDate: dateSchema,
  restaurantId: z.preprocess((val) => val ? Number(val) : undefined, z.number().int().positive()).optional(),
  groupBy: z.enum(['day', 'week', 'month']).default('day'),
  includeDetails: z.preprocess((val) => val === 'true', z.boolean()).default(false),
}).refine(
  (data) => new Date(data.startDate) <= new Date(data.endDate),
  {
    message: 'Start date must be before or equal to end date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 365; // Max 1 year range
  },
  {
    message: 'Date range cannot exceed 365 days',
    path: ['endDate'],
  }
);

// Sales analytics parameters validation
export const salesAnalyticsSchema = z.object({
  period: z.enum(['today', 'yesterday', 'week', 'month', 'quarter', 'year', 'custom']),
  startDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  restaurantId: z.preprocess((val) => val ? Number(val) : undefined, z.number().int().positive()).optional(),
  compareWith: z.enum(['previous_period', 'previous_year']).optional(),
}).refine(
  (data) => {
    if (data.period === 'custom') {
      return data.startDate && data.endDate;
    }
    return true;
  },
  {
    message: 'Start date and end date are required for custom period',
    path: ['startDate'],
  }
);

// Bulk sales operation validation
export const bulkSalesActionSchema = z.object({
  action: z.enum(['delete', 'export']),
  salesIds: z.array(z.number().positive()).min(1, 'At least one sales ID is required'),
  format: z.enum(['csv', 'excel', 'pdf']).optional(),
});

// Sales duplicate check validation
export const salesDuplicateCheckSchema = z.object({
  restaurantId: restaurantIdSchema,
  salesDate: dateSchema,
});

// Type exports
export type CreateSalesRequest = z.infer<typeof createSalesSchema>;
export type CreateSalesFormRequest = z.infer<typeof createSalesFormSchema>;
export type UpdateSalesRequest = z.infer<typeof updateSalesSchema>;
export type SalesIdParam = z.infer<typeof salesIdParamSchema>;
export type SalesQuery = z.infer<typeof salesQuerySchema>;
export type SalesReport = z.infer<typeof salesReportSchema>;
export type SalesAnalytics = z.infer<typeof salesAnalyticsSchema>;
export type BulkSalesAction = z.infer<typeof bulkSalesActionSchema>;
export type SalesDuplicateCheck = z.infer<typeof salesDuplicateCheckSchema>;
