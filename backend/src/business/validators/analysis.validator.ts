import { z } from 'zod';

export const AnalyticsQuery = z.object({
  period: z.enum(['6m', '12m', '24m']).default('12m'),
  includeForecasts: z.boolean().default(true),
  restaurantId: z.number().int().positive().optional(),
});

export type AnalyticsQuery = z.infer<typeof AnalyticsQuery>;

export const KpiProgressQuery = z.object({
  restaurantId: z.number().int().positive(),
});

export type KpiProgressQuery = z.infer<typeof KpiProgressQuery>;

export const BranchAnalyticsQuery = z.object({
  restaurantId: z.number().int().positive(),
});

export type BranchAnalyticsQuery = z.infer<typeof BranchAnalyticsQuery>;