import express, { Router } from 'express';
import restaurantRoutes from './routes/restaurant.routes';
import authRoutes from './routes/auth.routes';
import salesRoutes from './routes/sales.routes';
import userRoutes from './routes/user.routes';
import whatsappRoutes from './routes/whatsapp.route';
import analysisRoutes from './routes/analysis.routes';
import auditLogRoutes from './routes/audit-log.routes';
import dashboardRoutes from './routes/dashboard.routes';
import { errorHandler } from './middleware/error';

const router: express.Router = Router();
router.use((req, res, next) => {
  console.log(`Incoming request: ${req.method} ${req.originalUrl}`);
  next();
});

router.use('/auth', authRoutes);
router.use('/restaurants', restaurantRoutes);
router.use('/sales', salesRoutes);
router.use('/users', userRoutes);
router.use('/whatsapp', whatsappRoutes);
router.use('/analysis', analysisRoutes);
router.use('/audit-logs', auditLogRoutes);
router.use('/dashboard', dashboardRoutes);

// This should be the last middleware
router.use(errorHandler);

export default router;
