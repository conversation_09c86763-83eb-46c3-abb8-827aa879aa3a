import { Request, Response, NextFunction } from 'express';
import { apiLogger } from '../../infrastructure/logger/pino';

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  admin: 3,
  staff: 2,
  user: 1,
} as const;

type UserRole = keyof typeof ROLE_HIERARCHY;

/**
 * Middleware to require specific roles
 * @param allowedRoles - Array of roles that are allowed to access the endpoint
 */
export function requireRoles(allowedRoles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;
      
      // Added for debugging
      apiLogger.info({ user, path: req.path }, 'Authorization check');

      if (!user) {
        apiLogger.warn('Authorization failed: No user in request');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      const userRole = user.role;
      const hasPermission = allowedRoles.includes(userRole);

      if (!hasPermission) {
        apiLogger.warn('Authorization failed: Insufficient permissions', {
          userId: user.userId,
          userRole,
          requiredRoles: allowedRoles,
        });
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
        });
        return;
      }

      apiLogger.debug('Authorization successful', {
        userId: user.userId,
        userRole,
        endpoint: req.path,
      });

      next();
    } catch (error) {
      apiLogger.error('Authorization middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
}

/**
 * Middleware to require minimum role level
 * @param minimumRole - Minimum role required (user < staff < admin)
 */
export function requireMinimumRole(minimumRole: UserRole) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;

      if (!user) {
        apiLogger.warn('Authorization failed: No user in request');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      const userRoleLevel = ROLE_HIERARCHY[user.role];
      const requiredRoleLevel = ROLE_HIERARCHY[minimumRole];

      if (userRoleLevel < requiredRoleLevel) {
        apiLogger.warn('Authorization failed: Insufficient role level', {
          userId: user.userId,
          userRole: user.role,
          userRoleLevel,
          requiredRole: minimumRole,
          requiredRoleLevel,
        });
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
        });
        return;
      }

      apiLogger.debug('Role authorization successful', {
        userId: user.userId,
        userRole: user.role,
        endpoint: req.path,
      });

      next();
    } catch (error) {
      apiLogger.error('Role authorization middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
}

/**
 * Middleware to check restaurant access permissions
 * Users can only access data for their assigned restaurant (unless admin)
 */
export function requireRestaurantAccess() {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;

      if (!user) {
        apiLogger.warn('Restaurant access check failed: No user in request');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Admins have access to all restaurants
      if (user.role === 'admin') {
        next();
        return;
      }

      // Get restaurant ID from request (params, query, or body)
      const requestedRestaurantId =
        parseInt(req.params && req.params['restaurantId'] as string) ||
        parseInt(req.query && req.query['restaurantId'] as string) ||
        parseInt(req.body && req.body.restaurantId);

      // If no restaurant ID in request, allow (will be handled by business logic)
      if (!requestedRestaurantId) {
        next();
        return;
      }

      // Check if user has access to the requested restaurant
      if (!user.restaurantId || user.restaurantId !== requestedRestaurantId) {
        apiLogger.warn('Restaurant access denied', {
          userId: user.userId,
          userRestaurantId: user.restaurantId,
          requestedRestaurantId,
        });
        res.status(403).json({
          success: false,
          message: 'Access denied to this restaurant',
        });
        return;
      }

      apiLogger.debug('Restaurant access granted', {
        userId: user.userId,
        restaurantId: requestedRestaurantId,
      });

      next();
    } catch (error) {
      apiLogger.error('Restaurant access middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
}

/**
 * Middleware to check if user can modify their own data or if admin
 */
export function requireSelfOrAdmin() {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;

      if (!user) {
        apiLogger.warn('Self or admin check failed: No user in request');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Admins can modify any user
      if (user.role === 'admin') {
        next();
        return;
      }

      // Get target user ID from request params
      const targetUserId = parseInt((req.params && req.params['id'] || '') || (req.params && req.params['userId'] || ''));

      // Check if user is trying to modify their own data
      if (user.userId !== targetUserId) {
        apiLogger.warn('Self or admin access denied', {
          userId: user.userId,
          targetUserId,
        });
        res.status(403).json({
          success: false,
          message: 'Can only modify your own data',
        });
        return;
      }

      apiLogger.debug('Self access granted', {
        userId: user.userId,
        targetUserId,
      });

      next();
    } catch (error) {
      apiLogger.error('Self or admin middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
}

/**
 * Middleware to filter query results based on user's restaurant access
 * Automatically adds restaurant filter for non-admin users
 */
export function applyRestaurantFilter() {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;

      if (!user) {
        apiLogger.warn('Restaurant filter failed: No user in request');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Admins see all data - no filter needed
      if (user.role === 'admin') {
        next();
        return;
      }

      // For non-admin users, automatically filter by their restaurant
      if (user.restaurantId) {
        // Add restaurant filter to query parameters
        req.query['restaurantId'] = user.restaurantId.toString();

        apiLogger.debug('Applied restaurant filter', {
          userId: user.userId,
          restaurantId: user.restaurantId,
        });
      }

      next();
    } catch (error) {
      apiLogger.error('Restaurant filter middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
}

// Convenience middleware combinations
export const requireAdmin = requireRoles(['admin']);
export const requireStaffOrAdmin = requireRoles(['staff', 'admin']);
export const requireAnyRole = requireRoles(['user', 'staff', 'admin']);
