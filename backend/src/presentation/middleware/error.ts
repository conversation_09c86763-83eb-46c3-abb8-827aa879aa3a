import { Request, Response, NextFunction } from 'express';
import { AppError } from '../../infrastructure/errors';
import { apiLogger } from '../../infrastructure/logger/pino';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  if (err instanceof AppError) {
    apiLogger.warn(`Application Error: ${err.message}`, {
      statusCode: err.statusCode,
      path: req.path,
      method: req.method,
    });
    res.status(err.statusCode).json({
      success: false,
      message: err.message,
    });
  } else {
    apiLogger.error('Internal Server Error:', err);
    res.status(500).json({
      success: false,
      message: 'An unexpected internal server error occurred.',
    });
  }
};