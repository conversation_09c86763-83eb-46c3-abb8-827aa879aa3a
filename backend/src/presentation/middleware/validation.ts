import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError, z } from 'zod';
import { apiLogger } from '../../infrastructure/logger/pino';

// Generic validation middleware factory
export const validateRequest = <TOutput>(schema: ZodSchema<TOutput, z.ZodTypeDef, any>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Validate request body
      const validatedData = schema.parse(req.body);
      
      // Replace req.body with validated data
      req.body = validatedData;
      
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        apiLogger.warn('Validation failed', { 
          path: req.path,
          method: req.method,
          errors: error.errors 
        });

        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }

      // Handle unexpected errors
      apiLogger.error('Unexpected validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};

// Query parameter validation middleware
export const validateQuery = <TOutput>(schema: ZodSchema<TOutput, z.ZodTypeDef, any>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedData = schema.parse(req.query);
      req.query = validatedData as any;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        apiLogger.warn('Query validation failed', { 
          path: req.path,
          method: req.method,
          errors: error.errors 
        });

        res.status(400).json({
          success: false,
          message: 'Query validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }

      apiLogger.error('Unexpected query validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};

// URL parameter validation middleware
export const validateParams = <TOutput>(schema: ZodSchema<TOutput, z.ZodTypeDef, any>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedData = schema.parse(req.params);
      req.params = validatedData as any;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        apiLogger.warn('Params validation failed', { 
          path: req.path,
          method: req.method,
          errors: error.errors 
        });

        res.status(400).json({
          success: false,
          message: 'Parameter validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        });
        return;
      }

      apiLogger.error('Unexpected params validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};
