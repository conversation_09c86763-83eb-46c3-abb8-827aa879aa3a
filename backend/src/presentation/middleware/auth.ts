import { Request, Response, NextFunction } from 'express';
import { authService, type AuthService } from '../../business/services/auth.service';
import { userRepository, type UserRepository } from '../../data/repositories/user.repository';
import { type JwtPayload } from '../../business/validators/auth.validator';
import { apiLogger } from '../../infrastructure/logger/pino';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: number;
        username: string;
        email: string;
        role: 'admin' | 'staff' | 'user';
        restaurantId?: number;
      };
    }
  }
}

export class AuthMiddleware {
  constructor(
    private readonly authSvc: AuthService = authService,
    private readonly userRepo: UserRepository = userRepository
  ) { }

  // JWT authentication middleware
  authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          message: 'Access token is required',
        });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify token
      const payload: JwtPayload = await this.authSvc.verifyToken(token);

      // Verify user still exists
      const user = await this.userRepo.findSafeById(payload.userId);
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      // Attach user to request
      req.user = {
        userId: user.userId,
        username: user.username,
        email: user.email,
        role: user.role,
        ...(user.restaurantId && { restaurantId: user.restaurantId }),
      };

      next();
    } catch (error) {
      apiLogger.warn('Authentication failed', {
        path: req.path,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      if (error instanceof Error && error.name === 'TokenExpiredError') {
        res.status(401).json({
          success: false,
          message: 'Token has expired',
        });
        return;
      }

      res.status(401).json({
        success: false,
        message: 'Invalid or expired token',
      });
    }
  };

  // Optional authentication middleware (doesn't fail if no token)
  optionalAuthenticate = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        next();
        return;
      }

      const token = authHeader.substring(7);
      const payload: JwtPayload = await this.authSvc.verifyToken(token);
      const user = await this.userRepo.findSafeById(payload.userId);

      if (user) {
        req.user = {
          userId: user.userId,
          username: user.username,
          email: user.email,
          role: user.role,
          ...(user.restaurantId && { restaurantId: user.restaurantId }),
        };
      }

      next();
    } catch (error) {
      // Log but don't fail the request
      apiLogger.debug('Optional authentication failed', {
        path: req.path,
        method: req.method,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next();
    }
  };
}

export const authMiddleware = new AuthMiddleware();
