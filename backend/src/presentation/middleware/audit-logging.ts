import { Request, Response, NextFunction } from 'express';
import { auditLogService } from '../../business/services/audit-log.service';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AUDIT_ACTIONS, RESOURCE_TYPES, type CreateAuditLogData } from '../../data/models/audit-log.model';
import { filterUndefined } from '../../shared/utils/object-utils';

interface AuditLogOptions {
  action: string;
  resourceType?: string;
  getResourceId?: (req: Request, res: Response) => string | undefined;
  getOldValues?: (req: Request) => Record<string, any> | undefined;
  getNewValues?: (req: Request, res: Response) => Record<string, any> | undefined;
  skipIf?: (req: Request, res: Response) => boolean;
}

/**
 * Middleware to automatically log user actions
 */
export function auditLog(options: AuditLogOptions) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original response methods
    const originalSend = res.send;
    const originalJson = res.json;

    let responseData: any;
    let statusCode: number;

    // Override response methods to capture data
    res.send = function (data: any) {
      responseData = data;
      statusCode = res.statusCode;
      return originalSend.call(this, data);
    };

    res.json = function (data: any) {
      responseData = data;
      statusCode = res.statusCode;
      return originalJson.call(this, data);
    };

    // Continue with the request
    next();

    // Log after response is sent
    res.on('finish', async () => {
      try {
        // Skip logging if condition is met
        if (options.skipIf && options.skipIf(req, res)) {
          return;
        }

        // Skip logging for failed requests (4xx, 5xx)
        if (statusCode >= 400) {
          return;
        }

        // Skip if no user is authenticated (for some actions)
        if (!req.user?.userId && options.action !== AUDIT_ACTIONS.USER_LOGIN) {
          return;
        }

        const auditData = {
          userId: req.user?.userId || null,
          action: options.action,
          resourceType: options.resourceType,
          resourceId: options.getResourceId ? options.getResourceId(req, res) : undefined,
          oldValues: options.getOldValues ? options.getOldValues(req) : undefined,
          newValues: options.getNewValues ? options.getNewValues(req, res) : responseData,
          ipAddress: req.ip || req.socket.remoteAddress,
          userAgent: req.get('User-Agent'),
        };

        await auditLogService.createAuditLog(filterUndefined(auditData) as CreateAuditLogData);
      } catch (error) {
        // Don't fail the request if audit logging fails
        apiLogger.error('Failed to create audit log:', error);
      }
    });
  };
}

/**
 * Predefined audit logging middleware for common actions
 */
export const auditMiddleware = {
  // User actions
  userCreated: auditLog({
    action: AUDIT_ACTIONS.USER_CREATED,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data?.userId?.toString();
    },
    getNewValues: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data;
    },
  }),

  userUpdated: auditLog({
    action: AUDIT_ACTIONS.USER_UPDATED,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
    getNewValues: (req) => req.body,
  }),

  userDeleted: auditLog({
    action: AUDIT_ACTIONS.USER_DELETED,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
  }),

  userLogin: auditLog({
    action: AUDIT_ACTIONS.USER_LOGIN,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data?.user?.userId?.toString();
    },
    getNewValues: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return {
        email: responseData.data?.user?.email,
        role: responseData.data?.user?.role,
        loginTime: new Date().toISOString(),
      };
    },
  }),

  userLogout: auditLog({
    action: AUDIT_ACTIONS.USER_LOGOUT,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (req) => req.user?.userId?.toString(),
    getNewValues: () => ({
      logoutTime: new Date().toISOString(),
    }),
  }),

  userPasswordChanged: auditLog({
    action: AUDIT_ACTIONS.USER_PASSWORD_CHANGED,
    resourceType: RESOURCE_TYPES.USER,
    getResourceId: (req) => req.params['id'] || req.user?.userId?.toString(),
    getNewValues: () => ({
      passwordChangedAt: new Date().toISOString(),
    }),
  }),

  // Restaurant actions
  restaurantCreated: auditLog({
    action: AUDIT_ACTIONS.RESTAURANT_CREATED,
    resourceType: RESOURCE_TYPES.RESTAURANT,
    getResourceId: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data?.restaurantId?.toString();
    },
    getNewValues: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data;
    },
  }),

  restaurantUpdated: auditLog({
    action: AUDIT_ACTIONS.RESTAURANT_UPDATED,
    resourceType: RESOURCE_TYPES.RESTAURANT,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
    getNewValues: (req) => req.body,
  }),

  restaurantDeleted: auditLog({
    action: AUDIT_ACTIONS.RESTAURANT_DELETED,
    resourceType: RESOURCE_TYPES.RESTAURANT,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
  }),

  // Sales actions
  salesCreated: auditLog({
    action: AUDIT_ACTIONS.SALES_CREATED,
    resourceType: RESOURCE_TYPES.SALES,
    getResourceId: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data?.salesId?.toString();
    },
    getNewValues: (_req, res) => {
      const responseData = res.locals['responseData'] || {};
      return responseData.data;
    },
  }),

  salesUpdated: auditLog({
    action: AUDIT_ACTIONS.SALES_UPDATED,
    resourceType: RESOURCE_TYPES.SALES,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
    getNewValues: (req) => req.body,
  }),

  salesDeleted: auditLog({
    action: AUDIT_ACTIONS.SALES_DELETED,
    resourceType: RESOURCE_TYPES.SALES,
    getResourceId: (req) => req.params['id'],
    getOldValues: (req) => req.body.oldValues,
  }),
};

/**
 * Helper function to create custom audit log middleware
 */
export function createAuditMiddleware(options: AuditLogOptions) {
  return auditLog(options);
}

/**
 * Middleware to capture old values before update/delete operations
 */
export function captureOldValues(getOldValues: (req: Request) => Promise<Record<string, any> | undefined>) {
  return async (req: Request, _res: Response, next: NextFunction) => {
    try {
      const oldValues = await getOldValues(req);
      req.body.oldValues = oldValues;
      next();
    } catch (error) {
      apiLogger.error('Failed to capture old values for audit log:', error);
      next(); // Continue without old values
    }
  };
}
