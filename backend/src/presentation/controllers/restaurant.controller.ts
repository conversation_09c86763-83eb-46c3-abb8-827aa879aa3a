import { Request, Response, NextFunction } from 'express';
import { restaurantService, type RestaurantService } from '../../business/services/restaurant.service';
import {
  type CreateRestaurantRequest,
  type UpdateRestaurantRequest,
  type RestaurantQuery,
  type BulkRestaurantAction,
  type RestaurantStatus
} from '../../business/validators/restaurant.validator';
// import { apiLogger } from '../../infrastructure/logger/pino';

export class RestaurantController {
  constructor(private readonly restaurantSvc: RestaurantService = restaurantService) { }

  getAll = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const query: RestaurantQuery = req.query as any;
      const result = await this.restaurantSvc.getAll(query);
      res.status(200).json({
        success: true,
        message: 'Restaurants retrieved successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  getById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['id'] as string);
      const restaurant = await this.restaurantSvc.getById(restaurantId);
      res.status(200).json({
        success: true,
        message: 'Restaurant retrieved successfully',
        data: { restaurant },
      });
    } catch (error) {
      next(error);
    }
  };

  create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantData: CreateRestaurantRequest = req.body;
      const restaurant = await this.restaurantSvc.create(restaurantData);
      res.status(201).json({
        success: true,
        message: 'Restaurant created successfully',
        data: { restaurant },
      });
    } catch (error) {
      next(error);
    }
  };

  update = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['id'] as string);
      const updateData: UpdateRestaurantRequest = req.body;
      const updatedRestaurant = await this.restaurantSvc.update(restaurantId, updateData);
      res.status(200).json({
        success: true,
        message: 'Restaurant updated successfully',
        data: { restaurant: updatedRestaurant },
      });
    } catch (error) {
      next(error);
    }
  };

  delete = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['id'] as string);
      await this.restaurantSvc.delete(restaurantId);
      res.status(200).json({
        success: true,
        message: 'Restaurant deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  restore = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['id'] as string);
      await this.restaurantSvc.restore(restaurantId);
      res.status(200).json({
        success: true,
        message: 'Restaurant restored successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  updateStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['id'] as string);
      const { isActive }: RestaurantStatus = req.body;
      const updatedRestaurant = await this.restaurantSvc.updateStatus(restaurantId, { isActive });
      res.status(200).json({
        success: true,
        message: 'Restaurant status updated successfully',
        data: { restaurant: updatedRestaurant },
      });
    } catch (error) {
      next(error);
    }
  };

  getActive = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const activeRestaurants = await this.restaurantSvc.getActive();
      res.status(200).json({
        success: true,
        message: 'Active restaurants retrieved successfully',
        data: { restaurants: activeRestaurants },
      });
    } catch (error) {
      next(error);
    }
  };

  search = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const query: RestaurantQuery = req.query as any;
      const result = await this.restaurantSvc.getAll(query);
      res.status(200).json({
        success: true,
        message: 'Restaurants search completed successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  bulkAction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { action, restaurantIds }: BulkRestaurantAction = req.body;
      const result = await this.restaurantSvc.bulkAction(action, restaurantIds);
      res.status(200).json({
        success: true,
        message: result.message,
        data: { results: result.results },
      });
    } catch (error) {
      next(error);
    }
  };
}

export const restaurantController = new RestaurantController();
