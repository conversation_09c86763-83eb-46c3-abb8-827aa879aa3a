import { Request, Response, NextFunction } from 'express';
import { dashboardService, DashboardService } from '../../business/services/dashboard.service';
import {
  adminDashboardQuerySchema,
  userDashboardQuerySchema,
  recentActivityQuerySchema,
  salesBreakdownQuerySchema,
  type AdminDashboardQuery,
  type UserDashboardQuery,
  type RecentActivityQuery,
  type SalesBreakdownQuery,
} from '../../business/validators/dashboard.validator';
import { apiLogger } from '../../infrastructure/logger/pino';
import { BadRequestError } from '../../infrastructure/errors';

export class DashboardController {
  constructor(private readonly dashboardSvc: DashboardService = dashboardService) { }

  /**
   * Get admin dashboard data with comprehensive metrics
   * @route GET /dashboard/admin
   * @access Admin only
   */
  getAdminDashboard = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      apiLogger.info('Fetching admin dashboard data', {
        query: req.query,
        userId: req.user?.userId
      });

      // Validate query parameters
      const validationResult = adminDashboardQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        throw new BadRequestError('Invalid query parameters');
      }

      const query: AdminDashboardQuery = validationResult.data;
      const dashboardData = await this.dashboardSvc.getAdminDashboardData(query);

      res.status(200).json({
        success: true,
        message: 'Admin dashboard data retrieved successfully',
        data: dashboardData,
      });

      apiLogger.info('Admin dashboard data retrieved successfully', {
        userId: req.user?.userId,
        period: query.period,
        dataPoints: {
          totalUsers: dashboardData.metrics.totalUsers,
          totalRestaurants: dashboardData.metrics.totalRestaurants,
          totalRevenue: dashboardData.metrics.totalRevenue,
        },
      });
    } catch (error) {
      apiLogger.error('Error fetching admin dashboard data:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get user dashboard data with personalized metrics
   * @route GET /dashboard/user
   * @access All authenticated users
   */
  getUserDashboard = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new BadRequestError('User ID is required');
      }

      apiLogger.info('Fetching user dashboard data', {
        query: req.query,
        userId
      });

      // Validate query parameters with detailed error reporting
      const validationResult = userDashboardQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        const errorDetails = validationResult.error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          code: issue.code
        }));

        apiLogger.error('Validation failed for user dashboard query', {
          errors: errorDetails,
          query: req.query
        });

        throw new BadRequestError(`Invalid query parameters: ${errorDetails.map(e => `${e.field}: ${e.message}`).join(', ')}`);
      }

      const query: UserDashboardQuery = validationResult.data;
      const dashboardData = await this.dashboardSvc.getUserDashboardData(userId, query);

      res.status(200).json({
        success: true,
        message: 'User dashboard data retrieved successfully',
        data: dashboardData,
      });

      apiLogger.info('User dashboard data retrieved successfully', {
        userId,
        period: query.period,
        dataPoints: {
          monthlySales: dashboardData.monthlySales,
          salesTarget: dashboardData.salesTarget,
          targetAchievement: dashboardData.targetAchievement,
        },
      });
    } catch (error) {
      apiLogger.error('Error fetching user dashboard data: %o', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get user sales trend data for charts
   * @route GET /dashboard/user/sales-trend
   * @access All authenticated users
   */
  getUserSalesTrend = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new BadRequestError('User ID is required');
      }

      apiLogger.info('Fetching user sales trend data', {
        query: req.query,
        userId
      });

      // Validate query parameters
      const validationResult = userDashboardQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        throw new BadRequestError('Invalid query parameters');
      }

      const query: UserDashboardQuery = validationResult.data;

      console.log('DEBUG: Controller method called');
      console.log('DEBUG: Service instance:', this.dashboardSvc);
      console.log('DEBUG: Method exists:', typeof this.dashboardSvc.getUserSalesTrend);

      const salesTrendData = await this.dashboardSvc.getUserSalesTrend(userId, query);

      res.status(200).json({
        success: true,
        message: 'User sales trend data retrieved successfully',
        data: salesTrendData,
      });

      apiLogger.info('User sales trend data retrieved successfully', {
        userId,
        period: query.period,
        dataPoints: salesTrendData.length,
      });
    } catch (error) {
      apiLogger.error('Error fetching user sales trend data:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get user sales breakdown data for charts
   * @route GET /dashboard/user/sales-breakdown
   * @access All authenticated users
   */
  getUserSalesBreakdown = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Add error logging context
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new BadRequestError('User ID is required');
      }

      apiLogger.info('Fetching user sales breakdown data', {
        query: req.query,
        userId
      });

      // Validate query parameters with detailed error reporting
      const validationResult = salesBreakdownQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        const errorDetails = validationResult.error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          code: issue.code
        }));

        apiLogger.error('Validation failed for sales breakdown query', {
          errors: errorDetails,
          query: req.query
        });

        throw new BadRequestError(`Invalid query parameters: ${errorDetails.map(e => `${e.field}: ${e.message}`).join(', ')}`);
      }

      const query: SalesBreakdownQuery = validationResult.data;
      const salesBreakdownData = await this.dashboardSvc.getUserSalesBreakdown(userId, query);

      res.status(200).json({
        success: true,
        message: 'User sales breakdown data retrieved successfully',
        data: salesBreakdownData,
      });

      apiLogger.info('User sales breakdown data retrieved successfully', {
        userId,
        period: query.period,
        dataPoints: salesBreakdownData.length,
      });
    } catch (error) {
      apiLogger.error('Error fetching user sales breakdown data:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get recent activity data
   * @route GET /dashboard/activity
   * @access All authenticated users
   */
  getRecentActivity = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      apiLogger.info('Fetching recent activity data', {
        query: req.query,
        userId: req.user?.userId
      });

      // Validate query parameters
      const validationResult = recentActivityQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        throw new BadRequestError('Invalid query parameters');
      }

      const query: RecentActivityQuery = validationResult.data;

      // For now, we'll use the admin dashboard method to get recent activity
      // In a real implementation, you might want a separate method
      const adminData = await this.dashboardSvc.getAdminDashboardData({
        period: 'month',
        includeForecasts: false,
        limit: query.limit || 10
      });

      res.status(200).json({
        success: true,
        message: 'Recent activity data retrieved successfully',
        data: {
          activities: adminData.recentActivity,
          total: adminData.recentActivity.length,
        },
      });

      apiLogger.info('Recent activity data retrieved successfully', {
        userId: req.user?.userId,
        activityCount: adminData.recentActivity.length,
      });
    } catch (error) {
      apiLogger.error('Error fetching recent activity data:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get dashboard metrics summary
   * @route GET /dashboard/metrics
   * @access Admin and Staff
   */
  getDashboardMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      apiLogger.info('Fetching dashboard metrics summary', {
        query: req.query,
        userId: req.user?.userId
      });

      // Validate query parameters
      const validationResult = adminDashboardQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        throw new BadRequestError('Invalid query parameters');
      }

      const query: AdminDashboardQuery = validationResult.data;
      const dashboardData = await this.dashboardSvc.getAdminDashboardData(query);

      res.status(200).json({
        success: true,
        message: 'Dashboard metrics retrieved successfully',
        data: {
          metrics: dashboardData.metrics,
          userActivity: dashboardData.userActivity,
        },
      });

      apiLogger.info('Dashboard metrics retrieved successfully', {
        userId: req.user?.userId,
        period: query.period,
      });
    } catch (error) {
      apiLogger.error('Error fetching dashboard metrics:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };

  /**
   * Get sales trend data for charts
   * @route GET /dashboard/sales-trend
   * @access All authenticated users
   */
  getSalesTrend = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      apiLogger.info('Fetching sales trend data', {
        query: req.query,
        userId: req.user?.userId
      });

      // Validate query parameters
      const validationResult = adminDashboardQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        throw new BadRequestError('Invalid query parameters');
      }

      const query: AdminDashboardQuery = validationResult.data;
      const dashboardData = await this.dashboardSvc.getAdminDashboardData(query);

      res.status(200).json({
        success: true,
        message: 'Sales trend data retrieved successfully',
        data: {
          salesTrend: dashboardData.salesTrend,
          restaurantPerformance: dashboardData.restaurantPerformance,
        },
      });

      apiLogger.info('Sales trend data retrieved successfully', {
        userId: req.user?.userId,
        period: query.period,
        dataPoints: dashboardData.salesTrend.length,
      });
    } catch (error) {
      apiLogger.error('Error fetching sales trend data:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.userId,
        query: req.query,
      });
      next(error);
    }
  };
}

export const dashboardController = new DashboardController();
