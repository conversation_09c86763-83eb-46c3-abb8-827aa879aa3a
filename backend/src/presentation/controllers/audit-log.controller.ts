import { Request, Response } from 'express';
import { auditLogService } from '../../business/services/audit-log.service';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError } from '../../infrastructure/errors';
import { filterUndefined } from '../../shared/utils/object-utils';
import { type CreateAuditLogData } from '../../data/models/audit-log.model';
import {
  type AuditLogQuery,
  type CreateAuditLogRequest,
  type BulkCreateAuditLogsRequest,
  type AuditLogIdParam,
  type UserIdParam,
  type ResourceAuditLogsParam,
  type AuditLogStatsQuery,

} from '../../business/validators/audit-log.validator';

export class AuditLogController {
  /**
   * Get all audit logs with filtering, sorting, and pagination
   * GET /audit-logs
   */
  async getAll(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query as unknown as AuditLogQuery;

      apiLogger.info('Getting all audit logs', { query, userId: req.user?.userId });

      const result = await auditLogService.getAllAuditLogs(filterUndefined(query));

      res.status(200).json({
        success: true,
        message: 'Audit logs retrieved successfully',
        data: {
          logs: result.data,
          pagination: result.pagination,
        },
      });
    } catch (error) {
      apiLogger.error('Error getting audit logs:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get audit log by ID
   * GET /audit-logs/:id
   */
  async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params as AuditLogIdParam;

      apiLogger.info('Getting audit log by ID', { id, userId: req.user?.userId });

      const auditLog = await auditLogService.getAuditLogById(id);

      res.status(200).json({
        success: true,
        message: 'Audit log retrieved successfully',
        data: auditLog,
      });
    } catch (error) {
      apiLogger.error('Error getting audit log by ID:', error);

      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          message: error.message,
        });
      } else if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get audit logs for a specific user
   * GET /audit-logs/user/:userId
   */
  async getUserAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params as unknown as UserIdParam;
      const query = req.query as unknown as Omit<AuditLogQuery, 'userId'>;

      apiLogger.info('Getting user audit logs', { userId, query, requestUserId: req.user?.userId });

      const result = await auditLogService.getUserAuditLogs(userId, filterUndefined(query));

      res.status(200).json({
        success: true,
        message: 'User audit logs retrieved successfully',
        data: {
          logs: result.data,
          pagination: result.pagination,
        },
      });
    } catch (error) {
      apiLogger.error('Error getting user audit logs:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get audit logs for a specific resource
   * GET /audit-logs/resource/:resourceType/:resourceId
   */
  async getResourceAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const { resourceType, resourceId } = req.params as ResourceAuditLogsParam;
      const query = req.query as unknown as Omit<AuditLogQuery, 'resourceType' | 'resourceId'>;

      apiLogger.info('Getting resource audit logs', {
        resourceType,
        resourceId,
        query,
        userId: req.user?.userId
      });

      const result = await auditLogService.getResourceAuditLogs(resourceType, resourceId, filterUndefined(query));

      res.status(200).json({
        success: true,
        message: 'Resource audit logs retrieved successfully',
        data: {
          logs: result.data,
          pagination: result.pagination,
        },
      });
    } catch (error) {
      apiLogger.error('Error getting resource audit logs:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get audit log statistics
   * GET /audit-logs/stats
   */
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query as unknown as AuditLogStatsQuery;

      apiLogger.info('Getting audit log statistics', { query, userId: req.user?.userId });

      const stats = await auditLogService.getAuditLogStats(filterUndefined(query));

      res.status(200).json({
        success: true,
        message: 'Audit log statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      apiLogger.error('Error getting audit log statistics:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get filter options for dropdowns
   * GET /audit-logs/filter-options
   */
  async getFilterOptions(req: Request, res: Response): Promise<void> {
    try {
      apiLogger.info('Getting audit log filter options', { userId: req.user?.userId });

      const options = await auditLogService.getFilterOptions();

      res.status(200).json({
        success: true,
        message: 'Filter options retrieved successfully',
        data: options,
      });
    } catch (error) {
      apiLogger.error('Error getting filter options:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Create a new audit log entry
   * POST /audit-logs
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      const data = req.body as CreateAuditLogRequest;

      apiLogger.info('Creating audit log', { data, userId: req.user?.userId });

      // Add user context if not provided
      if (!data.userId && req.user?.userId) {
        data.userId = req.user.userId;
      }

      // Add IP address and user agent from request
      if (!data.ipAddress) {
        data.ipAddress = req.ip || req.socket.remoteAddress;
      }

      if (!data.userAgent) {
        data.userAgent = req.get('User-Agent');
      }

      const auditLog = await auditLogService.createAuditLog(filterUndefined(data) as CreateAuditLogData);

      res.status(201).json({
        success: true,
        message: 'Audit log created successfully',
        data: auditLog,
      });
    } catch (error) {
      apiLogger.error('Error creating audit log:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Bulk create audit logs
   * POST /audit-logs/bulk
   */
  async bulkCreate(req: Request, res: Response): Promise<void> {
    try {
      const { auditLogs: auditLogsData } = req.body as BulkCreateAuditLogsRequest;

      apiLogger.info('Bulk creating audit logs', { count: auditLogsData.length, userId: req.user?.userId });

      // Add user context to each entry if not provided
      const enrichedData = auditLogsData.map(data => ({
        ...data,
        userId: data.userId || req.user?.userId || null,
        ipAddress: data.ipAddress || req.ip || req.socket.remoteAddress,
        userAgent: data.userAgent || req.get('User-Agent'),
      }));

      const auditLogs = await auditLogService.bulkCreateAuditLogs(enrichedData.map(item => filterUndefined(item) as CreateAuditLogData));

      res.status(201).json({
        success: true,
        message: 'Audit logs created successfully',
        data: {
          auditLogs,
          count: auditLogs.length,
        },
      });
    } catch (error) {
      apiLogger.error('Error bulk creating audit logs:', error);

      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Soft delete audit log
   * DELETE /audit-logs/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params as AuditLogIdParam;

      apiLogger.info('Deleting audit log', { id, userId: req.user?.userId });

      const result = await auditLogService.deleteAuditLog(id);

      res.status(200).json({
        success: true,
        message: 'Audit log deleted successfully',
        data: { deleted: result },
      });
    } catch (error) {
      apiLogger.error('Error deleting audit log:', error);

      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          message: error.message,
        });
      } else if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Restore soft deleted audit log
   * POST /audit-logs/:id/restore
   */
  async restore(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params as AuditLogIdParam;

      apiLogger.info('Restoring audit log', { id, userId: req.user?.userId });

      const result = await auditLogService.restoreAuditLog(id);

      res.status(200).json({
        success: true,
        message: 'Audit log restored successfully',
        data: { restored: result },
      });
    } catch (error) {
      apiLogger.error('Error restoring audit log:', error);

      if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          message: error.message,
        });
      } else if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }
}

// Export singleton instance
export const auditLogController = new AuditLogController();
