import { Request, Response, NextFunction } from 'express';
import { salesService, type SalesService } from '../../business/services/sales.service';
import {
  type CreateSalesRequest,
  type CreateSalesFormRequest,
  type UpdateSalesRequest,
  type SalesQuery,
  type SalesReport,
  type SalesAnalytics,
  type BulkSalesAction,
  type SalesDuplicateCheck
} from '../../business/validators/sales.validator';
// import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError } from '../../infrastructure/errors';

export class SalesController {
  constructor(private readonly salesSvc: SalesService = salesService) { }

  getAll = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const query: SalesQuery = req.query as any;
      const result = await this.salesSvc.getAll(query);
      res.status(200).json({
        success: true,
        message: 'Sales retrieved successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  getById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const salesId = parseInt(req.params['id'] as string);
      const salesRecord = await this.salesSvc.getById(salesId);
      res.status(200).json({
        success: true,
        message: 'Sales record retrieved successfully',
        data: { sales: salesRecord },
      });
    } catch (error) {
      next(error);
    }
  };

  create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const salesData: CreateSalesRequest | CreateSalesFormRequest = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        throw new AppError('User authentication required', 401);
      }

      const salesRecord = await this.salesSvc.create(salesData, userId);
      res.status(201).json({
        success: true,
        message: 'Sales record created successfully',
        data: { sales: salesRecord },
      });
    } catch (error) {
      next(error);
    }
  };

  update = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const salesId = parseInt(req.params['id'] as string);
      const updateData: UpdateSalesRequest = req.body;
      const updatedSales = await this.salesSvc.update(salesId, updateData);
      res.status(200).json({
        success: true,
        message: 'Sales record updated successfully',
        data: { sales: updatedSales },
      });
    } catch (error) {
      next(error);
    }
  };

  delete = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const salesId = parseInt(req.params['id'] as string);
      await this.salesSvc.delete(salesId);
      res.status(200).json({
        success: true,
        message: 'Sales record deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  restore = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const salesId = parseInt(req.params['id'] as string);
      await this.salesSvc.restore(salesId);
      res.status(200).json({
        success: true,
        message: 'Sales record restored successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  getSummary = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { startDate, endDate, restaurantId } = req.query as any;
      const summary = await this.salesSvc.getSummary(
        startDate,
        endDate,
        restaurantId ? parseInt(restaurantId) : undefined
      );
      res.status(200).json({
        success: true,
        message: 'Sales summary retrieved successfully',
        data: { summary },
      });
    } catch (error) {
      next(error);
    }
  };

  getReport = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const reportParams: SalesReport = req.query as any;
      const result = await this.salesSvc.getReport(reportParams);
      res.status(200).json({
        success: true,
        message: 'Sales report generated successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getAnalytics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const analyticsParams: SalesAnalytics = req.query as any;
      const result = await this.salesSvc.getAnalytics(analyticsParams);
      res.status(200).json({
        success: true,
        message: 'Sales analytics retrieved successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  bulkAction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { action, salesIds }: BulkSalesAction = req.body;
      const result = await this.salesSvc.bulkAction(action, salesIds);
      res.status(200).json({
        success: true,
        message: result.message,
        data: { results: result.results },
      });
    } catch (error) {
      next(error);
    }
  };

  checkDuplicate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { restaurantId, salesDate }: SalesDuplicateCheck = req.body;
      const existingSales = await this.salesSvc.findDuplicate(restaurantId, salesDate);
      res.status(200).json({
        success: true,
        message: 'Duplicate check completed',
        data: {
          exists: !!existingSales,
          existingSales: existingSales || null
        },
      });
    } catch (error) {
      next(error);
    }
  };
}

export const salesController = new SalesController();
