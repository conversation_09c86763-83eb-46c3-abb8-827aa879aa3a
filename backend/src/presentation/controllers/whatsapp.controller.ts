import { Request, Response } from 'express';
import {
  disconnect,
  getConnectionStatus,
  initializeWhatsApp,
  requestPairingCode,
  whatsappEmitter,
} from 'business/services/whatsapp/client';
import qr from 'qr-image';

let lastQrCode: string | null = null;

whatsappEmitter.on('qr', (qrCode) => {
  lastQrCode = qrCode;
});

export const getWhatsAppStatus = async (_req: Request, res: Response) => {
  await initializeWhatsApp();
  const status = getConnectionStatus();
  res.status(200).json(status);
};

export const getQrCodeAsImage = async (_req: Request, res: Response) => {
  await initializeWhatsApp();
  if (lastQrCode) {
    const img = qr.image(lastQrCode, { type: 'png' });
    res.type('png');
    img.pipe(res);
  } else {
    res.status(404).send('QR code not available');
  }
};

export const pairWithPhoneNumber = async (req: Request, res: Response) => {
  await initializeWhatsApp();
  try {
    const { phoneNumber } = req.body;
    if (!phoneNumber) {
      return res.status(400).send('Phone number is required');
    }
    const pairingCode = await requestPairingCode(phoneNumber);
    res.status(200).json({ pairingCode });
  } catch (error: any) {
    res.status(500).send(error.message);
  }
};

export const disconnectWhatsApp = async (_req: Request, res: Response) => {
  try {
    await disconnect();
    res.status(200).send('WhatsApp client disconnected and session cleared');
  } catch (error: any) {
    res.status(500).send(error.message);
  }
};