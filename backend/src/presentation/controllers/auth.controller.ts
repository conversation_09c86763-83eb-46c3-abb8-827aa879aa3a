import { Request, Response, NextFunction } from 'express';
import { authService, type AuthService } from '../../business/services/auth.service';
import { type RegisterRequest, type LoginRequest } from '../../business/validators/auth.validator';
import { apiLogger } from '../../infrastructure/logger/pino';

export class AuthController {
  constructor(private readonly authSvc: AuthService = authService) { }

  register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userData: RegisterRequest = req.body;
      const result = await this.authSvc.register(userData);
      apiLogger.info('User registered successfully', {
        userId: result.user.userId,
        username: result.user.username
      });
      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const credentials: LoginRequest = req.body;
      const result = await this.authSvc.login(credentials);
      apiLogger.info('User logged in successfully', {
        userId: result.user.userId,
        username: result.user.username
      });
      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  verify = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      if (!token) {
        res.status(401).json({ success: false, message: 'No token provided' });
        return;
      }
      const decoded = await this.authSvc.verifyToken(token);
      res.status(200).json({
        success: true,
        message: 'Token is valid',
        data: { user: decoded },
      });
    } catch (error) {
      next(error);
    }
  };

  getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }
      // Assuming you have a method in your authService to get a user's profile
      // const user = await this.authSvc.getProfile(req.user.userId);
      res.status(200).json({
        success: true,
        message: 'Profile retrieved successfully',
        data: {
          user: req.user,
        },
      });
    } catch (error) {
      next(error);
    }
  };
}

export const authController = new AuthController();
