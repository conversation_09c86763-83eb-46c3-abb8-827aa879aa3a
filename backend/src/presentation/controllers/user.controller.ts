import { Request, Response, NextFunction } from 'express';
import { userService, type UserService } from '../../business/services/user.service';
import {
  type AdminCreateUserRequest,
  type AdminUpdateUserRequest,
  type BulkUserAction,
  type ChangePasswordRequest,
  type UpdateUserRequest,
  // type UserStatsQuery,
} from '../../business/validators/auth.validator';
import { AppError } from '../../infrastructure/errors';

export class UserController {
  constructor(private readonly userSvc: UserService = userService) { }

  getAll = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // The query parameters are already validated by the validateQuery middleware
      // So we can use req.query directly as it's already been parsed and validated
      const query = req.query as any; // Type assertion since validation middleware already processed it
      const result = await this.userSvc.getAll(query);

      // Set cache control headers to prevent caching of filtered results
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.status(200).json({
        success: true,
        message: 'Users retrieved successfully',
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  };

  getById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = parseInt(req.params['id'] as string);
      const user = await this.userSvc.getById(userId);
      res.status(200).json({
        success: true,
        message: 'User retrieved successfully',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  };

  create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userData: AdminCreateUserRequest = req.body;
      const user = await this.userSvc.create(userData);
      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  };

  update = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = parseInt(req.params['id'] as string);
      const updateData: AdminUpdateUserRequest = req.body;
      const updatedUser = await this.userSvc.update(userId, updateData);
      res.status(200).json({
        success: true,
        message: 'User updated successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  };

  delete = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.params['id'] ? parseInt(req.params['id']) : req.user!.userId;
      const requestingUserId = req.user?.userId;
      const { password } = req.body;

      // If it's a self-delete, password is required
      if (userId === requestingUserId && !password) {
        throw new AppError('Password is required for self-deletion', 400);
      }

      await this.userSvc.delete(userId, requestingUserId, password);

      res.status(200).json({
        success: true,
        message: 'User deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        throw new AppError('User authentication required', 401);
      }
      const user = await this.userSvc.getProfile(userId);
      res.status(200).json({
        success: true,
        message: 'Profile retrieved successfully',
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  };

  updateProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const updateData: UpdateUserRequest = req.body;
      if (!userId) {
        throw new AppError('User authentication required', 401);
      }
      const updatedUser = await this.userSvc.updateProfile(userId, updateData);
      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  };

  changePassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { currentPassword, newPassword }: ChangePasswordRequest = req.body;
      if (!userId) {
        throw new AppError('User authentication required', 401);
      }
      await this.userSvc.changePassword(userId, { currentPassword, newPassword });
      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  getByRestaurant = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const restaurantId = parseInt(req.params['restaurantId'] as string);
      const users = await this.userSvc.getByRestaurant(restaurantId);
      res.status(200).json({
        success: true,
        message: 'Users retrieved successfully',
        data: { users },
      });
    } catch (error) {
      next(error);
    }
  };

  bulkAction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { action, userIds, restaurantId }: BulkUserAction = req.body;
      const result = await this.userSvc.bulkAction(action, userIds, restaurantId);
      res.status(200).json({
        success: true,
        message: result.message,
        data: { results: result.results },
      });
    } catch (error) {
      next(error);
    }
  };

  getStats = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.userSvc.getStats();
      res.status(200).json({
        success: true,
        message: 'User statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };
}

export const userController = new UserController();
