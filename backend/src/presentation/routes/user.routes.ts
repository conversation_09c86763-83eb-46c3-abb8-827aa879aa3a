import { Router } from 'express';
import { userController } from '../controllers/user.controller';
import { authMiddleware } from '../middleware/auth';
import {
  requireAdmin,
  requireStaffOrAdmin,
  requireAnyRole,
  requireSelfOrAdmin
} from '../middleware/authorization';
import { validateRequest, validateQuery, validateParams } from '../middleware/validation';
import {
  userQuerySchema,
  userIdParamSchema,
  adminCreateUserSchema,
  adminUpdateUserSchema,
  updateUserSchema,
  changePasswordSchema,
  bulkUserActionSchema,
  userStatsQuerySchema,
} from '../../business/validators/auth.validator';

const router: Router = Router();

// Apply authentication to all user routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /users
 * @desc Get all users with pagination and filtering
 * @access Admin only
 */
router.get(
  '/',
  requireAdmin,
  validateQuery(userQuerySchema),
  userController.getAll
);

/**
 * @route GET /users/profile
 * @desc Get current user profile
 * @access All authenticated users
 */
router.get(
  '/profile',
  requireAnyRole,
  userController.getProfile
);

/**
 * @route PUT /users/profile
 * @desc Update current user profile
 * @access All authenticated users
 */
router.put(
  '/profile',
  requireAnyRole,
  validateRequest(updateUserSchema),
  userController.updateProfile
);

/**
 * @route DELETE /users/profile
 * @desc Delete current user profile
 * @access All authenticated users
 */
router.delete(
  '/profile',
  requireAnyRole,
  userController.delete
);

/**
 * @route POST /users/change-password
 * @desc Change current user password
 * @access All authenticated users
 */
router.post(
  '/change-password',
  requireAnyRole,
  validateRequest(changePasswordSchema),
  userController.changePassword
);

/**
 * @route GET /users/restaurant/:restaurantId
 * @desc Get users by restaurant
 * @access Staff, Admin
 */
router.get(
  '/restaurant/:restaurantId',
  requireStaffOrAdmin,
  userController.getByRestaurant
);

/**
 * @route GET /users/stats
 * @desc Get user statistics
 * @access Admin only
 */
router.get(
  '/stats',
  requireAdmin,
  validateQuery(userStatsQuerySchema),
  userController.getStats
);

/**
 * @route GET /users/:id
 * @desc Get user by ID
 * @access Admin (or self)
 */
router.get(
  '/:id',
  requireSelfOrAdmin(),
  validateParams(userIdParamSchema),
  userController.getById
);

/**
 * @route POST /users
 * @desc Create new user
 * @access Admin only
 */
router.post(
  '/',
  requireAdmin,
  validateRequest(adminCreateUserSchema),
  userController.create
);

/**
 * @route PUT /users/:id
 * @desc Update user (admin operation)
 * @access Admin only
 */
router.put(
  '/:id',
  requireAdmin,
  validateParams(userIdParamSchema),
  validateRequest(adminUpdateUserSchema),
  userController.update
);

/**
 * @route DELETE /users/:id
 * @desc Delete user
 * @access Admin only
 */
router.delete(
  '/:id',
  requireAdmin,
  validateParams(userIdParamSchema),
  userController.delete
);

/**
 * @route POST /users/bulk
 * @desc Bulk operations on users (activate, deactivate, delete, assign_restaurant)
 * @access Admin only
 */
router.post(
  '/bulk',
  requireAdmin,
  validateRequest(bulkUserActionSchema),
  userController.bulkAction
);

export default router;
