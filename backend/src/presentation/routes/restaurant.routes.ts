import express, { Router } from 'express';
import { restaurantController } from '../controllers/restaurant.controller';
import { authMiddleware } from '../middleware/auth';
import {
  requireAdmin,

  requireAnyRole,
  applyRestaurantFilter
} from '../middleware/authorization';
import {
  validateRequest,
  validateQuery,
  validateParams
} from '../middleware/validation';
import {
  createRestaurantSchema,
  updateRestaurantSchema,
  restaurantIdParamSchema,
  restaurantQuerySchema,
  restaurantSearchSchema,
  bulkRestaurantActionSchema,
  restaurantStatusSchema,
} from '../../business/validators/restaurant.validator';

const router: express.Router = Router();

// Apply authentication to all restaurant routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /restaurants
 * @desc Get all restaurants with pagination and filtering
 * @access Staff, Admin (Users see only their restaurant via filter)
 */
router.get(
  '/',
  requireAnyRole,
  applyRestaurantFilter(),
  validateQuery(restaurantQuerySchema),
  restaurantController.getAll
);

/**
 * @route GET /restaurants/active
 * @desc Get only active restaurants (simplified endpoint)
 * @access Staff, Admin (Users see only their restaurant via filter)
 */
router.get(
  '/active',
  requireAnyRole,
  applyRestaurantFilter(),
  restaurantController.getActive
);

/**
 * @route GET /restaurants/search
 * @desc Search restaurants by name, address, or description
 * @access Staff, Admin (Users see only their restaurant via filter)
 */
router.get(
  '/search',
  requireAnyRole,
  applyRestaurantFilter(),
  validateQuery(restaurantSearchSchema),
  restaurantController.search
);

/**
 * @route GET /restaurants/:id
 * @desc Get restaurant by ID
 * @access Staff, Admin (Users can only see their restaurant)
 */
router.get(
  '/:id',
  requireAnyRole,
  validateParams(restaurantIdParamSchema),
  restaurantController.getById
);

/**
 * @route POST /restaurants
 * @desc Create new restaurant
 * @access Admin only
 */
router.post(
  '/',
  requireAdmin,
  validateRequest(createRestaurantSchema),
  restaurantController.create
);

/**
 * @route PUT /restaurants/:id
 * @desc Update restaurant
 * @access Admin only
 */
router.put(
  '/:id',
  requireAdmin,
  validateParams(restaurantIdParamSchema),
  validateRequest(updateRestaurantSchema),
  restaurantController.update
);

/**
 * @route PATCH /restaurants/:id/status
 * @desc Update restaurant status (active/inactive)
 * @access Admin only
 */
router.patch(
  '/:id/status',
  requireAdmin,
  validateParams(restaurantIdParamSchema),
  validateRequest(restaurantStatusSchema),
  restaurantController.updateStatus
);

/**
 * @route DELETE /restaurants/:id
 * @desc Soft delete restaurant
 * @access Admin only
 */
router.delete(
  '/:id',
  requireAdmin,
  validateParams(restaurantIdParamSchema),
  restaurantController.delete
);

/**
 * @route POST /restaurants/:id/restore
 * @desc Restore soft-deleted restaurant
 * @access Admin only
 */
router.post(
  '/:id/restore',
  requireAdmin,
  validateParams(restaurantIdParamSchema),
  restaurantController.restore
);

/**
 * @route POST /restaurants/bulk
 * @desc Bulk operations on restaurants (activate, deactivate, delete)
 * @access Admin only
 */
router.post(
  '/bulk',
  requireAdmin,
  validateRequest(bulkRestaurantActionSchema),
  restaurantController.bulkAction
);

export default router;
