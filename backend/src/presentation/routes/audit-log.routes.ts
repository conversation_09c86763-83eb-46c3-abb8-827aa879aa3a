import express, { Router } from 'express';
import { auditLogController } from '../controllers/audit-log.controller';
import { authMiddleware } from '../middleware/auth';
import { requireAdmin } from '../middleware/authorization';
import { validateRequest, validateQuery, validateParams } from '../middleware/validation';
import {
  auditLogQuerySchema,
  createAuditLogSchema,
  bulkCreateAuditLogsSchema,
  auditLogIdParamSchema,
  userIdParamSchema,
  resourceAuditLogsParamSchema,
  auditLogStatsQuerySchema,

} from '../../business/validators/audit-log.validator';

const router: express.Router = Router();

// Apply authentication to all audit log routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /audit-logs
 * @desc Get all audit logs with pagination and filtering
 * @access Admin only
 */
router.get(
  '/',
  requireAdmin,
  validateQuery(auditLogQuerySchema),
  auditLogController.getAll
);

/**
 * @route GET /audit-logs/stats
 * @desc Get audit log statistics
 * @access Admin only
 */
router.get(
  '/stats',
  requireAdmin,
  validateQuery(auditLogStatsQuerySchema),
  auditLogController.getStats
);

/**
 * @route GET /audit-logs/filter-options
 * @desc Get filter options for dropdowns
 * @access Admin only
 */
router.get(
  '/filter-options',
  requireAdmin,
  auditLogController.getFilterOptions
);

/**
 * @route GET /audit-logs/user/:userId
 * @desc Get audit logs for a specific user
 * @access Admin only
 */
router.get(
  '/user/:userId',
  requireAdmin,
  validateParams(userIdParamSchema),
  validateQuery(auditLogQuerySchema),
  auditLogController.getUserAuditLogs
);

/**
 * @route GET /audit-logs/resource/:resourceType/:resourceId
 * @desc Get audit logs for a specific resource
 * @access Admin only
 */
router.get(
  '/resource/:resourceType/:resourceId',
  requireAdmin,
  validateParams(resourceAuditLogsParamSchema),
  validateQuery(auditLogQuerySchema),
  auditLogController.getResourceAuditLogs
);

/**
 * @route GET /audit-logs/:id
 * @desc Get audit log by ID
 * @access Admin only
 */
router.get(
  '/:id',
  requireAdmin,
  validateParams(auditLogIdParamSchema),
  auditLogController.getById
);

/**
 * @route POST /audit-logs
 * @desc Create a new audit log entry
 * @access Admin only (for manual entries)
 */
router.post(
  '/',
  requireAdmin,
  validateRequest(createAuditLogSchema),
  auditLogController.create
);

/**
 * @route POST /audit-logs/bulk
 * @desc Bulk create audit logs
 * @access Admin only
 */
router.post(
  '/bulk',
  requireAdmin,
  validateRequest(bulkCreateAuditLogsSchema),
  auditLogController.bulkCreate
);

/**
 * @route POST /audit-logs/:id/restore
 * @desc Restore soft deleted audit log
 * @access Admin only
 */
router.post(
  '/:id/restore',
  requireAdmin,
  validateParams(auditLogIdParamSchema),
  auditLogController.restore
);

/**
 * @route DELETE /audit-logs/:id
 * @desc Soft delete audit log
 * @access Admin only
 */
router.delete(
  '/:id',
  requireAdmin,
  validateParams(auditLogIdParamSchema),
  auditLogController.delete
);

export default router;
