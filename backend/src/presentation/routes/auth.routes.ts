import express, { Router } from 'express';
import { authController } from '../controllers/auth.controller';
import { validateRequest } from '../middleware/validation';
import { registerSchema, loginSchema } from '../../business/validators/auth.validator';
import { authMiddleware } from '../middleware/auth';

const router: express.Router = Router();

router.post('/register', validateRequest(registerSchema), authController.register);
router.post('/login', validateRequest(loginSchema), authController.login);
router.post('/verify', authMiddleware.authenticate, authController.verify);
router.get('/me', authMiddleware.authenticate, authController.getProfile);

export default router;