/**
 * Type-safe utility functions for object manipulation
 */

/**
 * Filter function that removes undefined values
 * Returns a partial type to work with exactOptionalPropertyTypes
 */
export function filterUndefined<T extends Record<string, any>>(
  obj: T
): Partial<T> {
  const filtered: Partial<T> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      (filtered as any)[key] = value;
    }
  }

  return filtered;
}

/**
 * Alternative filter function that returns a partial type (for cases where making all properties optional is desired)
 */
export function filterUndefinedPartial<T extends Record<string, any>>(
  obj: T
): Partial<T> {
  const filtered: Partial<T> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      (filtered as any)[key] = value;
    }
  }

  return filtered;
}

/**
 * Removes null and undefined values from an object
 */
export function filterNullish<T extends Record<string, any>>(
  obj: T
): {
    [K in keyof T]: NonNullable<T[K]>
  } {
  const filtered = {} as any;

  for (const [key, value] of Object.entries(obj)) {
    if (value != null) {
      filtered[key] = value;
    }
  }

  return filtered;
}

/**
 * Deep clone an object (simple implementation for plain objects)
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as T;
  }

  const cloned = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }

  return cloned;
}

/**
 * Pick specific properties from an object
 */
export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;

  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }

  return result;
}

/**
 * Omit specific properties from an object
 */
export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = {} as Omit<T, K>;
  const keysToOmit = new Set(keys);

  for (const key in obj) {
    if (!keysToOmit.has(key as unknown as K)) {
      (result as any)[key] = obj[key];
    }
  }

  return result;
}

/**
 * Check if an object is empty (has no enumerable properties)
 */
export function isEmpty(obj: Record<string, any>): boolean {
  return Object.keys(obj).length === 0;
}

/**
 * Merge multiple objects together (shallow merge)
 */
export function merge<T extends Record<string, any>>(...objects: Partial<T>[]): T {
  return Object.assign({} as T, ...objects);
}
