interface CurrencyConfig {
  symbol: string;
  locale: string;
}

export function formatCurrency(amount: number, config: CurrencyConfig): string {
  return `${config.symbol} ${amount.toLocaleString(config.locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

export function getMonthName(monthString: string): string {
  const [year, month] = monthString.split('-');
  if (!year || !month) {
    return 'Invalid Date';
  }
  const date = new Date(parseInt(year, 10), parseInt(month, 10) - 1);
  return date.toLocaleDateString('en-US', { month: 'long' });
}

export function getYear(monthString: string): number {
  const yearStr = monthString.split('-')[0];
  return yearStr ? parseInt(yearStr, 10) : 0;
}

export function getChangeDirection(changePercentage: number | null): 'up' | 'down' | 'neutral' | null {
  if (changePercentage === null) return null;
  if (changePercentage > 0) return 'up';
  if (changePercentage < 0) return 'down';
  return 'neutral';
}