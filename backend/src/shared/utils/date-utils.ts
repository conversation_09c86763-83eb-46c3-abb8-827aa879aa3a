import { format } from 'date-fns';

/**
 * Supported date period types for calculating date ranges
 */
export type DatePeriod = 'today' | 'week' | 'month' | 'quarter' | 'year' | '6m' | '12m' | '24m';

/**
 * Function to parse a custom period string and return an object with the numeric value and unit
 */
function parseCustomPeriod(period: string): { value: number; unit: string } {
    if (!period || typeof period !== 'string') {
        throw new Error('Period must be a non-empty string');
    }

    const match = period.match(/^(\d+)([dwm])$/);
    if (!match) {
        throw new Error(`Invalid period format: "${period}". Expected format: number followed by d, w, or m (e.g., "30d", "4w", "6m")`);
    }

    const valueStr = String(match[1]);
    const unit = String(match[2]);
    const value = Number.parseInt(valueStr, 10);

    if (isNaN(value) || value <= 0) {
        throw new Error(`Invalid period value: "${valueStr}". Must be a positive number`);
    }

    // Validate reasonable ranges
    if (unit === 'd' && (value < 1 || value > 365)) {
        throw new Error(`Invalid day period: "${period}". Days must be between 1 and 365`);
    }
    if (unit === 'w' && (value < 1 || value > 52)) {
        throw new Error(`Invalid week period: "${period}". Weeks must be between 1 and 52`);
    }
    if (unit === 'm' && (value < 1 || value > 24)) {
        throw new Error(`Invalid month period: "${period}". Months must be between 1 and 24`);
    }

    if (['d', 'w', 'm'].includes(unit)) {
        return { value, unit };
    }

    throw new Error(`Invalid period unit: "${unit}". Must be d, w, or m`);
}

/**
 * Calculate date range based on a specified period
 * 
 * This utility function calculates start and end dates for various time periods
 * relative to the current date. All dates are returned in YYYY-MM-DD format.
 * 
 * @param period - The time period to calculate the date range for
 *   - 'today': Same day (start and end date are the same)
 *   - 'week': Last 7 days from today
 *   - 'month': Last 1 month from today
 *   - 'quarter': Last 3 months from today
 *   - 'year': Last 1 year from today
 *   - '6m': Last 6 months from today
 *   - '12m': Last 12 months from today (same as 'year')
 *   - '24m': Last 24 months from today
 *   - '30d': Custom period of any number followed by 'd' for days, 'w' for weeks, or 'm' for months
 *
 * @returns Object containing startDate and endDate in YYYY-MM-DD format
 */
export function calculateDateRange(period: DatePeriod | string): { startDate: string; endDate: string; } {
    if (!period) {
        throw new Error('Period parameter is required');
    }

    const today = new Date();

    // Validate that today is a valid date
    if (isNaN(today.getTime())) {
        throw new Error('Invalid current date');
    }

    if (typeof period === 'string') {
        // Handle custom period strings like "30d", "4w", "6m"
        if (!['today', 'week', 'month', 'quarter', 'year', '6m', '12m', '24m'].includes(period)) {
            const { value, unit } = parseCustomPeriod(period);
            const startDate = new Date(today);

            switch (unit) {
                case 'd':
                    startDate.setDate(today.getDate() - value);
                    break;
                case 'w':
                    startDate.setDate(today.getDate() - value * 7);
                    break;
                case 'm':
                    startDate.setMonth(today.getMonth() - value);
                    break;
                default:
                    throw new Error(`Unsupported custom period unit: ${unit}`);
            }

            startDate.setHours(0, 0, 0, 0);
            today.setHours(23, 59, 59, 999);

            return {
                startDate: format(startDate, 'yyyy-MM-dd'),
                endDate: format(today, 'yyyy-MM-dd')
            };
        }
    }

    // Handle predefined period types
    let startDate: Date;
    const endDate = new Date(today);
    endDate.setHours(23, 59, 59, 999);

    // Validate period is a supported enum value
    const validPeriods: DatePeriod[] = ['today', 'week', 'month', 'quarter', 'year', '6m', '12m', '24m'];
    if (!validPeriods.includes(period as DatePeriod)) {
        throw new Error(`Invalid period: "${period}". Supported periods: ${validPeriods.join(', ')}`);
    }

    startDate = new Date(today);

    try {
        switch (period) {
            case 'today':
                // For today, start and end are the same day
                break;
            case 'week':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case 'month':
                startDate.setMonth(startDate.getMonth() - 1);
                break;
            case 'quarter':
                startDate.setMonth(startDate.getMonth() - 3);
                break;
            case 'year':
            case '12m':
                startDate.setFullYear(startDate.getFullYear() - 1);
                break;
            case '6m':
                startDate.setMonth(startDate.getMonth() - 6);
                break;
            case '24m':
                startDate.setFullYear(startDate.getFullYear() - 2);
                break;
            default:
                throw new Error(`Unhandled period: ${period}`);
        }

        startDate.setHours(0, 0, 0, 0);

        // Validate the calculated dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('Invalid date calculation result');
        }

        if (startDate >= endDate) {
            throw new Error('Start date must be before end date');
        }

        return {
            startDate: format(startDate, 'yyyy-MM-dd'),
            endDate: format(endDate, 'yyyy-MM-dd'),
        };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new Error(`Failed to calculate date range for period "${period}": ${errorMessage}`);
    }
}
