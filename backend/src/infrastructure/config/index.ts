import dotenv from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Determine the environment
const isProduction = process.env.NODE_ENV === 'production';

const ENV_PATH = path.resolve(process.cwd(), '../', '.env');

// Add detailed logging to show which path is being checked for the .env file
console.log(`[${isProduction ? 'Production' : 'Development'} Mode] 🔍 Checking for .env file at: ${ENV_PATH}`);

// Load environment variables from the resolved path
const result = dotenv.config({ path: ENV_PATH });

if (result.error) {
  console.error('❌ Error loading .env file:', result.error);
} else {
  console.log('✅ .env file loaded successfully.');
}

// Define the configuration schema using Zod
const configSchema = z.object({
  port: z.coerce.number().default(3000),
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
  database: z.object({
    host: z.string().default('localhost'),
    port: z.coerce.number().default(3306),
    user: z.string().default('root'),
    password: z.string().default('password'),
    name: z.string().default('brokudb'),
  }),
  jwt: z.object({
    secret: z.string().min(1, 'JWT_SECRET is required'),
    expiresIn: z.string().default('7d'),
  }),
  logging: z.object({
    level: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  }),
  whatsapp: z.object({
    enabled: z.coerce.boolean().default(false),
    sessionDir: z.string().default('./whatsapp_session'),
    recipientNumber: z.string().optional(),
    pairingCode: z.object({
      phoneNumber: z.string().optional(),
    }),
  }),
});

// Parse and validate configuration
const parseConfig = (): z.infer<typeof configSchema> => {
  try {
    return configSchema.parse({
      port: process.env['PORT'],
      nodeEnv: process.env['NODE_ENV'],
      database: {
        host: process.env['DB_HOST'],
        port: process.env['DB_PORT'],
        user: process.env['DB_USER'],
        password: process.env['DB_PASSWORD'],
        name: process.env['DB_NAME'],
      },
      jwt: {
        secret: process.env['JWT_SECRET'],
        expiresIn: process.env['JWT_EXPIRES_IN'],
      },
      logging: {
        level: process.env['LOG_LEVEL'],
      },
      whatsapp: {
        enabled: process.env['WHATSAPP_ENABLED'],
        sessionDir: process.env['WHATSAPP_SESSION_DIR'],
        recipientNumber: process.env['WHATSAPP_RECIPIENT_NUMBER'],
        pairingCode: {
          phoneNumber: process.env['WHATSAPP_PAIRING_CODE_PHONE_NUMBER'],
        },
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Configuration validation failed:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
    } else {
      console.error('❌ Failed to load configuration:', error);
    }
    process.exit(1);
  }
};

export const config = parseConfig();
export type Config = typeof config;
