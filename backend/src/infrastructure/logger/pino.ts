import pino from 'pino';
import { config } from '../config';

// Create Pino logger instance
const loggerOptions: pino.LoggerOptions = {
  level: config.logging.level,
  formatters: {
    level: (label: string) => {
      return { level: label };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  base: {
    env: config.nodeEnv,
  },
};

// Disable pino-pretty for now to avoid dependency issues
// if (config.nodeEnv === 'development') {
//   loggerOptions.transport = {
//     target: 'pino-pretty',
//     options: {
//       colorize: true,
//       translateTime: 'SYS:standard',
//       ignore: 'pid,hostname',
//     },
//   };
// }

export const logger = pino(loggerOptions);

// Create child loggers for different modules
export const createModuleLogger = (module: string): pino.Logger => {
  return logger.child({ module });
};

// Export specific loggers for common use cases
export const dbLogger = createModuleLogger('database');
export const authLogger = createModuleLogger('auth');
export const apiLogger = createModuleLogger('api');
