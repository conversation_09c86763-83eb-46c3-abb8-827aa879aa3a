import { eq, and, isNull, desc, asc, sql, SQL } from 'drizzle-orm';
import { MySqlTable } from 'drizzle-orm/mysql-core';
import { db } from './client';
import { dbLogger } from '../logger/pino';

/**
 * Generic soft delete utility
 * Marks a record as deleted by setting deletedAt timestamp
 */
export async function softDelete<T extends MySqlTable>(
  table: T,
  idColumn: any,
  id: number | string
): Promise<boolean> {
  try {
    const result = await db
      .update(table)
      .set({ deletedAt: new Date() } as any)
      .where(eq(idColumn, id));

    return result[0].affectedRows > 0;
  } catch (error) {
    dbLogger.error('Error in soft delete:', error);
    throw error;
  }
}

/**
 * Generic restore utility
 * Restores a soft-deleted record by setting deletedAt to null
 */
export async function restore<T extends MySqlTable>(
  table: T,
  idColumn: any,
  id: number | string
): Promise<boolean> {
  try {
    const result = await db
      .update(table)
      .set({ deletedAt: null } as any)
      .where(eq(idColumn, id));

    return result[0].affectedRows > 0;
  } catch (error) {
    dbLogger.error('Error in restore:', error);
    throw error;
  }
}

/**
 * Generic hard delete utility
 * Permanently removes a record from the database
 */
export async function hardDelete<T extends MySqlTable>(
  table: T,
  idColumn: any,
  id: number | string
): Promise<boolean> {
  try {
    const result = await db
      .delete(table)
      .where(eq(idColumn, id));

    return result[0].affectedRows > 0;
  } catch (error) {
    dbLogger.error('Error in hard delete:', error);
    throw error;
  }
}

/**
 * Check if a record exists (excluding soft-deleted records)
 */
export async function recordExists<T extends MySqlTable>(
  table: T,
  idColumn: any,
  id: number | string,
  includeDeleted: boolean = false
): Promise<boolean> {
  try {
    const conditions = [eq(idColumn, id)];

    if (!includeDeleted) {
      conditions.push(isNull((table as any).deletedAt));
    }

    const [record] = await db
      .select({ id: idColumn })
      .from(table)
      .where(conditions.length > 1 ? and(...conditions) : conditions[0])
      .limit(1);

    return !!record;
  } catch (error) {
    dbLogger.error('Error checking record existence:', error);
    throw error;
  }
}

/**
 * Get active records (not soft-deleted)
 */
export function getActiveRecordsCondition(table: any): SQL {
  return isNull(table.deletedAt);
}

/**
 * Pagination utility
 */
export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export async function paginate<T>(
  query: any,
  countQuery: any,
  options: PaginationOptions
): Promise<PaginationResult<T>> {
  try {
    const { page, limit } = options;
    const offset = (page - 1) * limit;

    // Execute count query to get total records
    const [countResult] = await countQuery;
    const total = countResult.count || 0;

    // Execute main query with pagination
    const data = await query.limit(limit).offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    dbLogger.error('Error in pagination:', error);
    throw error;
  }
}

/**
 * Transaction wrapper utility
 */
export async function withTransaction<T>(
  callback: (tx: any) => Promise<T>
): Promise<T> {
  try {
    return await db.transaction(async (tx) => {
      return await callback(tx);
    });
  } catch (error) {
    dbLogger.error('Error in transaction:', error);
    throw error;
  }
}

/**
 * Batch insert utility
 */
export async function batchInsert<T extends MySqlTable>(
  table: T,
  data: any[],
  batchSize: number = 100
): Promise<void> {
  try {
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      await db.insert(table).values(batch);
      dbLogger.info(`Inserted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(data.length / batchSize)}`);
    }
  } catch (error) {
    dbLogger.error('Error in batch insert:', error);
    throw error;
  }
}

/**
 * Date range query helper
 */
export interface DateRangeOptions {
  startDate?: string;
  endDate?: string;
  dateColumn: any;
}

export function buildDateRangeCondition(options: DateRangeOptions): SQL[] {
  const conditions: SQL[] = [];

  if (options.startDate) {
    conditions.push(sql`${options.dateColumn} >= ${options.startDate}`);
  }

  if (options.endDate) {
    conditions.push(sql`${options.dateColumn} <= ${options.endDate}`);
  }

  return conditions;
}

/**
 * Search utility for text fields
 */
export function buildSearchCondition(searchTerm: string, columns: any[]): SQL {
  const searchConditions = columns.map(column =>
    sql`${column} LIKE ${`%${searchTerm}%`}`
  );

  return sql`(${sql.join(searchConditions, sql` OR `)})`;
}

/**
 * Order by utility
 */
export interface OrderByOptions {
  column: any;
  direction: 'asc' | 'desc';
}

export function buildOrderBy(options: OrderByOptions[]) {
  return options.map(option =>
    option.direction === 'desc' ? desc(option.column) : asc(option.column)
  );
}

/**
 * Database health check
 */
export async function healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; timestamp: Date }> {
  try {
    await db.execute(sql`SELECT 1`);
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    dbLogger.error('Database health check failed:', error);
    return { status: 'unhealthy', timestamp: new Date() };
  }
}
