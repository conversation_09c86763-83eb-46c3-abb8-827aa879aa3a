import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import { seed } from 'drizzle-seed';
import bcrypt from 'bcryptjs';
import { config } from '../config';
import { restaurants, users, sales } from '../../data/models';

// Create database connection for seeding
const connection = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

const db = drizzle(connection);

async function seedDatabaseAdvanced() {
  console.log('🌱 Starting advanced database seeding with drizzle-seed...');

  try {
    // Clear existing data (in reverse order due to foreign keys)
    console.log('🧹 Clearing existing data...');
    await db.delete(sales);
    await db.delete(users);
    await db.delete(restaurants);

    console.log('🏪 Seeding restaurants with drizzle-seed...');

    // Seed restaurants using drizzle-seed
    await seed(db, { restaurants }).refine((funcs) => ({
      restaurants: {
        count: 5,
        columns: {
          name: funcs.valuesFromArray({
            values: [
              'Broku Downtown',
              'Broku Uptown',
              'Broku Express',
              'Broku Seaside',
              'Broku Garden'
            ]
          }),
          address: funcs.valuesFromArray({
            values: [
              '123 Main Street, Downtown, NY 10001',
              '456 Broadway Avenue, Uptown, NY 10002',
              '789 Quick Street, Midtown, NY 10003',
              '321 Ocean Drive, Coastal, NY 10004',
              '555 Garden Lane, Suburbs, NY 10005'
            ]
          }),
          phoneNumber: funcs.phoneNumber(),
          openingTime: funcs.valuesFromArray({
            values: ['07:00', '08:00', '09:00', '10:00']
          }),
          closingTime: funcs.valuesFromArray({
            values: ['20:00', '21:00', '22:00', '23:00']
          }),
          isActive: funcs.boolean(), // 80% chance of being active
          description: funcs.loremIpsum({ sentencesCount: 2 }),
        }
      }
    }));

    console.log('👥 Seeding users with drizzle-seed...');

    // Hash password for all users
    const hashedPassword = await bcrypt.hash('password123', 12);

    const userEmails = Array.from({ length: 15 }, (_, i) => `user${i + 1}@example.com`);

    // Seed users using drizzle-seed
    await seed(db, { users }).refine((funcs) => ({
      users: {
        count: 15,
        columns: {
          username: funcs.string(),
          email: funcs.valuesFromArray({ values: userEmails }),
          password: funcs.default({ defaultValue: hashedPassword }),
          role: funcs.weightedRandom([
            { weight: 0.1, value: funcs.default({ defaultValue: 'admin' }) },
            { weight: 0.3, value: funcs.default({ defaultValue: 'staff' }) },
            { weight: 0.6, value: funcs.default({ defaultValue: 'user' }) }
          ]),
          fullName: funcs.fullName(),
          restaurantId: funcs.int({ minValue: 1, maxValue: 5 }),
        }
      }
    }));

    console.log('💰 Seeding sales data with drizzle-seed...');

    // Generate sales data for the last 15 months to support 12-month chart view
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 450); // ~15 months of data

    await seed(db, { sales }).refine((funcs) => ({
      sales: {
        count: 1500, // Approximately 3-4 sales per day across all restaurants for 15 months
        columns: {
          restaurantId: funcs.int({ minValue: 1, maxValue: 5 }),
          salesDate: funcs.date({
            minDate: startDate,
            maxDate: new Date()
          }),
          cashAmount: funcs.number({
            minValue: 50.00,
            maxValue: 800.00,
            precision: 2
          }),
          cardAmount: funcs.number({
            minValue: 100.00,
            maxValue: 1200.00,
            precision: 2
          }),
          onlinePaymentAmount: funcs.number({
            minValue: 25.00,
            maxValue: 500.00,
            precision: 2
          }),
          totalSales: funcs.number({
            minValue: 200.00,
            maxValue: 2500.00,
            precision: 2
          }),
          totalDiscounts: funcs.number({
            minValue: 0.00,
            maxValue: 100.00,
            precision: 2
          }),
          recordedByUserId: funcs.int({ minValue: 1, maxValue: 15 }),
          notes: funcs.loremIpsum({
            sentencesCount: 1
          }),
        }
      }
    }));

    console.log('🎉 Advanced database seeding completed successfully!');

    // Get counts for summary
    const restaurantCount = await db.select().from(restaurants);
    const userCount = await db.select().from(users);
    const salesCount = await db.select().from(sales);

    console.log('\n📊 Summary:');
    console.log(`- ${restaurantCount.length} restaurants`);
    console.log(`- ${userCount.length} users`);
    console.log(`- ${salesCount.length} sales records`);
    console.log('\n🔐 All users have password: password123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabaseAdvanced()
    .then(() => {
      console.log('✅ Advanced seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Advanced seeding process failed:', error);
      process.exit(1);
    });
}

export { seedDatabaseAdvanced };
