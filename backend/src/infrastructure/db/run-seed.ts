#!/usr/bin/env tsx

import { seedDatabase } from './seed';
import { seedDatabaseAdvanced } from './seed-advanced';

async function main() {
  const args = process.argv.slice(2);
  const seedType = args[0] || 'basic';

  console.log('🌱 Broku Sales Dashboard - Database Seeder');
  console.log('==========================================\n');

  try {
    if (seedType === 'advanced') {
      console.log('Running advanced seeding with drizzle-seed generators...\n');
      await seedDatabaseAdvanced();
    } else {
      console.log('Running basic seeding with predefined data...\n');
      await seedDatabase();
    }
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { main };
