import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import { config } from '../config';
import { dbLogger } from '../logger/pino';
import {
  users,
  restaurants,
  sales,
  auditLogs,
  usersRelations,
  restaurantsRelations,
  salesRelations,
  auditLogsRelations
} from '../../data/models';

// Create MySQL connection pool
const poolConnection = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// Create Drizzle database instance
export const db = drizzle(poolConnection, {
  logger: config.nodeEnv === 'development',
});

// Export schema for use in other parts of the application
export const schema = {
  users,
  restaurants,
  sales,
  auditLogs,
  usersRelations,
  restaurantsRelations,
  salesRelations,
  auditLogsRelations,
};

// Database connection helper
export const connectToDatabase = async (): Promise<void> => {
  try {
    // Test the connection
    const connection = await poolConnection.getConnection();
    connection.release();
    dbLogger.info('✅ Connected to MySQL database');
  } catch (error) {
    dbLogger.warn('⚠️ Failed to connect to database (server will continue without database):', error);
    // Don't exit the process - allow server to start without database
  }
};

// Graceful shutdown
export const disconnectFromDatabase = async (): Promise<void> => {
  try {
    await poolConnection.end();
    dbLogger.info('✅ Disconnected from MySQL database');
  } catch (error) {
    dbLogger.error('❌ Error disconnecting from database:', error);
  }
};

export type Database = typeof db;
