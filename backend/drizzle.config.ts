import type { Config } from 'drizzle-kit';
import dotenv from 'dotenv';
import { join } from 'path';

// Load environment variables
dotenv.config({
  path: join(__dirname, '../.env'),
});

// Check for required environment variables and provide a more informative error message.
const requiredEnv = ['DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
for (const name of requiredEnv) {
  if (!process.env[name]) {
    throw new Error(
      `Environment variable ${name} is not set. Please create a .env file based on .env.example.`,
    );
  }
}

export default {
  schema: ['./src/data/models/*.model.ts', './src/data/models/relations.ts'],
  out: './drizzle',
  dialect: 'mysql',
  dbCredentials: {
    host: process.env.DB_HOST!,
    port: Number(process.env.DB_PORT!),
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_NAME!,
  },
  verbose: true,
  strict: true,
  migrations: {
    prefix: 'timestamp',
  },
} satisfies Config;
