import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';

export interface CurrentSalesFigures {
  progress: number;
  total: number;
}
export interface KpiProgressCardProps {
  selectedPeriod: string;
  currentSalesFigures: CurrentSalesFigures;
  currentKpiTarget: number;
}
export function KpiProgressCard({
  selectedPeriod,
  currentSalesFigures,
  currentKpiTarget,
}: KpiProgressCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>KPI Progress ({selectedPeriod})</CardTitle>
        <CardDescription>Your sales progress towards the current target.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-2 flex items-center gap-4">
          <Progress
            value={currentSalesFigures.progress}
            aria-label="Restaurant performance progress"
          />
          <span className="text-base font-medium text-green-500">
            {Math.round(currentSalesFigures.progress)}%
          </span>
        </div>
        <p className="text-muted-foreground text-sm">
          Achieved: RM{' '}
          {(currentSalesFigures.total || 0).toLocaleString('en-MY', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}{' '}
          / Target: RM{' '}
          {(currentKpiTarget || 0).toLocaleString('en-MY', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </p>
      </CardContent>
    </Card>
  );
}
