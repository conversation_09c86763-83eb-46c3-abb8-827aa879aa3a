import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class DashboardErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  override render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Card className="mx-auto mt-8 w-full max-w-2xl">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Dashboard Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-muted-foreground text-sm">
              <p>Something went wrong while loading the dashboard.</p>
              {this.state.error && (
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">Error Details</summary>
                  <pre className="bg-muted mt-2 overflow-auto rounded p-2 text-xs">
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
            </div>
            <div className="flex gap-2">
              <Button onClick={this.handleRetry} variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button onClick={() => window.location.reload()} variant="default" size="sm">
                Reload Page
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Hook-based error boundary for functional components
export const useDashboardErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('Dashboard error:', error);
    setError(error);
  }, []);

  return {
    error,
    resetError,
    handleError,
    hasError: error !== null,
  };
};

// RTK Query error display component
interface QueryErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  className?: string;
}

export const QueryErrorDisplay: React.FC<QueryErrorDisplayProps> = ({
  error,
  onRetry,
  className = '',
}) => {
  const getErrorMessage = (error: any): string => {
    if (error?.data?.message) {
      return error.data.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (error?.status) {
      switch (error.status) {
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'You do not have permission to access this data.';
        case 404:
          return 'The requested data was not found.';
        case 500:
          return 'Server error. Please try again later.';
        case 'FETCH_ERROR':
          return 'Network error. Please check your connection.';
        case 'TIMEOUT_ERROR':
          return 'Request timed out. Please try again.';
        default:
          return `Error ${error.status}: Something went wrong.`;
      }
    }
    return 'An unexpected error occurred.';
  };

  return (
    <Card className={`border-destructive/50 ${className}`}>
      <CardContent className="pt-6">
        <div className="flex items-start gap-3">
          <AlertTriangle className="text-destructive mt-0.5 h-5 w-5" />
          <div className="flex-1 space-y-2">
            <p className="text-destructive text-sm font-medium">Failed to load data</p>
            <p className="text-muted-foreground text-sm">{getErrorMessage(error)}</p>
            {onRetry && (
              <Button onClick={onRetry} variant="outline" size="sm" className="mt-2">
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Loading skeleton component
export const DashboardLoadingSkeleton: React.FC = () => {
  return (
    <div className="animate-pulse space-y-6">
      {/* KPI Progress Card Skeleton */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="bg-muted h-4 w-1/3 rounded"></div>
            <div className="bg-muted h-8 w-1/2 rounded"></div>
            <div className="bg-muted h-2 w-full rounded"></div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <div className="bg-muted h-4 w-2/3 rounded"></div>
                <div className="bg-muted h-6 w-1/2 rounded"></div>
                <div className="bg-muted h-3 w-3/4 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-5">
        <Card className="lg:col-span-3">
          <CardHeader>
            <div className="bg-muted h-5 w-1/3 rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="bg-muted h-64 rounded"></div>
          </CardContent>
        </Card>
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="bg-muted h-5 w-1/3 rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="bg-muted h-64 rounded"></div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Cards Skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="bg-muted h-5 w-1/2 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="bg-muted h-4 w-full rounded"></div>
                <div className="bg-muted h-4 w-3/4 rounded"></div>
                <div className="bg-muted h-4 w-1/2 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
