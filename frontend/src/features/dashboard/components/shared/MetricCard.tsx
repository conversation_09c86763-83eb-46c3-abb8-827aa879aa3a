import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown } from 'lucide-react';
import React from 'react';

// Define a clear and flexible props interface
export interface MetricCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  /** A pre-formatted string representing the change, e.g., "+19.5%" or "-100" */
  change?: string;
  /** A description for the change, e.g., "from last month" */
  changeDescription?: string;
  /** An additional description line at the bottom of the card */
  description?: string;
  /** Renders the card in a disabled/loading state */
  disabled?: boolean;
  className?: string;
}

export function MetricCard({
  title,
  value,
  icon,
  change,
  changeDescription,
  description,
  disabled = false,
  className,
}: MetricCardProps) {
  // Handle the disabled state first
  if (disabled) {
    return (
      <Card className={cn('opacity-50', className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {icon && <div className="text-muted-foreground h-4 w-4">{icon}</div>}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">-</div>
          <p className="text-muted-foreground text-xs">Data not available</p>
        </CardContent>
      </Card>
    );
  }

  const isPositiveChange = change?.startsWith('+');
  const isNegativeChange = change?.startsWith('-');

  return (
    <Card className={cn(className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon && <div className="text-muted-foreground h-4 w-4">{icon}</div>}
      </CardHeader>
      <CardContent>
        {/* Main Value */}
        <div className="text-2xl font-bold">{value}</div>

        {/* Change Indicator */}
        {change && (
          <div className="text-muted-foreground flex items-center text-xs">
            <span
              className={cn(
                'mr-1 flex items-center gap-1',
                isPositiveChange && 'text-green-500',
                isNegativeChange && 'text-red-500'
              )}
            >
              {isPositiveChange && <TrendingUp className="h-4 w-4" />}
              {isNegativeChange && <TrendingDown className="h-4 w-4" />}
              {change}
            </span>
            {changeDescription && <span>{changeDescription}</span>}
          </div>
        )}

        {/* Optional bottom description */}
        {description && <p className="text-muted-foreground mt-2 text-xs">{description}</p>}
      </CardContent>
    </Card>
  );
}
