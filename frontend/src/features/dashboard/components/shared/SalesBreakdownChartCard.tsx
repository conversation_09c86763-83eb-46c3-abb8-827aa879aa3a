import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import type { SalesBreakdownChartCardProps } from '../../types';

export function SalesBreakdownChartCard({
  breakdownView,
  onBreakdownViewChange,
  displayedSalesBreakdownData,
  breakdownOptions,
  pieInnerRadius,
  pieOuterRadius,
}: SalesBreakdownChartCardProps) {
  const dynamicColors = [
    '#8884d8',
    '#82ca9d',
    '#ffc658',
    '#ff8042',
    '#0088FE',
    '#00C49F',
    '#FFBB28',
    '#FF8042',
  ]; // Example colors
  const ariaLabel = 'Sales breakdown chart';
  const shouldShowLegend = true;
  const shouldShowLabels = true;

  // Custom label renderer with improved accessibility
  const renderCustomLabel = ({
    name,
    percent,
    x,
    y,
    midAngle,
    outerRadius: pieOuterRadius,
  }: any) => {
    // Only show labels when accessibility hook recommends it and for segments larger than 5%
    if (!shouldShowLabels || percent < 0.05) return null;

    const RADIAN = Math.PI / 180;
    const sin = Math.sin(-RADIAN * midAngle);
    const cos = Math.cos(-RADIAN * midAngle);
    const sx = x + (pieOuterRadius + 10) * cos;
    const sy = y + (pieOuterRadius + 10) * sin;
    const ex = x + (pieOuterRadius + 25) * cos;
    const ey = y + (pieOuterRadius + 25) * sin;
    const textAnchor = cos >= 0 ? 'start' : 'end';

    return (
      <g>
        <path
          d={`M${sx},${sy}L${ex},${ey}`}
          stroke="hsl(var(--muted-foreground))"
          strokeWidth={1}
          fill="none"
        />
        <circle cx={ex} cy={ey} r={2} fill="hsl(var(--muted-foreground))" stroke="none" />
        <text
          x={ex + (cos >= 0 ? 1 : -1) * 8}
          y={ey}
          textAnchor={textAnchor}
          fill="hsl(var(--foreground))"
          fontSize={11}
          fontWeight={500}
          className="select-none"
        >
          {`${name}`}
        </text>
        <text
          x={ex + (cos >= 0 ? 1 : -1) * 8}
          y={ey + 12}
          textAnchor={textAnchor}
          fill="hsl(var(--muted-foreground))"
          fontSize={10}
          fontWeight={400}
          className="select-none"
        >
          {`${(percent * 100).toFixed(1)}%`}
        </text>
      </g>
    );
  };

  // Enhanced tooltip formatter
  const formatTooltip = (value: number, name: string, props: any) => {
    const percentage = Number(value).toFixed(1);
    const payload = props.payload;

    return [
      <div key="tooltip-content" className="space-y-1">
        <div className="text-foreground font-medium">{name}</div>
        <div className="text-muted-foreground text-sm">{percentage}% of total</div>
        {payload.rawValue && (
          <div className="text-muted-foreground text-xs">
            Value:{' '}
            {typeof payload.rawValue === 'number'
              ? payload.rawValue.toLocaleString()
              : payload.rawValue}
          </div>
        )}
      </div>,
      '',
    ];
  };

  return (
    <Card className="lg:col-span-2">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold">Sales Breakdown</CardTitle>
        <Select value={breakdownView} onValueChange={onBreakdownViewChange}>
          <SelectTrigger className="h-9 w-auto text-xs md:w-[180px]">
            <SelectValue placeholder="Select view" />
          </SelectTrigger>
          <SelectContent>
            {breakdownOptions.map((option: string) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {displayedSalesBreakdownData.length > 0 ? (
          <div className="space-y-4">
            {/* Chart Container */}
            <div
              className="h-80"
              role="img"
              aria-label={ariaLabel}
              aria-describedby="chart-description"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={displayedSalesBreakdownData}
                    cx="50%"
                    cy="50%"
                    innerRadius={pieInnerRadius}
                    outerRadius={pieOuterRadius}
                    paddingAngle={2}
                    dataKey="value"
                    labelLine={false}
                    label={renderCustomLabel}
                    stroke="hsl(var(--background))"
                    strokeWidth={2}
                  >
                    {displayedSalesBreakdownData.map(
                      (entry: { name: string; value: number }, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={dynamicColors[index % dynamicColors.length]}
                          className="transition-opacity duration-200 hover:opacity-80"
                          style={{
                            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))',
                          }}
                          tabIndex={0}
                          role="button"
                          aria-label={`${entry.name}: ${entry.value.toFixed(1)}% of total`}
                        />
                      )
                    )}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'hsl(var(--popover))',
                      borderColor: 'hsl(var(--border))',
                      borderRadius: 'var(--radius)',
                      boxShadow:
                        '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                      border: '1px solid hsl(var(--border))',
                      fontSize: '12px',
                    }}
                    labelStyle={{
                      color: 'hsl(var(--popover-foreground))',
                      fontWeight: 500,
                    }}
                    formatter={formatTooltip}
                    cursor={{ fill: 'transparent' }}
                  />
                </PieChart>
              </ResponsiveContainer>

              {/* Hidden description for screen readers */}
              <div id="chart-description" className="sr-only">
                Sales breakdown chart showing {displayedSalesBreakdownData.length} categories:{' '}
                {displayedSalesBreakdownData
                  .map(
                    (item: { name: string; value: number }) =>
                      `${item.name} at ${item.value.toFixed(1)} percent`
                  )
                  .join(', ')}
              </div>
            </div>

            {/* Legend for better accessibility */}
            {shouldShowLegend && (
              <div
                className="grid grid-cols-2 gap-2 text-sm md:grid-cols-3 lg:grid-cols-4"
                role="list"
                aria-label="Chart legend"
              >
                {displayedSalesBreakdownData.map(
                  (entry: { name: string; value: number }, index: number) => (
                    <div
                      key={`legend-${index}`}
                      className="chart-legend-item hover:bg-muted/50 flex items-center gap-2 rounded-md p-2 transition-colors duration-200"
                      role="listitem"
                      tabIndex={0}
                      aria-label={`${entry.name}: ${entry.value.toFixed(1)}% of total sales`}
                    >
                      <div
                        className="h-3 w-3 flex-shrink-0 rounded-full"
                        style={{ backgroundColor: dynamicColors[index % dynamicColors.length] }}
                        aria-hidden="true"
                      />
                      <div className="min-w-0 flex-1">
                        <div className="text-foreground truncate font-medium">{entry.name}</div>
                        <div className="text-muted-foreground text-xs">
                          {entry.value.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="text-muted-foreground flex h-80 items-center justify-center">
            <div className="space-y-2 text-center">
              <div className="text-lg">📊</div>
              <div>No data available for this view.</div>
              <div className="text-sm">Try selecting a different breakdown option.</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
