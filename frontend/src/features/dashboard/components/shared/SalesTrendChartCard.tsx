import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import type { SalesTrendChartCardProps } from '../../types';
import { ChartWidget } from './ChartWidget';
export function SalesTrendChartCard({
  salesTrendView,
  onSalesTrendViewChange,
  displayedSalesTrendData,
  trendOptions,
}: SalesTrendChartCardProps) {
  return (
    <Card className="lg:col-span-3">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold">Sales Trend</CardTitle>
        <Select value={salesTrendView} onValueChange={onSalesTrendViewChange}>
          <SelectTrigger className="h-9 w-auto text-xs md:w-[160px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            {trendOptions.map((option: string) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {displayedSalesTrendData.length > 0 ? (
          <ChartWidget
            type="line"
            xAxisKey="name"
            data={displayedSalesTrendData}
            dataKeys={['value']}
            colors={['hsl(var(--primary))']}
            title=""
          />
        ) : (
          <div className="text-muted-foreground flex h-80 items-center justify-center">
            No data available for this period.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
