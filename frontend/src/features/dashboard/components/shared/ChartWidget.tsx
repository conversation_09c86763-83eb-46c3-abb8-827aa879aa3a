import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Pie,
  Cell,
} from 'recharts';

// Default colors, can be overridden by props
const DEFAULT_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface AdvancedChartWidgetProps<T> {
  title: string;
  description?: string;
  data: T[];
  type: 'line' | 'bar' | 'pie';
  // The key on the data object for the X-axis label (e.g., 'month', 'date')
  xAxisKey: keyof T & string;
  // An array of keys for the data series to be plotted
  dataKeys: (keyof T & string)[];
  colors?: string[];
  className?: string;
}

export function ChartWidget<T extends Record<string, any>>({
  title,
  description,
  data,
  type,
  xAxisKey,
  dataKeys,
  colors = DEFAULT_COLORS,
  className,
}: AdvancedChartWidgetProps<T>) {
  // A single, beautifully styled tooltip for all chart types, explicitly typed as React.ReactElement
  const CustomTooltip: React.ReactElement = (
    <Tooltip
      contentStyle={{
        backgroundColor: 'hsl(var(--popover))',
        borderColor: 'hsl(var(--border))',
        borderRadius: 'var(--radius)',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      }}
      labelStyle={{ color: 'hsl(var(--popover-foreground))' }}
      cursor={{ fill: 'hsl(var(--accent))', opacity: 0.5 }}
    />
  );

  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey={xAxisKey}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            {CustomTooltip}
            <Legend />
            {dataKeys.map((key, index) => (
              <Line
                key={key}
                type="monotone"
                dataKey={key}
                stroke={colors[index % colors.length]}
                activeDot={{ r: 6 }}
                strokeWidth={2}
              />
            ))}
          </LineChart>
        );

      case 'bar':
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey={xAxisKey}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            {CustomTooltip}
            <Legend />
            {dataKeys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                fill={colors[index % colors.length]}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </BarChart>
        );

      case 'pie':
        // For Pie charts, we typically visualize one primary data series against categories.
        // We will use the first key from `dataKeys` as the value, and `xAxisKey` as the name.
        const pieDataKey = dataKeys[0];
        return (
          <PieChart>
            {CustomTooltip}
            <Pie
              data={data}
              dataKey={pieDataKey}
              nameKey={xAxisKey}
              cx="50%"
              cy="50%"
              outerRadius={100}
              fill="#8884d8"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((_, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
          </PieChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {(() => {
          const chart = renderChart();
          return chart ? (
            <ResponsiveContainer width="100%" height={300}>
              {chart}
            </ResponsiveContainer>
          ) : null;
        })()}
      </CardContent>
    </Card>
  );
}
