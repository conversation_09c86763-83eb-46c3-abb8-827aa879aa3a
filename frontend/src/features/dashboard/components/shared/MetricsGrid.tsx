import { MetricCard, MetricCardProps } from './MetricCard';

interface MetricsGridProps {
  metrics: MetricCardProps[]; // Use the props from our new unified card
  cols?: 1 | 2 | 3 | 4;
}

export const MetricsGrid = ({ metrics, cols = 4 }: MetricsGridProps) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={`mb-8 grid gap-4 ${gridClasses[cols]}`}>
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};
