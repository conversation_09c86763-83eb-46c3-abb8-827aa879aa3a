import { useAuth } from '@/features/auth/hooks/useAuth';
import { AdminDashboardPage } from './AdminDashboardPage';
import { UserDashboardPage } from './UserDashboardPage';
import StaffDashboardPage from './StaffDashboardPage';

export function DashboardPage() {
  const { user } = useAuth();

  if (user?.role === 'admin') {
    return <AdminDashboardPage />;
  }
  if (user?.role === 'staff') {
    return <StaffDashboardPage />;
  }
  return <UserDashboardPage />;
}
