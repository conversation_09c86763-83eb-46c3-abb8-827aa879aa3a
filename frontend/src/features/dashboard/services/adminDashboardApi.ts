import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  AdminDashboardData,
  AdminDashboardResponse,
  DashboardMetricsResponse,
  RecentActivityResponse,
  SalesTrendResponse,
  AdminDashboardQuery,
  RecentActivityQuery,
} from '../types';

export const adminDashboardApi = {
  /**
   * Get complete admin dashboard data
   */
  getDashboardData: async (query?: AdminDashboardQuery): Promise<AdminDashboardData> => {
    const params = new URLSearchParams();
    if (query?.period) params.append('period', query.period);
    if (query?.includeDetails !== undefined)
      params.append('includeDetails', query.includeDetails.toString());
    if (query?.limit) params.append('limit', query.limit.toString());

    const url = params.toString()
      ? `${API_ENDPOINTS.DASHBOARD.ADMIN}?${params.toString()}`
      : API_ENDPOINTS.DASHBOARD.ADMIN;

    const response = await apiClient.get<AdminDashboardResponse>(url);
    return response.data.data;
  },

  /**
   * Get dashboard metrics summary
   */
  getDashboardMetrics: async (
    query?: AdminDashboardQuery
  ): Promise<{ metrics: any; userActivity: any[] }> => {
    const params = new URLSearchParams();
    if (query?.period) params.append('period', query.period);
    if (query?.includeDetails !== undefined)
      params.append('includeDetails', query.includeDetails.toString());
    if (query?.limit) params.append('limit', query.limit.toString());

    const url = params.toString()
      ? `${API_ENDPOINTS.DASHBOARD.METRICS}?${params.toString()}`
      : API_ENDPOINTS.DASHBOARD.METRICS;

    const response = await apiClient.get<DashboardMetricsResponse>(url);
    return response.data.data;
  },

  /**
   * Get recent activity data
   */
  getRecentActivity: async (
    query?: RecentActivityQuery
  ): Promise<{ activities: any[]; total: number }> => {
    const params = new URLSearchParams();
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.type) params.append('type', query.type);
    if (query?.restaurantId) params.append('restaurantId', query.restaurantId.toString());

    const url = params.toString()
      ? `${API_ENDPOINTS.DASHBOARD.ACTIVITY}?${params.toString()}`
      : API_ENDPOINTS.DASHBOARD.ACTIVITY;

    const response = await apiClient.get<RecentActivityResponse>(url);
    return response.data.data;
  },

  /**
   * Get sales trend data for charts
   */
  getSalesTrend: async (
    query?: AdminDashboardQuery
  ): Promise<{ salesTrend: any[]; restaurantPerformance: any[] }> => {
    const params = new URLSearchParams();
    if (query?.period) params.append('period', query.period);
    if (query?.includeDetails !== undefined)
      params.append('includeDetails', query.includeDetails.toString());
    if (query?.limit) params.append('limit', query.limit.toString());

    const url = params.toString()
      ? `${API_ENDPOINTS.DASHBOARD.SALES_TREND}?${params.toString()}`
      : API_ENDPOINTS.DASHBOARD.SALES_TREND;

    const response = await apiClient.get<SalesTrendResponse>(url);
    return response.data.data;
  },
};
