import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import { OverallRevenueData, BranchAnalytics, KpiProgress } from '../types';

export const analysisApi = {
  getDashboardAnalytics: async (params: {
    period: string;
    includeForecasts: boolean;
  }): Promise<OverallRevenueData> => {
    const response = await apiClient.get(API_ENDPOINTS.ANALYSIS.DASHBOARD, { params });
    return response.data;
  },

  getBranchAnalytics: async (restaurantId: number): Promise<BranchAnalytics> => {
    const response = await apiClient.get(`${API_ENDPOINTS.ANALYSIS.BRANCH}/${restaurantId}`);
    return response.data;
  },

  getKpiProgress: async (restaurantId: number): Promise<KpiProgress> => {
    const response = await apiClient.get(`${API_ENDPOINTS.ANALYSIS.KPI}/${restaurantId}`);
    return response.data;
  },
};
