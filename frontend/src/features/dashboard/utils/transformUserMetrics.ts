import React from 'react';
import { DollarSign, Users, CreditCard, TrendingUp } from 'lucide-react';
import type { UserDashboardData } from '../types';
import type { MetricCardProps } from '../components/shared/MetricCard';

/**
 * Formats a number as currency
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

/**
 * Safely gets a numeric value with fallback
 */
function safeNumber(value: number | undefined | null, fallback: number = 0): number {
  return typeof value === 'number' && !isNaN(value) ? value : fallback;
}

/**
 * Safely calculates percentage change with fallback for missing data
 */
function safePercentageChange(current: number, previous: number | undefined | null): string {
  const safeCurrent = safeNumber(current);
  const safePrevious = safeNumber(previous);

  if (safePrevious === 0) return '+0%';

  const change = ((safeCurrent - safePrevious) / safePrevious) * 100;
  const sign = change >= 0 ? '+' : '';
  return `${sign}${change.toFixed(1)}%`;
}

/**
 * Transforms UserDashboardData into MetricCardProps array
 */
export function transformUserDashboardToMetrics(data: UserDashboardData): MetricCardProps[] {
  const metrics: MetricCardProps[] = [];

  // Safely extract performance data with fallbacks
  const performance = data.performance || {};
  const thisWeek = safeNumber(performance.thisWeek);
  const lastWeek = safeNumber(performance.lastWeek);
  const monthlySales = safeNumber(data.monthlySales);
  const weeklySales = safeNumber(data.weeklySales);
  const todaySales = safeNumber(data.todaySales);
  const targetAchievement = safeNumber(data.targetAchievement);
  const salesTarget = safeNumber(data.salesTarget);

  // Monthly Revenue (from monthlySales)
  metrics.push({
    title: 'Monthly Revenue',
    value: formatCurrency(monthlySales),
    icon: React.createElement(DollarSign),
    change: safePercentageChange(monthlySales, lastWeek * 4), // Approximate monthly from weekly
    changeDescription: 'from last month',
  });

  // Weekly Sales Performance
  metrics.push({
    title: 'Weekly Sales',
    value: formatCurrency(weeklySales),
    icon: React.createElement(TrendingUp),
    change: safePercentageChange(thisWeek, lastWeek),
    changeDescription: 'from last week',
  });

  // Daily Average (calculated from weekly sales)
  const dailyAverage = weeklySales / 7;
  metrics.push({
    title: 'Daily Average',
    value: formatCurrency(dailyAverage),
    icon: React.createElement(CreditCard),
    change: safePercentageChange(todaySales, dailyAverage),
    changeDescription: 'vs daily average',
  });

  // Target Achievement
  metrics.push({
    title: 'Target Achievement',
    value: `${targetAchievement}%`,
    icon: React.createElement(Users),
    change:
      targetAchievement >= 100
        ? '+' + (targetAchievement - 100).toFixed(1) + '%'
        : '-' + (100 - targetAchievement).toFixed(1) + '%',
    changeDescription: 'of monthly target',
    disabled: salesTarget === 0,
  });

  return metrics;
}

/**
 * Alternative transformation with different metrics focus
 */
export function transformUserDashboardToMetricsAlternative(
  data: UserDashboardData
): MetricCardProps[] {
  const metrics: MetricCardProps[] = [];

  // Safely extract data with fallbacks
  const performance = data.performance || {};
  const changeDirection = performance.changeDirection || 'neutral';
  const monthlySales = safeNumber(data.monthlySales);
  const weeklySales = safeNumber(data.weeklySales);
  const todaySales = safeNumber(data.todaySales);
  const targetAchievement = safeNumber(data.targetAchievement);
  const salesTarget = safeNumber(data.salesTarget);

  // Today's Sales
  metrics.push({
    title: "Today's Sales",
    value: formatCurrency(todaySales),
    icon: React.createElement(DollarSign),
    change: safePercentageChange(todaySales, weeklySales / 7),
    changeDescription: 'vs daily average',
  });

  // Weekly Performance
  metrics.push({
    title: 'Weekly Performance',
    value: formatCurrency(weeklySales),
    icon: React.createElement(TrendingUp),
    change: changeDirection === 'up' ? '+' : changeDirection === 'down' ? '-' : '',
    changeDescription: 'from last week',
  });

  // Monthly Progress
  metrics.push({
    title: 'Monthly Progress',
    value: formatCurrency(monthlySales),
    icon: React.createElement(CreditCard),
    change: `${targetAchievement}%`,
    changeDescription: 'of target achieved',
  });

  // Sales Target
  metrics.push({
    title: 'Monthly Target',
    value: formatCurrency(salesTarget),
    icon: React.createElement(Users),
    changeDescription: 'target amount',
    disabled: salesTarget === 0,
  });

  return metrics;
}
