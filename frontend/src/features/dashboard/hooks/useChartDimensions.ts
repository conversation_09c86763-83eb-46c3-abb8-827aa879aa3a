import { useState, useEffect } from 'react';

interface ChartDimensions {
  pieInnerRadius: number;
  pieOuterRadius: number;
}

export const useChartDimensions = (): ChartDimensions => {
  const [dimensions, setDimensions] = useState({
    pieInnerRadius: 60,
    pieOuterRadius: 90,
  });

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setDimensions({
          pieInnerRadius: 40,
          pieOuterRadius: 60,
        });
      } else if (window.innerWidth < 1024) {
        setDimensions({
          pieInnerRadius: 50,
          pieOuterRadius: 75,
        });
      } else {
        setDimensions({
          pieInnerRadius: 60,
          pieOuterRadius: 90,
        });
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial setup

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return dimensions;
};
