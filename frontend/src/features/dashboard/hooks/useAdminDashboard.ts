import { useQuery } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { analysisApi } from '../services/analysisApi';
import { adminDashboardApi } from '../services/adminDashboardApi';
import {
  OverallRevenueData,
  BranchAnalytics,
  KpiProgress,
  AdminDashboardData,
  AdminDashboardQuery,
  RecentActivityQuery,
} from '../types';

const STALE_TIME = 5 * 60 * 1000; // 5 minutes
const RETRY_COUNT = 3;

export function useDashboardAnalytics(period: string = '12m', includeForecasts: boolean = true) {
  return useQuery<OverallRevenueData, Error>({
    queryKey: [QUERY_KEYS.DASHBOARD, 'analytics', period, includeForecasts],
    queryFn: () => analysisApi.getDashboardAnalytics({ period, includeForecasts }),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
}

export function useBranchAnalytics(restaurantId: number, options?: { enabled?: boolean }) {
  return useQuery<BranchAnalytics, Error>({
    queryKey: [QUERY_KEYS.DASHBOARD, 'branch', restaurantId],
    queryFn: () => analysisApi.getBranchAnalytics(restaurantId),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
    enabled: options?.enabled,
  });
}

export function useKpiProgress(restaurantId: number, options?: { enabled?: boolean }) {
  return useQuery<KpiProgress, Error>({
    queryKey: [QUERY_KEYS.DASHBOARD, 'kpi', restaurantId],
    queryFn: () => analysisApi.getKpiProgress(restaurantId),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
    enabled: options?.enabled,
  });
}

export function useAdminDashboard(query?: AdminDashboardQuery) {
  return useQuery<AdminDashboardData, Error>({
    queryKey: [QUERY_KEYS.DASHBOARD, 'admin', query],
    queryFn: () => adminDashboardApi.getDashboardData(query),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
}

export function useDashboardMetrics(query?: AdminDashboardQuery) {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'metrics', query],
    queryFn: () => adminDashboardApi.getDashboardMetrics(query),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
}

export function useRecentActivity(query?: RecentActivityQuery) {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'activity', query],
    queryFn: () => adminDashboardApi.getRecentActivity(query),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
}

export function useSalesTrend(query?: AdminDashboardQuery) {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'sales-trend', query],
    queryFn: () => adminDashboardApi.getSalesTrend(query),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
  });
}
