# Sales Entry Flow Modifications

## Summary of Changes

The sales entry flow has been successfully modified to handle automatic calculation of offline and online sales totals based on payment method inputs, as requested.

## Modified Flow Structure

### New Step Sequence:

1. **Step 1: Payment Breakdown** - User inputs payment method amounts
2. **Step 2: Cash Management** - User inputs float and cash-related entries
3. **Step 3: Sales Summary** - Automatically calculated offline/online sales totals
4. **Step 4: Review and Submit** - Final review

### Key Changes Made:

#### 1. Section Reordering (`reportSections.ts`)

- Moved "Payment Breakdown" from step 3 to step 1
- Moved "Cash Management" from step 4 to step 2
- Moved "Sales Summary" from step 2 to step 3
- Updated Sales Summary description to reflect automatic calculations

#### 2. Sales Summary Fields Made Calculated (`reportSections.ts`)

- `offlineSales`: Now calculated automatically (Cash + Visa payments)
- `onlineSales`: Now calculated automatically (QR + Online Transaction payments)
- `totalSales`: Remains calculated (Offline + Online sales)
- Added `isCalculated: true` and descriptive text for these fields

#### 3. Automatic Calculations (`useRestaurantReportForm.ts`)

Added three new useEffect hooks for real-time calculations:

```typescript
// Offline Sales = Cash + Visa Payments
useEffect(() => {
  const cash = parseNum(formData.cashPayment);
  const visa = parseNum(formData.visaPayment);
  const offline = cash + visa;
  setFormData(prev => ({ ...prev, offlineSales: offline === 0 ? '' : offline }));
}, [formData.cashPayment, formData.visaPayment, parseNum]);

// Online Sales = QR + Online Transaction Payments
useEffect(() => {
  const qr = parseNum(formData.qrPayment);
  const onlineTransaction = parseNum(formData.onlineTransactionPayment);
  const online = qr + onlineTransaction;
  setFormData(prev => ({ ...prev, onlineSales: online === 0 ? '' : online }));
}, [formData.qrPayment, formData.onlineTransactionPayment, parseNum]);

// Total Sales = Offline + Online Sales (existing, unchanged)
```

#### 4. Field Name Consistency Updates

- Updated field names from `openingHours`/`closingHours` to `openTime`/`closeTime`
- Updated FormData interface, validation logic, and component references
- Maintained backward compatibility in hook return values

#### 5. Validation Logic (`validation.ts`)

- Existing validation already properly handles calculated fields by skipping them
- Updated cross-field validation to use new field names
- No changes needed for payment validation logic

## Expected User Experience

### Payment Input (Step 1)

User enters:

- Cash Payments: RM 355.80
- Visa Payments: RM 1,424.30
- QR Code Payments: RM 1,690.55
- Online Transactions: RM 200.00

### Automatic Calculations (Step 3)

System automatically calculates and displays:

- **Offline Sales**: RM 1,780.10 (355.80 + 1,424.30)
- **Online Sales**: RM 1,890.55 (1,690.55 + 200.00)
- **Total Sales**: RM 3,670.65 (1,780.10 + 1,890.55)

### User Interface

- Payment breakdown fields remain editable
- Sales summary fields are read-only with calculated values
- Calculations update in real-time as payment amounts change
- Clear visual indication that fields are auto-calculated
- Descriptive help text explains calculation formulas

## Files Modified

1. `frontend/src/features/reports/utils/reportSections.ts` - Section reordering and field configuration
2. `frontend/src/features/reports/hooks/useRestaurantReportForm.ts` - Automatic calculation logic
3. `frontend/src/features/reports/types/index.ts` - Field name updates
4. `frontend/src/features/reports/utils/validation.ts` - Field name consistency
5. `frontend/src/features/reports/pages/SalesEntryPage.tsx` - Field name references

## Testing

The application is running successfully at http://localhost:5174/ with hot-reloading enabled. All TypeScript compilation errors have been resolved.

## Backward Compatibility

- API types updated to match new field names
- Hook return values maintain compatibility for existing consumers
- Validation logic properly handles calculated fields
- No breaking changes to existing functionality
