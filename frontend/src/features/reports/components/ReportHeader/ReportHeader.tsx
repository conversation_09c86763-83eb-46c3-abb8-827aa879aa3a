import React from 'react';
import { ArrowLeft, Building2 } from 'lucide-react';

interface ReportHeaderProps {
  currentStep: number;
  totalSteps: number;
  restaurantName?: string;
  onBackToSelection?: () => void;
}

/**
 * Header component showing progress and title
 */
export const ReportHeader: React.FC<ReportHeaderProps> = ({
  currentStep,
  totalSteps,
  restaurantName,
  onBackToSelection,
}) => {
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  return (
    <div className="mb-8 rounded-2xl bg-white p-8 shadow-lg">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="mb-2 text-3xl font-bold text-gray-800">Daily Restaurant Report</h1>
          {restaurantName && (
            <div className="mb-2 flex items-center text-blue-600">
              <Building2 className="mr-2 h-5 w-5" />
              <span className="text-lg font-semibold">{restaurantName}</span>
            </div>
          )}
          <p className="text-gray-600">Complete your end-of-day sales and cash reconciliation</p>
        </div>

        {onBackToSelection && (
          <button
            onClick={onBackToSelection}
            className="flex items-center rounded-lg px-3 py-2 text-gray-600 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-800"
          >
            <ArrowLeft className="mr-1 h-4 w-4" />
            <span className="text-sm">Change Restaurant</span>
          </button>
        )}
      </div>

      <div className="mt-6">
        <div className="mb-2 flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Progress</span>
          <span className="text-sm font-medium text-gray-700">
            {currentStep + 1} of {totalSteps}
          </span>
        </div>
        <div className="h-2.5 w-full rounded-full bg-gray-200">
          <div
            className="h-2.5 rounded-full bg-blue-500 transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
};
