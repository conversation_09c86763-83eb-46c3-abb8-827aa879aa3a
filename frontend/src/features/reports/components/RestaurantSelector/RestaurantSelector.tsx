import React from 'react';
import { Building2, Clock, MapPin, Phone } from 'lucide-react';
import { Restaurant } from '@/features/restaurant-management/types';

interface RestaurantSelectorProps {
  restaurants: Restaurant[];
  selectedRestaurant: Restaurant | null;
  isLoading: boolean;
  error: string | null;
  onSelectRestaurant: (restaurant: Restaurant) => void;
  onProceed: () => void;
}

/**
 * Restaurant Selection Component
 * Allows users to select a restaurant before creating a report
 */
const RestaurantSelector: React.FC<RestaurantSelectorProps> = ({
  restaurants,
  selectedRestaurant,
  isLoading,
  error,
  onSelectRestaurant,
  onProceed,
}) => {
  if (isLoading) {
    return (
      <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
        <div className="rounded-lg bg-white p-8 shadow-sm">
          <div className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="text-gray-600">Loading restaurants...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
        <div className="rounded-lg bg-white p-8 shadow-sm">
          <div className="text-center">
            <div className="mb-4 text-red-500">
              <Building2 className="mx-auto h-12 w-12" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-900">Error Loading Restaurants</h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
      <div className="rounded-lg bg-white p-8 shadow-sm">
        <div className="mb-8 text-center">
          <Building2 className="mx-auto mb-4 h-12 w-12 text-blue-600" />
          <h1 className="mb-2 text-2xl font-bold text-gray-900">Select Restaurant</h1>
          <p className="text-gray-600">
            Choose the restaurant for which you want to create a report
          </p>
        </div>

        {restaurants.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-500">No active restaurants found.</p>
          </div>
        ) : (
          <div className="mb-8 space-y-4">
            {restaurants.map(restaurant => (
              <div
                key={restaurant.restaurantId}
                className={`cursor-pointer rounded-lg border p-4 transition-all duration-200 ${
                  selectedRestaurant?.restaurantId === restaurant.restaurantId
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => onSelectRestaurant(restaurant)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="mb-2 text-lg font-semibold text-gray-900">{restaurant.name}</h3>

                    <div className="grid grid-cols-1 gap-3 text-sm text-gray-600 md:grid-cols-2">
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4 text-gray-400" />
                        <span>{restaurant.address}</span>
                      </div>

                      {(restaurant.phoneNumber || restaurant.phone) && (
                        <div className="flex items-center">
                          <Phone className="mr-2 h-4 w-4 text-gray-400" />
                          <span>{restaurant.phoneNumber || restaurant.phone}</span>
                        </div>
                      )}

                      {restaurant.openingHours && restaurant.closingHours && (
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-gray-400" />
                          <span>
                            {restaurant.openingHours} - {restaurant.closingHours}
                          </span>
                        </div>
                      )}
                    </div>

                    {restaurant.description && (
                      <p className="mt-2 text-sm text-gray-500">{restaurant.description}</p>
                    )}
                  </div>

                  <div className="ml-4">
                    <div
                      className={`h-4 w-4 rounded-full border-2 ${
                        selectedRestaurant?.restaurantId === restaurant.restaurantId
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {selectedRestaurant?.restaurantId === restaurant.restaurantId && (
                        <div className="h-full w-full scale-50 rounded-full bg-white"></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedRestaurant && (
          <div className="text-center">
            <button
              type="button"
              onClick={onProceed}
              className="rounded-lg bg-blue-600 px-8 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700"
            >
              Continue with {selectedRestaurant.name}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RestaurantSelector;
