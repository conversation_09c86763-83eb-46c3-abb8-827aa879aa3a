import React from 'react';
import { ChevronRight, AlertCircle } from 'lucide-react';
import type { Section } from '../../types';

interface ReportSidebarProps {
  sections: Section[];
  currentStep: number;
  completedSections: Set<number>;
  errorSections: Set<number>;
  goToSection: (index: number) => void;
}

/**
 * Sidebar component for navigation between form sections
 */
export const ReportSidebar: React.FC<ReportSidebarProps> = ({
  sections,
  currentStep,
  completedSections,
  errorSections,
  goToSection,
}) => {
  return (
    <div className="sticky top-6 rounded-2xl bg-white p-6 shadow-lg">
      <h3 className="mb-4 text-lg font-semibold text-gray-800">Report Sections</h3>

      <nav className="space-y-2">
        {sections.map((section, index) => {
          const Icon = section.icon;
          const isCompleted = completedSections.has(index);
          const isCurrent = index === currentStep;
          const hasError = errorSections.has(index);

          let buttonClasses =
            'w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-all duration-200 ';
          let iconBgClasses = 'p-2 rounded-lg ';

          if (isCurrent) {
            buttonClasses += 'bg-blue-50 border-2 border-blue-200 text-blue-700 shadow-sm';
            iconBgClasses += section.color;
          } else if (hasError) {
            buttonClasses += 'bg-red-50 text-red-700 hover:bg-red-100';
            iconBgClasses += 'bg-red-500';
          } else if (isCompleted) {
            buttonClasses += 'bg-green-50 text-green-700 hover:bg-green-100';
            iconBgClasses += 'bg-green-500';
          } else {
            buttonClasses += 'text-gray-600 hover:bg-gray-100 hover:text-gray-800';
            iconBgClasses += 'bg-gray-300';
          }

          return (
            <button key={index} onClick={() => goToSection(index)} className={buttonClasses}>
              <div className={iconBgClasses}>
                <Icon className="h-4 w-4 text-white" />
              </div>

              <div className="min-w-0 flex-1">
                <p className="truncate text-sm font-medium">{section.title}</p>
                {hasError && !isCurrent && (
                  <p className="text-xs font-semibold text-red-600">! Needs attention</p>
                )}
                {isCompleted && !hasError && !isCurrent && (
                  <p className="text-xs text-green-600">✓ Completed</p>
                )}
              </div>

              {isCurrent && <ChevronRight className="h-5 w-5 text-blue-500" />}
              {hasError && !isCurrent && <AlertCircle className="h-5 w-5 text-red-500" />}
            </button>
          );
        })}
      </nav>
    </div>
  );
};
