import React from 'react';
import { AlertCircle } from 'lucide-react';
import type { FormFieldProps } from '../../types';

/**
 * Form field component with validation and styling
 */
export const FormField: React.FC<FormFieldProps> = ({ field, value, error, onChange, onBlur }) => {
  const isReadOnly = field.isCalculated || field.readOnly;

  const inputClasses = `w-full rounded-lg border-2 px-4 py-3 text-lg transition-all duration-200 ${
    error
      ? 'border-red-400 focus:border-red-500 focus:ring-red-200'
      : 'border-gray-200 focus:border-blue-500 focus:ring-blue-200'
  } ${isReadOnly ? 'bg-gray-100 text-gray-700 cursor-not-allowed' : 'bg-white'} ${
    field.prefix ? 'pl-12' : ''
  } focus:outline-none focus:ring-2`;

  return (
    <div className="space-y-1">
      <label htmlFor={field.name} className="block text-sm font-semibold text-gray-700">
        {field.label}
      </label>

      <div className="relative">
        {field.prefix && (
          <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4 text-lg text-gray-500">
            {field.prefix}
          </span>
        )}

        <input
          type={field.type === 'number' ? 'text' : field.type}
          inputMode={field.type === 'number' ? 'decimal' : undefined}
          id={field.name}
          name={field.name}
          value={value}
          onChange={onChange}
          onBlur={field.type === 'number' ? onBlur : undefined}
          readOnly={isReadOnly}
          step={field.type === 'number' ? '0.01' : undefined}
          className={inputClasses}
          placeholder={field.placeholder || (field.type === 'number' ? '0.00' : '')}
          autoComplete={field.autoComplete || 'off'}
        />
      </div>

      {field.description && !error && (
        <p className="mt-1 text-xs text-gray-500">{field.description}</p>
      )}

      {error && (
        <p className="mt-1 flex items-center gap-1 text-sm text-red-600">
          <AlertCircle size={14} />
          {error}
        </p>
      )}
    </div>
  );
};
