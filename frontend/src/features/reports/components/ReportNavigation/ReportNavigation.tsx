import React from 'react';
import { ChevronRight, CheckCircle, Eye } from 'lucide-react';

interface ReportNavigationProps {
  currentStep: number;
  isLastFormStep: boolean;
  isReviewStep: boolean;
  canProceed: boolean;
  isSubmitting: boolean;
  handlePrevious: () => void;
  handleNext: () => void;
}

/**
 * Navigation component for form steps
 */
export const ReportNavigation: React.FC<ReportNavigationProps> = ({
  currentStep,
  isLastFormStep,
  isReviewStep,
  canProceed,
  isSubmitting,
  handlePrevious,
  handleNext,
}) => {
  if (isReviewStep) {
    return (
      <div className="mt-8 flex items-center justify-between border-t border-gray-200 pt-6">
        <button
          type="button"
          onClick={handlePrevious}
          disabled={isSubmitting}
          className="flex items-center space-x-2 rounded-lg bg-gray-200 px-6 py-3 font-medium text-gray-700 transition-all duration-200 hover:bg-gray-300 active:bg-gray-400 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <span>Back to Edit</span>
        </button>

        <button
          type="submit"
          disabled={isSubmitting}
          className="flex items-center space-x-2 rounded-lg bg-indigo-600 px-8 py-3 font-medium text-white shadow-lg transition-all duration-200 hover:bg-indigo-700 hover:shadow-xl focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <span>{isSubmitting ? 'Submitting...' : 'Submit Final Report'}</span>
          {!isSubmitting && <CheckCircle className="h-5 w-5" />}
        </button>
      </div>
    );
  }

  return (
    <div className="mt-8 flex items-center justify-between border-t border-gray-200 pt-6">
      <button
        type="button"
        onClick={handlePrevious}
        disabled={currentStep === 0}
        className="flex items-center space-x-2 rounded-lg bg-gray-200 px-6 py-3 font-medium text-gray-700 transition-all duration-200 hover:bg-gray-300 active:bg-gray-400 disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-400"
      >
        <span>Previous</span>
      </button>

      <button
        type="button"
        onClick={handleNext}
        disabled={!canProceed}
        className="flex items-center space-x-2 rounded-lg bg-blue-500 px-6 py-3 font-medium text-white transition-all duration-200 hover:bg-blue-600 active:bg-blue-700 disabled:cursor-not-allowed disabled:bg-blue-300"
      >
        <span>{isLastFormStep ? 'Review Report' : 'Next'}</span>
        {isLastFormStep ? <Eye className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
      </button>
    </div>
  );
};
