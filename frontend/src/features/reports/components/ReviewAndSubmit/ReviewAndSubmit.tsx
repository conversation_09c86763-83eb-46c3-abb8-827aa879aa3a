import React from 'react';
import { Eye } from 'lucide-react';
import { formatCurrency, formatTime } from '../../utils/validation';
import type { FormData, Section } from '../../types';

interface ReviewAndSubmitProps {
  formData: FormData;
  sections: Section[];
  submitError?: string | null;
}

/**
 * Component for reviewing form data before submission
 */
export const ReviewAndSubmit: React.FC<ReviewAndSubmitProps> = ({
  formData,
  sections,
  submitError,
}) => {
  const formatValue = (key: keyof FormData, value: string | number) => {
    const field = sections.flatMap(s => s.fields).find(f => f.name === key);

    if (value === '' || value === null) {
      return <span className="text-gray-500">Not provided</span>;
    }

    if (field?.prefix) {
      return `${field.prefix} ${formatCurrency(value)}`;
    }

    if (field?.type === 'time') {
      return formatTime(value as string);
    }

    return value;
  };

  return (
    <div className="animate-fade-in rounded-2xl bg-white p-8 shadow-lg">
      <div className="mb-8 flex items-center space-x-4">
        <div className="rounded-2xl bg-indigo-500 p-4">
          <Eye className="h-8 w-8 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Review Your Report</h2>
          <p className="mt-1 text-gray-600">
            Please review all information carefully before final submission.
          </p>
        </div>
      </div>

      {submitError && (
        <div className="mb-6 rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-700">{submitError}</p>
        </div>
      )}

      <div className="space-y-8">
        {sections.map(section => (
          <div key={section.title}>
            <h3 className="mb-4 border-b pb-2 text-lg font-semibold text-gray-700">
              {section.title}
            </h3>
            <div className="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2 lg:grid-cols-3">
              {section.fields.map(field => (
                <div key={field.name}>
                  <p className="text-sm text-gray-500">{field.label}</p>
                  <p className="text-md font-medium text-gray-800">
                    {formatValue(
                      field.name as keyof FormData,
                      formData[field.name as keyof FormData]
                    )}
                  </p>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 rounded-lg border border-blue-200 bg-blue-50 p-4">
        <p className="text-sm text-blue-700">
          <strong>Note:</strong> Once submitted, this report will be saved to the system. Please
          ensure all information is accurate before proceeding.
        </p>
      </div>
    </div>
  );
};
