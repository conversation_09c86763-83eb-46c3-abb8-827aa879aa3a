import React from 'react';
import { FormField } from '../FormField';
import type { Section, FormData, ValidationErrors } from '../../types';

interface ReportSectionProps {
  section: Section;
  formData: FormData;
  errors: ValidationErrors;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleNumberBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
}

/**
 * Component for rendering a form section with its fields
 */
export const ReportSection: React.FC<ReportSectionProps> = ({
  section,
  formData,
  errors,
  handleChange,
  handleNumberBlur,
}) => {
  return (
    <div className="animate-fade-in rounded-2xl bg-white p-8 shadow-lg">
      <div className="mb-8 flex items-center space-x-4">
        <div className={`rounded-2xl p-4 ${section.color}`}>
          <section.icon className="h-8 w-8 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-800">{section.title}</h2>
          <p className="mt-1 text-gray-600">{section.description}</p>
        </div>
      </div>

      <div className="mb-8 grid grid-cols-1 gap-x-6 gap-y-8 md:grid-cols-2">
        {section.fields.map(field => (
          <FormField
            key={field.name}
            field={field}
            value={formData[field.name as keyof FormData]}
            error={errors[field.name]}
            onChange={handleChange}
            onBlur={handleNumberBlur}
          />
        ))}
      </div>
    </div>
  );
};
