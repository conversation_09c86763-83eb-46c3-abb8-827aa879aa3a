import type { FormData, ValidationErrors, Section } from '../types';

/**
 * Validates a specific section of the form
 */
export const validateSection = (
  formData: FormData,
  sectionFields: Section['fields']
): ValidationErrors => {
  const errors: ValidationErrors = {};

  sectionFields.forEach(field => {
    // Skip validation for calculated fields and read-only fields
    if (field.isCalculated || field.readOnly) {
      return;
    }

    const value = formData[field.name as keyof FormData];

    // Check for required fields (skip if field is optional)
    if (!field.optional && (value === '' || value === null || value === undefined)) {
      const errorMsg = `${field.label} is required.`;
      errors[field.name] = errorMsg;
    }

    // Type-specific validations
    if (field.type === 'number' && value !== '' && value !== null && value !== undefined) {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue)) {
        const errorMsg = `${field.label} must be a valid number.`;
        errors[field.name] = errorMsg;
      } else if (numValue < 0) {
        const errorMsg = `${field.label} cannot be negative.`;
        errors[field.name] = errorMsg;
      }
    }

    if (field.type === 'time' && value) {
      // More flexible time validation - accepts HH:MM, H:MM, HH:MM:SS formats
      // Also accepts time strings from database that might be in different formats
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
      const timeString = String(value).trim();

      // Skip validation for read-only time fields (they come from database and should be trusted)
      if (!field.readOnly && !timeRegex.test(timeString)) {
        const errorMsg = `${field.label} must be a valid time format (HH:MM).`;
        errors[field.name] = errorMsg;
      }
    }

    if (field.type === 'text' && value && typeof value === 'string') {
      if (value.trim().length === 0) {
        const errorMsg = `${field.label} cannot be empty.`;
        errors[field.name] = errorMsg;
      }
      if (value.length > 500) {
        const errorMsg = `${field.label} cannot exceed 500 characters.`;
        errors[field.name] = errorMsg;
      }
    }
  });

  return errors;
};

/**
 * Cross-field validations for business logic
 */
export const validateCrossFields = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};

  // Validate time range
  if (formData.openTime && formData.closeTime) {
    if (formData.openTime >= formData.closeTime) {
      errors.closeTime = 'Close time must be after open time.';
    }
  }

  // Validate payment totals match sales
  const totalPayments =
    (typeof formData.cashPayment === 'number' ? formData.cashPayment : 0) +
    (typeof formData.visaPayment === 'number' ? formData.visaPayment : 0) +
    (typeof formData.onlineTransactionPayment === 'number'
      ? formData.onlineTransactionPayment
      : 0) +
    (typeof formData.qrPayment === 'number' ? formData.qrPayment : 0);

  const totalSales = typeof formData.totalSales === 'number' ? formData.totalSales : 0;

  if (totalPayments > 0 && totalSales > 0 && Math.abs(totalPayments - totalSales) > 0.01) {
    const difference = (totalPayments - totalSales).toFixed(2);
    const errorMessage = `Total payments (RM ${totalPayments.toFixed(2)}) should match total sales (RM ${totalSales.toFixed(2)}). Difference: RM ${difference}`;

    // Add error to both sales and payment fields for better visibility
    errors.totalSales = errorMessage;
    errors.cashPayment = 'Payment total mismatch - verify all payment amounts';
    errors.visaPayment = 'Payment total mismatch - verify all payment amounts';
    errors.onlineTransactionPayment = 'Payment total mismatch - verify all payment amounts';
    errors.qrPayment = 'Payment total mismatch - verify all payment amounts';
  }

  // Validate drawer calculations
  if (formData.drawerAmount !== '' && formData.expectedDrawerAmount !== '') {
    const actual = typeof formData.drawerAmount === 'number' ? formData.drawerAmount : 0;
    const expected =
      typeof formData.expectedDrawerAmount === 'number' ? formData.expectedDrawerAmount : 0;
    const difference = Math.abs(actual - expected);

    // Warning for large discrepancies (more than $50)
    if (difference > 50) {
      errors.drawerAmount = `Large discrepancy detected: $${difference.toFixed(2)}. Please verify the amount.`;
    }
  }

  return errors;
};

/**
 * Validates all form data
 */
export const validateAllSections = (formData: FormData, sections: Section[]): ValidationErrors => {
  let allErrors: ValidationErrors = {};

  // Validate each section
  sections.forEach(section => {
    const sectionErrors = validateSection(formData, section.fields);
    allErrors = { ...allErrors, ...sectionErrors };
  });

  // Add cross-field validations
  const crossFieldErrors = validateCrossFields(formData);
  allErrors = { ...allErrors, ...crossFieldErrors };

  return allErrors;
};

/**
 * Checks if a specific field has validation errors
 */
export const hasFieldError = (fieldName: string, errors: ValidationErrors): boolean => {
  return fieldName in errors;
};

/**
 * Gets the error message for a specific field
 */
export const getFieldError = (fieldName: string, errors: ValidationErrors): string | undefined => {
  return errors[fieldName];
};

/**
 * Checks if any section has validation errors
 */
export const getSectionsWithErrors = (
  errors: ValidationErrors,
  sections: Section[]
): Set<number> => {
  const errorSections = new Set<number>();

  Object.keys(errors).forEach(fieldName => {
    const sectionIndex = sections.findIndex(section =>
      section.fields.some(field => field.name === fieldName)
    );
    if (sectionIndex !== -1) {
      errorSections.add(sectionIndex);
    }
  });

  return errorSections;
};

/**
 * Sanitizes form data for API submission
 */
export const sanitizeFormData = (formData: FormData): Record<string, any> => {
  const sanitized: Record<string, any> = {};

  Object.entries(formData).forEach(([key, value]) => {
    if (value === '') {
      sanitized[key] = 0; // Convert empty strings to 0 for numeric fields
    } else {
      sanitized[key] = value;
    }
  });

  return sanitized;
};

/**
 * Formats currency values for display
 */
export const formatCurrency = (value: number | string): string => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '0.00';
  return numValue.toFixed(2);
};

/**
 * Formats time values for display
 */
export const formatTime = (timeString: string): string => {
  if (!timeString) return '';
  try {
    const date = new Date(`1970-01-01T${timeString}`);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } catch {
    return timeString;
  }
};
