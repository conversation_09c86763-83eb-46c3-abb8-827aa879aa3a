import { Home, TrendingUp, CreditCard, DollarSign } from 'lucide-react';
import type { Section } from '../types';

/**
 * Configuration for restaurant report form sections
 * Each section contains fields with validation and calculation rules
 */
export const sections: Section[] = [
  {
    title: 'Restaurant Details',
    icon: Home,
    description: 'Basic information about the restaurant and operating hours.',
    color: 'bg-sky-500',
    fields: [
      {
        label: 'Restaurant Name',
        name: 'restaurantName',
        type: 'text',
        placeholder: 'e.g., The Grand Diner',
        autoComplete: 'organization',
        readOnly: true,
        description: 'Pre-selected restaurant from your selection.',
      },
      {
        label: 'Management Note / Manager',
        name: 'managementNote',
        type: 'text',
        placeholder: 'e.g., <PERSON> (Shift Manager)',
        autoComplete: 'off',
        optional: true,
      },
      {
        label: 'Open Time',
        name: 'openTime',
        type: 'time',
        autoComplete: 'off',
        readOnly: true,
        description: 'Default opening time from restaurant settings.',
      },
      {
        label: 'Close Time',
        name: 'closeTime',
        type: 'time',
        autoComplete: 'off',
        readOnly: true,
        description: 'Default closing time from restaurant settings.',
      },
    ],
  },
  {
    title: 'Payment Breakdown',
    icon: CreditCard,
    description: 'Details of transactions by payment type.',
    color: 'bg-purple-500',
    fields: [
      {
        label: 'Cash Payments',
        name: 'cashPayment',
        type: 'number',
        prefix: 'RM',
      },
      {
        label: 'Visa Payments',
        name: 'visaPayment',
        type: 'number',
        prefix: 'RM',
      },
      {
        label: 'Online Transactions',
        name: 'onlineTransactionPayment',
        type: 'number',
        prefix: 'RM',
      },
      {
        label: 'QR Code Payments',
        name: 'qrPayment',
        type: 'number',
        prefix: 'RM',
      },
    ],
  },
  {
    title: 'Cash Management',
    icon: DollarSign,
    description: 'Drawer reconciliation and cash flow details.',
    color: 'bg-red-500',
    fields: [
      {
        label: 'Float (Starting Cash)',
        name: 'float',
        type: 'number',
        prefix: 'RM',
      },
      {
        label: 'Cash In',
        name: 'cashIn',
        type: 'number',
        prefix: 'RM',
        optional: true,
      },
      {
        label: 'Cash Out',
        name: 'cashOut',
        type: 'number',
        prefix: 'RM',
        optional: true,
      },
      {
        label: 'Actual Drawer Amount',
        name: 'drawerAmount',
        type: 'number',
        prefix: 'RM',
        description: 'The final amount of cash counted in the drawer.',
      },
      {
        label: 'Expected Drawer Amount',
        name: 'expectedDrawerAmount',
        type: 'number',
        prefix: 'RM',
        isCalculated: true,
        description: 'Auto-calculated: Float + Cash Payments + Cash In - Cash Out.',
      },
      {
        label: 'Short/Extra',
        name: 'shortExtra',
        type: 'number',
        prefix: 'RM',
        isCalculated: true,
        description: 'Auto-calculated: Actual - Expected Amount.',
      },
    ],
  },
  {
    title: 'Sales Summary',
    icon: TrendingUp,
    description: 'Automatically calculated sales breakdown based on payment methods.',
    color: 'bg-green-500',
    fields: [
      {
        label: 'Offline Sales',
        name: 'offlineSales',
        type: 'number',
        prefix: 'RM',
        isCalculated: true,
        description: 'Auto-calculated: Cash + Visa Payments.',
      },
      {
        label: 'Online Sales',
        name: 'onlineSales',
        type: 'number',
        prefix: 'RM',
        isCalculated: true,
        description: 'Auto-calculated: QR + Online Transaction Payments.',
      },
      {
        label: 'Total Sales',
        name: 'totalSales',
        type: 'number',
        prefix: 'RM',
        isCalculated: true,
        description: 'Auto-calculated: Offline + Online Sales.',
      },
      {
        label: 'Total Discounts',
        name: 'totalDiscounts',
        type: 'number',
        prefix: 'RM',
        optional: true,
      },
    ],
  },
];
