import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  validateSection,
  validateAllSections,
  getSectionsWithErrors,
  sanitizeFormData,
} from '../utils/validation';
import { sections } from '../utils/reportSections';
import type {
  FormData,
  ValidationErrors,
  UseRestaurantReportFormReturn,
  RestaurantReportInput,
} from '../types';

/**
 * Custom hook for managing restaurant report form state and business logic
 */
export const useRestaurantReportForm = (): UseRestaurantReportFormReturn => {
  const navigate = useNavigate();
  // No longer using restaurant state or loading state as data is hardcoded

  // Initial form data structure
  const initialFormData: FormData = useMemo(
    () => ({
      restaurantName: '',
      managementNote: '',
      openTime: '',
      closeTime: '',
      totalSales: '',
      offlineSales: '',
      onlineSales: '',
      totalDiscounts: 0,
      cashPayment: 0,
      visaPayment: 0,
      onlineTransactionPayment: 0,
      qrPayment: 0,
      float: 0,
      cashIn: 0,
      cashOut: 0,
      expectedDrawerAmount: '',
      drawerAmount: '',
      shortExtra: '',
    }),
    []
  );

  // Form state
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSections, setCompletedSections] = useState(new Set<number>());
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Initialize form data with placeholder restaurant details
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      restaurantName: 'Broku Sales Dashboard Restaurant', // Placeholder name
      managementNote: '',
      openTime: '09:00', // Placeholder opening time
      closeTime: '21:00', // Placeholder closing time
    }));
  }, []);

  // Helper function to parse number values
  const parseNum = useCallback((val: number | '') => {
    if (val === '' || typeof val !== 'number' || isNaN(val)) return 0;
    return val;
  }, []);

  // Automatic calculation: Offline Sales = Cash + Visa Payments
  useEffect(() => {
    const cash = parseNum(formData.cashPayment);
    const visa = parseNum(formData.visaPayment);
    const offline = cash + visa;

    setFormData(prev => ({
      ...prev,
      offlineSales: offline === 0 ? '' : offline,
    }));
  }, [formData.cashPayment, formData.visaPayment, parseNum]);

  // Automatic calculation: Online Sales = QR + Online Transaction Payments
  useEffect(() => {
    const qr = parseNum(formData.qrPayment);
    const onlineTransaction = parseNum(formData.onlineTransactionPayment);
    const online = qr + onlineTransaction;

    setFormData(prev => ({
      ...prev,
      onlineSales: online === 0 ? '' : online,
    }));
  }, [formData.qrPayment, formData.onlineTransactionPayment, parseNum]);

  // Automatic calculation: Total Sales = Offline + Online Sales
  useEffect(() => {
    const offline = parseNum(formData.offlineSales);
    const online = parseNum(formData.onlineSales);
    const total = offline + online;

    setFormData(prev => ({
      ...prev,
      totalSales: total === 0 ? '' : total,
    }));
  }, [formData.offlineSales, formData.onlineSales, parseNum]);

  // Automatic calculation: Expected Drawer Amount
  useEffect(() => {
    const float = parseNum(formData.float);
    const cashIn = parseNum(formData.cashIn);
    const cashOut = parseNum(formData.cashOut);
    const cashPayment = parseNum(formData.cashPayment);
    const expected = float + cashPayment + cashIn - cashOut;

    setFormData(prev => ({
      ...prev,
      expectedDrawerAmount: expected,
    }));
  }, [formData.float, formData.cashIn, formData.cashOut, formData.cashPayment, parseNum]);

  // Automatic calculation: Short/Extra Amount
  useEffect(() => {
    const expected = parseNum(formData.expectedDrawerAmount);
    const actual = parseNum(formData.drawerAmount);

    if (formData.drawerAmount !== '') {
      const diff = actual - expected;
      setFormData(prev => ({ ...prev, shortExtra: diff }));
    } else {
      setFormData(prev => ({ ...prev, shortExtra: '' }));
    }
  }, [formData.drawerAmount, formData.expectedDrawerAmount, parseNum]);

  // Real-time validation: Total payments should match total sales
  useEffect(() => {
    const totalPayments =
      parseNum(formData.cashPayment) +
      parseNum(formData.visaPayment) +
      parseNum(formData.onlineTransactionPayment) +
      parseNum(formData.qrPayment);

    const totalSales = parseNum(formData.totalSales);

    // Clear any existing payment validation errors first
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.totalSales;
      delete newErrors.cashPayment;
      delete newErrors.visaPayment;
      delete newErrors.onlineTransactionPayment;
      delete newErrors.qrPayment;
      return newErrors;
    });

    // Only validate if both totals are greater than 0
    if (totalPayments > 0 && totalSales > 0 && Math.abs(totalPayments - totalSales) > 0.01) {
      const difference = (totalPayments - totalSales).toFixed(2);
      const errorMessage = `Total payments (RM ${totalPayments.toFixed(2)}) should match total sales (RM ${totalSales.toFixed(2)}). Difference: RM ${difference}`;

      setErrors(prev => ({
        ...prev,
        totalSales: errorMessage,
        // Also add error to payment fields to highlight the issue in the Payment Breakdown section
        cashPayment: 'Payment total mismatch - check all payment amounts',
        visaPayment: 'Payment total mismatch - check all payment amounts',
        onlineTransactionPayment: 'Payment total mismatch - check all payment amounts',
        qrPayment: 'Payment total mismatch - check all payment amounts',
      }));
    }
  }, [
    formData.cashPayment,
    formData.visaPayment,
    formData.onlineTransactionPayment,
    formData.qrPayment,
    formData.totalSales,
    parseNum,
  ]);

  // Handle input changes
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value, type } = e.target;

      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? (value === '' ? '' : parseFloat(value) || '') : value,
      }));

      // Clear error for the field being edited
      if (errors[name]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Handle number field blur (format and validate)
  const handleNumberBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (value === '' || isNaN(parseFloat(value))) {
      setFormData(prev => ({ ...prev, [name as keyof FormData]: '' }));
    } else {
      const parsedValue = parseFloat(parseFloat(value).toFixed(2));
      setFormData(prev => ({ ...prev, [name as keyof FormData]: parsedValue }));
    }
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      setIsSubmitting(true);
      setSubmitError(null);

      try {
        // Final validation of all sections
        const allErrors = validateAllSections(formData, sections);

        if (Object.keys(allErrors).length > 0) {
          setErrors(allErrors);
          // Navigate to first section with errors
          const firstErrorSection = sections.findIndex(sec =>
            sec.fields.some(f => allErrors[f.name])
          );
          if (firstErrorSection !== -1) {
            setCurrentStep(firstErrorSection);
          }
          throw new Error('Please fix the validation errors before submitting.');
        }

        // Prepare data for API
        const sanitizedData = sanitizeFormData(formData);
        const reportData: RestaurantReportInput = {
          ...(sanitizedData as any),
          restaurantId: 1, // Placeholder restaurantId
          reportDate: new Date().toISOString(),
        };

        // Simulate API submission
        console.log('Submitting report:', reportData);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

        // Success - clear form and reset state
        setFormData(initialFormData);
        setCurrentStep(0);
        setCompletedSections(new Set());
        setErrors({});

        // Navigate to dashboard after successful submission
        navigate('/dashboard');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to submit report';
        setSubmitError(errorMessage);
        console.error('Form submission error:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [formData, initialFormData]
  );

  // Computed values
  const errorSections = useMemo(() => getSectionsWithErrors(errors, sections), [errors]);

  const canProceed = useMemo(() => {
    // Always allow proceeding from the first section (Restaurant Details) since it's auto-populated
    // The UI skips this section, but we need to handle it in the logic
    if (currentStep === 0) {
      return true;
    }

    // For review step, check for any validation errors including cross-field validations
    if (currentStep >= sections.length) {
      return Object.keys(errors).length === 0;
    }

    const currentSectionFields = sections[currentStep]?.fields || [];
    const sectionErrors = validateSection(formData, currentSectionFields);

    // Also check for cross-field validation errors that affect the current section
    const currentSectionFieldNames = currentSectionFields.map(f => f.name);
    const crossFieldErrors = Object.keys(errors).filter(errorKey =>
      currentSectionFieldNames.includes(errorKey)
    );

    return Object.keys(sectionErrors).length === 0 && crossFieldErrors.length === 0;
  }, [formData, currentStep, errors]);

  // Navigation handlers
  const handleNext = useCallback(() => {
    if (!canProceed) {
      return; // Should not happen if button is not disabled, but adding for safety
    }

    // Skip validation for the first section (Restaurant Details) since it's auto-populated
    if (currentStep === 0) {
      setCompletedSections(prev => new Set([...prev, currentStep]));
      setCurrentStep(currentStep + 1);
      return;
    }

    // For other sections, validate before proceeding
    const sectionErrors = validateSection(formData, sections[currentStep]?.fields || []);
    setErrors(prev => ({ ...prev, ...sectionErrors }));

    if (Object.keys(sectionErrors).length === 0) {
      setCompletedSections(prev => new Set([...prev, currentStep]));
      if (currentStep < sections.length) {
        setCurrentStep(currentStep + 1);
      }
    }
  }, [currentStep, formData, canProceed]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const goToSection = useCallback(
    (index: number) => {
      // Allow navigation to any previous step or completed step
      if (index <= currentStep || completedSections.has(index)) {
        setCurrentStep(index);
      }
    },
    [currentStep, completedSections]
  );

  const currentSection = sections[currentStep];
  const isReviewStep = currentStep === sections.length;
  const isLastFormStep = currentStep === sections.length - 1;

  return {
    formData,
    currentStep,
    completedSections,
    errors,
    errorSections,
    handleChange,
    handleNumberBlur,
    handleSubmit,
    handleNext,
    handlePrevious,
    goToSection,
    sections,
    currentSection,
    isReviewStep,
    isLastFormStep,
    canProceed,
    isSubmitting,
    submitError,
    restaurantName: 'Broku Sales Dashboard Restaurant', // Placeholder name
    openingHours: formData.openTime || '09:00', // Use form data or fallback
    closingHours: formData.closeTime || '21:00', // Use form data or fallback
    isLoading: false, // No longer loading from an external source
  };
};
