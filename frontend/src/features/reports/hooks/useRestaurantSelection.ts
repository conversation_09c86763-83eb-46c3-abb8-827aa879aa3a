import { useState, useEffect } from 'react';
import { restaurantApi } from '@/features/restaurant-management/services/restaurantApi';
import { Restaurant } from '@/features/restaurant-management/types';

export interface UseRestaurantSelectionReturn {
  restaurants: Restaurant[];
  selectedRestaurant: Restaurant | null;
  isLoading: boolean;
  error: string | null;
  selectRestaurant: (restaurant: Restaurant) => void;
  clearSelection: () => void;
}

/**
 * Custom hook for managing restaurant selection
 */
export const useRestaurantSelection = (): UseRestaurantSelectionReturn => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch restaurants on mount
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await restaurantApi.getAllRestaurants();
        setRestaurants(data.filter(restaurant => restaurant.isActive === true));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch restaurants';
        setError(errorMessage);
        console.error('Error fetching restaurants:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRestaurants();
  }, []);

  const selectRestaurant = (restaurant: Restaurant) => {
    setSelectedRestaurant(restaurant);
  };

  const clearSelection = () => {
    setSelectedRestaurant(null);
  };

  return {
    restaurants,
    selectedRestaurant,
    isLoading,
    error,
    selectRestaurant,
    clearSelection,
  };
};
