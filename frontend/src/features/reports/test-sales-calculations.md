# Sales Entry Flow Test Cases

## Test the Modified Sales Entry Flow

### Expected Flow:

1. **Step 1: Payment Breakdown** - User inputs payment method amounts
2. **Step 2: Cash Management** - User inputs float and cash-related entries
3. **Step 3: Sales Summary** - Automatically calculated offline/online sales totals
4. **Step 4: Review and Submit** - Final review

### Test Case 1: Basic Payment Input

**Input (Step 1 - Payment Breakdown):**

- Cash Payments: RM 355.80
- Visa Payments: RM 1,424.30
- QR Code Payments: RM 1,690.55
- Online Transactions: RM 200.00

**Expected Calculations (Step 3 - Sales Summary):**

- Offline Sales: RM 1,780.10 (Cash + Visa = 355.80 + 1,424.30)
- Online Sales: RM 1,890.55 (QR + Online TF = 1,690.55 + 200.00)
- Total Sales: RM 3,670.65 (Offline + Online = 1,780.10 + 1,890.55)

### Test Case 2: Cash Management (Step 2)

**Input:**

- Float: RM 150.00
- Cash In: RM 0.00 (optional)
- Cash Out: RM 0.00 (optional)
- Actual Drawer Amount: RM 505.80

**Expected Calculations:**

- Expected Drawer Amount: RM 505.80 (Float + Cash Payments + Cash In - Cash Out = 150.00 + 355.80 + 0 - 0)
- Short/Extra: RM 0.00 (Actual - Expected = 505.80 - 505.80)

### Verification Steps:

1. Navigate to Sales Entry page
2. Enter payment breakdown amounts in Step 1
3. Proceed to Step 2 (Cash Management) and verify calculations
4. Proceed to Step 3 (Sales Summary) and verify automatic calculations
5. Check that offline/online sales are read-only and correctly calculated
6. Verify total sales calculation
7. Proceed to Step 4 (Review) and verify all data is correct

### Expected User Experience:

- Payment Breakdown fields should be editable
- Sales Summary fields should be read-only with calculated values
- Calculations should update in real-time as payment amounts change
- No manual input required for offline/online sales totals
