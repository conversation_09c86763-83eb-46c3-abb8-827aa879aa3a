import { Restaurant as BaseRestaurant, User } from '@/shared/types';

export interface Restaurant extends BaseRestaurant {}

export interface CreateRestaurantRequest {
  name: string;
  address: string;
  phone: string;
  email?: string;
  managerId?: string;
}

export interface UpdateRestaurantRequest {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  managerId?: string;
  isActive?: boolean;
}

export interface RestaurantFilters {
  search?: string;
  managerId?: string;
  isActive?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export interface RestaurantWithManager extends Restaurant {
  manager?: User;
}

export interface RestaurantStats {
  totalSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  salesCount: number;
  lastSaleDate?: string;
  monthlyGrowth: number;
}

export interface RestaurantWithStats extends RestaurantWithManager {
  stats: RestaurantStats;
}

export interface RestaurantsListResponse {
  restaurants: RestaurantWithManager[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface RestaurantLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface RestaurantDetails extends RestaurantWithManager {
  location?: RestaurantLocation;
  stats: RestaurantStats;
  recentSales: Array<{
    id: string;
    amount: number;
    date: string;
    user: User;
  }>;
}
