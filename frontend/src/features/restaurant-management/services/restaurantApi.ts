import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  Restaurant,
  CreateRestaurantRequest,
  UpdateRestaurantRequest,
  RestaurantFilters,
  RestaurantsListResponse,
  RestaurantDetails,
  RestaurantStats,
} from '../types';

export const restaurantApi = {
  // Get all restaurants with filters
  getRestaurants: async (
    filters?: RestaurantFilters & { page?: number; limit?: number }
  ): Promise<RestaurantsListResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.RESTAURANTS.LIST, {
      params: filters,
    });
    return response.data;
  },

  // Get all restaurants (simplified method for backward compatibility)
  getAllRestaurants: async (): Promise<Restaurant[]> => {
    const response = await apiClient.get(API_ENDPOINTS.RESTAURANTS.LIST, {
      params: { isActive: true, limit: 1000 }, // Get all active restaurants
    });
    return response.data.restaurants || response.data;
  },

  // Get single restaurant with details
  getRestaurant: async (id: string): Promise<RestaurantDetails> => {
    const response = await apiClient.get(API_ENDPOINTS.RESTAURANTS.GET(id));
    return response.data;
  },

  // Create new restaurant
  createRestaurant: async (data: CreateRestaurantRequest): Promise<Restaurant> => {
    const response = await apiClient.post(API_ENDPOINTS.RESTAURANTS.CREATE, data);
    return response.data;
  },

  // Update restaurant
  updateRestaurant: async (id: string, data: UpdateRestaurantRequest): Promise<Restaurant> => {
    const response = await apiClient.put(API_ENDPOINTS.RESTAURANTS.UPDATE(id), data);
    return response.data;
  },

  // Delete restaurant (soft delete)
  deleteRestaurant: async (id: string): Promise<void> => {
    await apiClient.delete(API_ENDPOINTS.RESTAURANTS.DELETE(id));
  },

  // Get restaurant statistics
  getRestaurantStats: async (id: string, period?: string): Promise<RestaurantStats> => {
    const response = await apiClient.get(`${API_ENDPOINTS.RESTAURANTS.GET(id)}/stats`, {
      params: { period },
    });
    return response.data;
  },

  // Get restaurant sales
  getRestaurantSales: async (id: string, filters?: any): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.RESTAURANTS.GET(id)}/sales`, {
      params: filters,
    });
    return response.data;
  },

  // Bulk operations
  bulkDeleteRestaurants: async (ids: string[]): Promise<void> => {
    await apiClient.delete(`${API_ENDPOINTS.RESTAURANTS.LIST}/bulk`, {
      data: { ids },
    });
  },

  bulkUpdateRestaurants: async (
    updates: Array<{ id: string; data: UpdateRestaurantRequest }>
  ): Promise<Restaurant[]> => {
    const response = await apiClient.put(`${API_ENDPOINTS.RESTAURANTS.LIST}/bulk`, { updates });
    return response.data;
  },

  // Restaurant activation/deactivation
  activateRestaurant: async (id: string): Promise<Restaurant> => {
    const response = await apiClient.post(`${API_ENDPOINTS.RESTAURANTS.UPDATE(id)}/activate`);
    return response.data;
  },

  deactivateRestaurant: async (id: string): Promise<Restaurant> => {
    const response = await apiClient.post(`${API_ENDPOINTS.RESTAURANTS.UPDATE(id)}/deactivate`);
    return response.data;
  },

  // Manager assignment
  assignManager: async (restaurantId: string, managerId: string): Promise<Restaurant> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.RESTAURANTS.UPDATE(restaurantId)}/assign-manager`,
      {
        managerId,
      }
    );
    return response.data;
  },

  removeManager: async (restaurantId: string): Promise<Restaurant> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.RESTAURANTS.UPDATE(restaurantId)}/remove-manager`
    );
    return response.data;
  },

  // Export restaurants
  exportRestaurants: async (filters?: RestaurantFilters): Promise<Blob> => {
    const response = await apiClient.get(`${API_ENDPOINTS.RESTAURANTS.LIST}/export`, {
      params: filters,
      responseType: 'blob',
    });
    return response.data;
  },

  // Import restaurants
  importRestaurants: async (file: File): Promise<{ success: number; errors: any[] }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${API_ENDPOINTS.RESTAURANTS.CREATE}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get restaurant statistics summary
  getRestaurantStatsSummary: async (): Promise<{
    totalRestaurants: number;
    activeRestaurants: number;
    totalRevenue: number;
    averageRevenue: number;
  }> => {
    const response = await apiClient.get(`${API_ENDPOINTS.RESTAURANTS.LIST}/stats`);
    return response.data;
  },

  // Search restaurants by location
  searchByLocation: async (
    latitude: number,
    longitude: number,
    radius: number
  ): Promise<Restaurant[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.RESTAURANTS.LIST}/search-location`, {
      params: { latitude, longitude, radius },
    });
    return response.data;
  },
};
