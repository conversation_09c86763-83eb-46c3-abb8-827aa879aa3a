import { z } from 'zod';
import {
  nameSchema,
  phoneSchema,
  addressSchema,
  emailSchema,
  idSchema,
  paginationSchema,
} from '@/shared/validations/baseSchemas';

export const restaurantFormSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
});

export const restaurantUpdateSchema = z.object({
  name: nameSchema.optional(),
  address: addressSchema.optional(),
  phone: phoneSchema.optional(),
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
  isActive: z.boolean().optional(),
});

export const restaurantFiltersSchema = z
  .object({
    search: z.string().optional(),
    managerId: idSchema.optional(),
    isActive: z.boolean().optional(),
    dateFrom: z.string().optional(),
    dateTo: z.string().optional(),
  })
  .merge(paginationSchema);

export const restaurantLocationSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  address: addressSchema,
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().min(1, 'ZIP code is required'),
  country: z.string().min(1, 'Country is required'),
});

export const bulkRestaurantImportSchema = z.object({
  restaurants: z.array(restaurantFormSchema).min(1, 'At least one restaurant is required'),
});

export const restaurantSearchLocationSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  radius: z.number().min(1).max(100), // in kilometers
});

// Type exports
export type RestaurantFormData = z.infer<typeof restaurantFormSchema>;
export type RestaurantUpdateData = z.infer<typeof restaurantUpdateSchema>;
export type RestaurantFiltersData = z.infer<typeof restaurantFiltersSchema>;
export type RestaurantLocationData = z.infer<typeof restaurantLocationSchema>;
export type BulkRestaurantImportData = z.infer<typeof bulkRestaurantImportSchema>;
export type RestaurantSearchLocationData = z.infer<typeof restaurantSearchLocationSchema>;
