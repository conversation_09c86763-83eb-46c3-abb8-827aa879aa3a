import { useQuery } from '@tanstack/react-query';
import { restaurantApi } from '../services/restaurantApi';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { RestaurantFilters } from '../types';

// Query Keys
export const RESTAURANT_QUERY_KEYS = {
  all: [QUERY_KEYS.RESTAURANTS] as const,
  lists: () => [...RESTAURANT_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: RestaurantFilters) => [...RESTAURANT_QUERY_KEYS.lists(), filters] as const,
  details: () => [...RESTAURANT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...RESTAURANT_QUERY_KEYS.details(), id] as const,
  stats: () => [...RESTAURANT_QUERY_KEYS.all, 'stats'] as const,
} as const;

// Get all restaurants query
export function useRestaurants(filters?: RestaurantFilters) {
  return useQuery({
    queryKey: RESTAURANT_QUERY_KEYS.list(filters),
    queryFn: () => restaurantApi.getRestaurants(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get single restaurant query
export function useRestaurant(id: string) {
  return useQuery({
    queryKey: RESTAURANT_QUERY_KEYS.detail(id),
    queryFn: () => restaurantApi.getRestaurant(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get restaurant by user's restaurantId (for header display)
export function useUserRestaurant(restaurantId: number | null) {
  return useQuery({
    queryKey: RESTAURANT_QUERY_KEYS.detail(restaurantId?.toString() || ''),
    queryFn: () => restaurantApi.getRestaurant(restaurantId!.toString()),
    enabled: !!restaurantId,
    staleTime: 10 * 60 * 1000, // 10 minutes - restaurant info doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Get active restaurants only
export function useActiveRestaurants() {
  return useQuery({
    queryKey: RESTAURANT_QUERY_KEYS.list({ isActive: true }),
    queryFn: () => restaurantApi.getRestaurants({ isActive: true }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
