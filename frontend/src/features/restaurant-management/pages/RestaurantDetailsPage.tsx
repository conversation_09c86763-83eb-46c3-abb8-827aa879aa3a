import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';

export function RestaurantDetailsPage() {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Restaurant Details</h1>
        <p className="text-muted-foreground">View and edit restaurant information.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Restaurant ID: {id}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            Restaurant details functionality will be implemented here.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
