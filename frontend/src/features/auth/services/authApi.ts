import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  LoginRequest,
  SignupRequest,
  AuthResponse,
  User,
  PasswordChangeRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from '../types';

export const authApi = {
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post(API_ENDPOINTS.AUTH.LOGIN, data);
    return response.data.data; // Extract the actual data from the backend response structure
  },

  signup: async (data: SignupRequest): Promise<AuthResponse> => {
    const response = await apiClient.post(API_ENDPOINTS.AUTH.SIGNUP, data);
    return response.data.data; // Extract the actual data from the backend response structure
  },

  logout: async (): Promise<void> => {
    await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get(API_ENDPOINTS.AUTH.ME);
    return response.data.data.user; // Extract the user from the backend response structure
  },

  refreshToken: async (): Promise<AuthResponse> => {
    const response = await apiClient.post(API_ENDPOINTS.AUTH.REFRESH);
    return response.data;
  },

  changePassword: async (data: PasswordChangeRequest): Promise<void> => {
    await apiClient.post('/api/auth/change-password', data);
  },

  forgotPassword: async (data: ForgotPasswordRequest): Promise<void> => {
    await apiClient.post('/api/auth/forgot-password', data);
  },

  resetPassword: async (data: ResetPasswordRequest): Promise<void> => {
    await apiClient.post('/api/auth/reset-password', data);
  },
};
