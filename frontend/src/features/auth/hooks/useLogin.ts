import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';
import { LoginRequest } from '../types';
import { getDefaultRedirectPath } from '../utils/authHelpers';

export function useLogin() {
  const navigate = useNavigate();
  const { login: authLogin } = useAuth();

  return useMutation({
    mutationFn: async (data: LoginRequest) => {
      console.log('Attempting login with email:', data.email);
      try {
        // Use the context's login function which now returns the user data
        const user = await authLogin(data.email, data.password);
        console.log('Login successful via context, user:', user);
        return user;
      } catch (error: any) {
        console.error('Login attempt failed:', {
          email: data.email,
          errorType: error.name,
          errorCode: error.code,
          errorMessage: error.message,
          config: error.config,
        });
        throw error;
      }
    },
    onSuccess: user => {
      console.log('Login successful, determining redirect path for user:', user);
      const redirectPath = getDefaultRedirectPath(user);
      console.log('Redirecting to:', redirectPath);
      navigate(redirectPath);
    },
    onError: (error: any) => {
      console.error('Login mutation error:', {
        name: error.name,
        message: error.message,
        code: error.code,
        stack: error.stack,
      });
    },
  });
}
