import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';
import { SignupRequest } from '../types';
import { getDefaultRedirectPath } from '../utils/authHelpers';

export function useSignup() {
  const navigate = useNavigate();
  const { signup: authSignup } = useAuth();

  return useMutation({
    mutationFn: async (data: SignupRequest) => {
      const user = await authSignup(data.email, data.password, data.fullName);
      return user;
    },
    onSuccess: user => {
      // Use role-based redirect for signup as well
      const redirectPath = getDefaultRedirectPath(user);
      console.log('Signup successful, redirecting to:', redirectPath);
      navigate(redirectPath);
    },
    onError: (error: any) => {
      console.error('Signup failed:', error);
    },
  });
}
