import { describe, it, expect } from 'vitest';
import {
  hasRole,
  isAdmin,
  isUser,
  canAccessAdmin,
  canManageUsers,
  getDefaultRedirectPath,
} from '../authHelpers';
import { User } from '@/shared/types';

// Mock user data
const adminUser: User = {
  userId: 1,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  restaurantId: null,
};

const staffUser: User = {
  userId: 2,
  username: 'staff',
  email: '<EMAIL>',
  fullName: 'Staff User',
  role: 'staff',
  restaurantId: 1,
};

const regularUser: User = {
  userId: 3,
  username: 'user',
  email: '<EMAIL>',
  fullName: 'Regular User',
  role: 'user',
  restaurantId: null,
};

describe('authHelpers', () => {
  describe('hasRole', () => {
    it('should return true when user has the specified role', () => {
      expect(hasRole(adminUser, 'admin')).toBe(true);
      expect(hasRole(staffUser, 'staff')).toBe(true);
      expect(hasRole(regularUser, 'user')).toBe(true);
    });

    it('should return false when user does not have the specified role', () => {
      expect(hasRole(adminUser, 'user')).toBe(false);
      expect(hasRole(staffUser, 'admin')).toBe(false);
      expect(hasRole(regularUser, 'admin')).toBe(false);
    });

    it('should return false when user is null', () => {
      expect(hasRole(null, 'admin')).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true for admin users', () => {
      expect(isAdmin(adminUser)).toBe(true);
    });

    it('should return false for non-admin users', () => {
      expect(isAdmin(staffUser)).toBe(false);
      expect(isAdmin(regularUser)).toBe(false);
      expect(isAdmin(null)).toBe(false);
    });
  });

  describe('isUser', () => {
    it('should return true for regular users', () => {
      expect(isUser(regularUser)).toBe(true);
    });

    it('should return false for non-regular users', () => {
      expect(isUser(adminUser)).toBe(false);
      expect(isUser(staffUser)).toBe(false);
      expect(isUser(null)).toBe(false);
    });
  });

  describe('canAccessAdmin', () => {
    it('should return true for admin users', () => {
      expect(canAccessAdmin(adminUser)).toBe(true);
    });

    it('should return false for non-admin users', () => {
      expect(canAccessAdmin(staffUser)).toBe(false);
      expect(canAccessAdmin(regularUser)).toBe(false);
      expect(canAccessAdmin(null)).toBe(false);
    });
  });

  describe('canManageUsers', () => {
    it('should return true for admin users', () => {
      expect(canManageUsers(adminUser)).toBe(true);
    });

    it('should return false for non-admin users', () => {
      expect(canManageUsers(staffUser)).toBe(false);
      expect(canManageUsers(regularUser)).toBe(false);
      expect(canManageUsers(null)).toBe(false);
    });
  });

  describe('getDefaultRedirectPath', () => {
    it('should return /admin/dashboard for admin users', () => {
      expect(getDefaultRedirectPath(adminUser)).toBe('/admin/dashboard');
    });

    it('should return /dashboard for staff users', () => {
      expect(getDefaultRedirectPath(staffUser)).toBe('/dashboard');
    });

    it('should return /dashboard for regular users', () => {
      expect(getDefaultRedirectPath(regularUser)).toBe('/dashboard');
    });

    it('should return /login for null user', () => {
      expect(getDefaultRedirectPath(null)).toBe('/login');
    });

    it('should return /dashboard for unknown roles', () => {
      const unknownRoleUser = { ...regularUser, role: 'unknown' as any };
      expect(getDefaultRedirectPath(unknownRoleUser)).toBe('/dashboard');
    });
  });
});
