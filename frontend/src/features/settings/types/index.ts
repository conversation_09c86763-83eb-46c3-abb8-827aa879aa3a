import { UserPreferences, NotificationSettings, SecuritySettings } from '@/shared/types';

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: string;
  currency: string;
  pageSize: number;
}

export interface NotificationPreferences extends NotificationSettings {
  salesAlerts: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
  systemUpdates: boolean;
  marketingEmails: boolean;
}

export interface SecurityPreferences extends SecuritySettings {
  loginNotifications: boolean;
  deviceTracking: boolean;
  apiAccessEnabled: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'team';
  dataSharing: boolean;
  analyticsTracking: boolean;
  cookiePreferences: {
    necessary: boolean;
    functional: boolean;
    analytics: boolean;
    marketing: boolean;
  };
}

export interface IntegrationSettings {
  apiKeys: Array<{
    id: string;
    name: string;
    key: string;
    permissions: string[];
    createdAt: string;
    lastUsed?: string;
  }>;
  webhooks: Array<{
    id: string;
    url: string;
    events: string[];
    isActive: boolean;
    createdAt: string;
  }>;
}

export interface BackupSettings {
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  retentionPeriod: number;
  includeFiles: boolean;
}

export interface AllSettings {
  app: AppSettings;
  notifications: NotificationPreferences;
  security: SecurityPreferences;
  privacy: PrivacySettings;
  integrations: IntegrationSettings;
  backup: BackupSettings;
}

export interface SettingsUpdateRequest {
  app?: Partial<AppSettings>;
  notifications?: Partial<NotificationPreferences>;
  security?: Partial<SecurityPreferences>;
  privacy?: Partial<PrivacySettings>;
  backup?: Partial<BackupSettings>;
}
