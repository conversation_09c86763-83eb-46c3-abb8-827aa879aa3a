import { z } from 'zod';

export const appSettingsSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().min(1, 'Language is required').default('en'),
  timezone: z.string().min(1, 'Timezone is required').default('UTC'),
  dateFormat: z.string().min(1, 'Date format is required').default('MMM dd, yyyy'),
  currency: z.string().min(1, 'Currency is required').default('USD'),
  pageSize: z.number().min(5).max(100).default(10),
});

export const notificationSettingsSchema = z.object({
  email: z.boolean().default(true),
  push: z.boolean().default(true),
  sms: z.boolean().default(false),
  salesAlerts: z.boolean().default(true),
  weeklyReports: z.boolean().default(true),
  monthlyReports: z.boolean().default(true),
  systemUpdates: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
});

export const securitySettingsSchema = z.object({
  twoFactorEnabled: z.boolean().default(false),
  sessionTimeout: z.number().min(15).max(1440).default(60), // minutes
  passwordChangeRequired: z.boolean().default(false),
  loginNotifications: z.boolean().default(true),
  deviceTracking: z.boolean().default(true),
  apiAccessEnabled: z.boolean().default(false),
});

export const privacySettingsSchema = z.object({
  profileVisibility: z.enum(['public', 'private', 'team']).default('team'),
  dataSharing: z.boolean().default(false),
  analyticsTracking: z.boolean().default(true),
  cookiePreferences: z.object({
    necessary: z.boolean().default(true),
    functional: z.boolean().default(true),
    analytics: z.boolean().default(true),
    marketing: z.boolean().default(false),
  }),
});

export const backupSettingsSchema = z.object({
  autoBackup: z.boolean().default(true),
  backupFrequency: z.enum(['daily', 'weekly', 'monthly']).default('weekly'),
  retentionPeriod: z.number().min(7).max(365).default(30), // days
  includeFiles: z.boolean().default(false),
});

export const apiKeyCreateSchema = z.object({
  name: z.string().min(1, 'API key name is required').max(50),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
});

export const webhookCreateSchema = z.object({
  url: z.string().url('Invalid webhook URL'),
  events: z.array(z.string()).min(1, 'At least one event is required'),
});

export const webhookUpdateSchema = z.object({
  url: z.string().url('Invalid webhook URL').optional(),
  events: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
});

export const settingsImportSchema = z.object({
  file: z
    .instanceof(File)
    .refine(file => file.size <= 1024 * 1024, 'File size must be less than 1MB')
    .refine(file => file.type === 'application/json', 'File must be a JSON file'),
});

// Type exports
export type AppSettingsData = z.infer<typeof appSettingsSchema>;
export type NotificationSettingsData = z.infer<typeof notificationSettingsSchema>;
export type SecuritySettingsData = z.infer<typeof securitySettingsSchema>;
export type PrivacySettingsData = z.infer<typeof privacySettingsSchema>;
export type BackupSettingsData = z.infer<typeof backupSettingsSchema>;
export type ApiKeyCreateData = z.infer<typeof apiKeyCreateSchema>;
export type WebhookCreateData = z.infer<typeof webhookCreateSchema>;
export type WebhookUpdateData = z.infer<typeof webhookUpdateSchema>;
export type SettingsImportData = z.infer<typeof settingsImportSchema>;
