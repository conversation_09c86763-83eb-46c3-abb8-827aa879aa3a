import { apiClient } from '@/lib/api/client';
import { AllSettings, SettingsUpdateRequest } from '../types';

export const settingsApi = {
  // Get all settings
  getSettings: async (): Promise<AllSettings> => {
    const response = await apiClient.get('/api/settings');
    return response.data;
  },

  // Update settings
  updateSettings: async (data: SettingsUpdateRequest): Promise<AllSettings> => {
    const response = await apiClient.put('/api/settings', data);
    return response.data;
  },

  // Reset settings to defaults
  resetSettings: async (category?: keyof AllSettings): Promise<AllSettings> => {
    const response = await apiClient.post('/api/settings/reset', { category });
    return response.data;
  },

  // Export settings
  exportSettings: async (): Promise<Blob> => {
    const response = await apiClient.get('/api/settings/export', {
      responseType: 'blob',
    });
    return response.data;
  },

  // Import settings
  importSettings: async (file: File): Promise<AllSettings> => {
    const formData = new FormData();
    formData.append('settings', file);

    const response = await apiClient.post('/api/settings/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // API Key management
  createApiKey: async (
    name: string,
    permissions: string[]
  ): Promise<{ id: string; key: string }> => {
    const response = await apiClient.post('/api/settings/api-keys', { name, permissions });
    return response.data;
  },

  deleteApiKey: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/settings/api-keys/${id}`);
  },

  regenerateApiKey: async (id: string): Promise<{ key: string }> => {
    const response = await apiClient.post(`/api/settings/api-keys/${id}/regenerate`);
    return response.data;
  },

  // Webhook management
  createWebhook: async (url: string, events: string[]): Promise<{ id: string }> => {
    const response = await apiClient.post('/api/settings/webhooks', { url, events });
    return response.data;
  },

  updateWebhook: async (
    id: string,
    data: { url?: string; events?: string[]; isActive?: boolean }
  ): Promise<void> => {
    await apiClient.put(`/api/settings/webhooks/${id}`, data);
  },

  deleteWebhook: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/settings/webhooks/${id}`);
  },

  testWebhook: async (
    id: string
  ): Promise<{ success: boolean; response?: any; error?: string }> => {
    const response = await apiClient.post(`/api/settings/webhooks/${id}/test`);
    return response.data;
  },

  // Backup management
  createBackup: async (): Promise<{ id: string; downloadUrl: string }> => {
    const response = await apiClient.post('/api/settings/backup');
    return response.data;
  },

  getBackups: async (): Promise<
    Array<{
      id: string;
      createdAt: string;
      size: number;
      type: 'manual' | 'automatic';
    }>
  > => {
    const response = await apiClient.get('/api/settings/backups');
    return response.data;
  },

  downloadBackup: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/api/settings/backups/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  deleteBackup: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/settings/backups/${id}`);
  },

  restoreBackup: async (id: string): Promise<void> => {
    await apiClient.post(`/api/settings/backups/${id}/restore`);
  },
};
