import React, { useState, useCallback } from 'react';
import { Activity, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs';
import { Badge } from '@/shared/components/ui/badge';
import { AuditLogDisplay, AuditLogFilters, AuditLogsTable } from '../components';
import {
  useAuditLogs,
  useAuditLogStats,
  useAuditLogFilterOptions,
  useAuditLogFilters,
} from '../hooks';
import { exportAuditLogsToCSV, downloadFile } from '../utils';

const AuditLogsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('table');
  const [alert, setAlert] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Filters management
  const { filters, updateFilter, clearFilters, hasActiveFilters } = useAuditLogFilters({
    page: 1,
    limit: 50,
  });

  // Data fetching
  const {
    data: auditLogsData,
    isLoading: auditLogsLoading,
    error: auditLogsError,
    refetch: refetchAuditLogs,
  } = useAuditLogs(filters);

  const { data: stats, isLoading: _statsLoading } = useAuditLogStats();

  const { data: filterOptions, isLoading: filterOptionsLoading } = useAuditLogFilterOptions();

  // Clear alert after 5 seconds
  React.useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => setAlert(null), 5000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [alert]);

  const handleFiltersChange = useCallback(
    (newFilters: typeof filters) => {
      Object.entries(newFilters).forEach(([key, value]) => {
        updateFilter(key as keyof typeof filters, value);
      });
    },
    [updateFilter]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      updateFilter('page', page);
    },
    [updateFilter]
  );

  const handleRefresh = useCallback(() => {
    refetchAuditLogs();
  }, [refetchAuditLogs]);

  const handleExport = useCallback(async () => {
    try {
      if (auditLogsData?.logs) {
        const csvContent = exportAuditLogsToCSV(auditLogsData.logs);
        const timestamp = new Date().toISOString().split('T')[0];
        downloadFile(csvContent, `audit-logs-${timestamp}.csv`, 'text/csv');
        setAlert({ type: 'success', message: 'Audit logs exported successfully' });
      }
    } catch (error) {
      console.error('Export error:', error);
      setAlert({ type: 'error', message: 'Failed to export audit logs' });
    }
  }, [auditLogsData?.logs]);

  const auditLogs = auditLogsData?.logs || [];
  const pagination = auditLogsData?.pagination;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Activity className="text-primary h-8 w-8" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Audit Logs</h1>
            <p className="text-muted-foreground">
              Monitor and review system activity and user actions
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={auditLogsLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${auditLogsLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={auditLogsLoading || auditLogs.length === 0}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {auditLogsError && (
        <Alert className="border-destructive">
          <AlertDescription>
            Failed to load audit logs: {auditLogsError.message || 'Unknown error'}
          </AlertDescription>
        </Alert>
      )}

      {/* Success/Error Alert */}
      {alert && (
        <Alert className={alert.type === 'error' ? 'border-destructive' : 'border-green-500'}>
          <AlertDescription>{alert.message}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
              <Activity className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLogs?.toLocaleString() || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today</CardTitle>
              <Badge variant="secondary">{stats.todayLogs || 0}</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayLogs?.toLocaleString() || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <Badge variant="secondary">{stats.weekLogs || 0}</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.weekLogs?.toLocaleString() || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Badge variant="secondary">{stats.monthLogs || 0}</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.monthLogs?.toLocaleString() || 0}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <AuditLogFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        filterOptions={filterOptions}
        loading={auditLogsLoading || filterOptionsLoading}
        hasActiveFilters={hasActiveFilters}
        onClearFilters={clearFilters}
      />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <AuditLogsTable
            data={auditLogs}
            loading={auditLogsLoading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onExport={handleExport}
          />
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <AuditLogDisplay
            auditLogs={auditLogs}
            loading={auditLogsLoading}
            onRefresh={handleRefresh}
            title="System Audit Timeline"
            height="600px"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AuditLogsPage;
