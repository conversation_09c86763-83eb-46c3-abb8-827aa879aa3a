import { AuditLog, AuditLogFilters } from '../types';

/**
 * Get the timestamp from an audit log (handles both old and new field names)
 */
export const getAuditLogTimestamp = (log: AuditLog): string => {
  return log.createdAt || log.timestamp || '';
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
};

/**
 * Get action color classes for badges
 */
export const getActionColor = (action: string): string => {
  switch (action.toLowerCase()) {
    case 'create':
    case 'created':
    case 'register':
    case 'signup':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'update':
    case 'updated':
    case 'edit':
    case 'modified':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'delete':
    case 'deleted':
    case 'remove':
    case 'removed':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'login':
    case 'signin':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'logout':
    case 'signout':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    case 'password_reset':
    case 'password_change':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'role_change':
    case 'permission_change':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'activate':
    case 'activated':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'deactivate':
    case 'deactivated':
      return 'bg-slate-100 text-slate-800 border-slate-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Get action icon for display
 */
export const getActionIcon = (action: string): string => {
  switch (action.toLowerCase()) {
    case 'create':
    case 'created':
    case 'register':
    case 'signup':
      return '➕';
    case 'update':
    case 'updated':
    case 'edit':
    case 'modified':
      return '✏️';
    case 'delete':
    case 'deleted':
    case 'remove':
    case 'removed':
      return '🗑️';
    case 'login':
    case 'signin':
      return '🔓';
    case 'logout':
    case 'signout':
      return '🔒';
    case 'password_reset':
    case 'password_change':
      return '🔑';
    case 'role_change':
    case 'permission_change':
      return '👤';
    case 'activate':
    case 'activated':
      return '✅';
    case 'deactivate':
    case 'deactivated':
      return '❌';
    default:
      return '📝';
  }
};

/**
 * Format action details for display
 */
export const formatActionDetails = (details: Record<string, any>): string | null => {
  if (!details || Object.keys(details).length === 0) return null;

  return Object.entries(details)
    .map(
      ([key, value]) =>
        `${key}: ${typeof value === 'object' ? JSON.stringify(value) : String(value)}`
    )
    .join(', ');
};

/**
 * Filter audit logs based on search criteria
 */
export const filterAuditLogs = (logs: AuditLog[], filters: AuditLogFilters): AuditLog[] => {
  return logs.filter(log => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableFields = [
        log.action,
        log.performedByName,
        log.ipAddress,
        JSON.stringify(log.details),
      ].filter(Boolean);

      const matchesSearch = searchableFields.some(field =>
        field?.toLowerCase().includes(searchTerm)
      );

      if (!matchesSearch) return false;
    }

    // Action filter
    if (filters.action && log.action !== filters.action) {
      return false;
    }

    // User ID filter
    if (filters.userId && log.userId !== filters.userId) {
      return false;
    }

    // Performed by filter
    if (filters.performedBy && log.performedBy !== filters.performedBy) {
      return false;
    }

    // Date range filter
    if (filters.dateFrom || filters.dateTo) {
      const logDate = new Date(getAuditLogTimestamp(log));

      if (filters.dateFrom && logDate < new Date(filters.dateFrom)) {
        return false;
      }

      if (filters.dateTo && logDate > new Date(filters.dateTo)) {
        return false;
      }
    }

    // Resource type filter
    if (filters.resourceType && log.resourceType !== filters.resourceType) {
      return false;
    }

    return true;
  });
};

/**
 * Export audit logs to CSV format
 */
export const exportAuditLogsToCSV = (logs: AuditLog[]): string => {
  const headers = [
    'ID',
    'Action',
    'User ID',
    'Performed By',
    'Performed By Name',
    'Timestamp',
    'IP Address',
    'Resource Type',
    'Resource ID',
    'Details',
  ];

  const csvContent = [
    headers.join(','),
    ...logs.map(log =>
      [
        log.id,
        log.action,
        log.userId || '',
        log.performedBy || '',
        log.performedByName || '',
        getAuditLogTimestamp(log),
        log.ipAddress || '',
        log.resourceType || '',
        log.resourceId || '',
        JSON.stringify(log.details || log.newValues || {}).replace(/"/g, '""'),
      ]
        .map(field => `"${String(field)}"`)
        .join(',')
    ),
  ].join('\n');

  return csvContent;
};

/**
 * Download data as file
 */
export const downloadFile = (
  content: string,
  filename: string,
  mimeType: string = 'text/plain'
): void => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Debounce function for search inputs
 */
export const debounce = (func: Function, wait: number) => {
  let timeout: any;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
