import React from 'react';
import { Search, Filter, X, Calendar } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { AuditLogFilters as AuditLogFiltersType, AuditLogFilterOptions } from '../types';

interface AuditLogFiltersProps {
  filters: AuditLogFiltersType;
  onFiltersChange: (filters: AuditLogFiltersType) => void;
  filterOptions?: AuditLogFilterOptions;
  loading?: boolean;
  hasActiveFilters?: boolean;
  onClearFilters?: () => void;
}

const AuditLogFilters: React.FC<AuditLogFiltersProps> = ({
  filters,
  onFiltersChange,
  filterOptions,
  loading = false,
  hasActiveFilters = false,
  onClearFilters,
}) => {
  const handleFilterChange = <K extends keyof AuditLogFiltersType>(
    key: K,
    value: AuditLogFiltersType[K]
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      // Reset to first page when filters change
      page: 1,
    });
  };

  const handleClearFilters = () => {
    if (onClearFilters) {
      onClearFilters();
    } else {
      onFiltersChange({
        search: '',
        action: undefined,
        userId: undefined,
        performedBy: undefined,
        dateFrom: undefined,
        dateTo: undefined,
        resourceType: undefined,
        page: 1,
        limit: filters.limit || 50,
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <CardTitle className="text-lg">Filters</CardTitle>
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                Active
              </Badge>
            )}
          </div>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="text-muted-foreground"
            >
              <X className="mr-1 h-4 w-4" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
            <Input
              id="search"
              placeholder="Search actions, users, IP addresses..."
              value={filters.search || ''}
              onChange={e => handleFilterChange('search', e.target.value)}
              className="pl-10"
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* Action Filter */}
          <div className="space-y-2">
            <Label htmlFor="action">Action</Label>
            <Select
              value={filters.action || 'all'}
              onValueChange={value =>
                handleFilterChange('action', value === 'all' ? undefined : value)
              }
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All actions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All actions</SelectItem>
                {filterOptions?.actions?.map(action => (
                  <SelectItem key={action.value} value={action.value}>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${action.color}`} />
                      {action.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* User Filter */}
          <div className="space-y-2">
            <Label htmlFor="user">User</Label>
            <Select
              value={filters.userId?.toString() || 'all'}
              onValueChange={value =>
                handleFilterChange('userId', value === 'all' ? undefined : parseInt(value))
              }
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All users" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All users</SelectItem>
                {filterOptions?.users?.map(user => (
                  <SelectItem key={user.userId} value={user.userId.toString()}>
                    {user.userName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Resource Type Filter */}
          <div className="space-y-2">
            <Label htmlFor="resourceType">Resource Type</Label>
            <Select
              value={filters.resourceType || 'all'}
              onValueChange={value =>
                handleFilterChange('resourceType', value === 'all' ? undefined : value)
              }
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="All resources" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All resources</SelectItem>
                {filterOptions?.resourceTypes?.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="dateFrom">From Date</Label>
            <div className="relative">
              <Calendar className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                id="dateFrom"
                type="datetime-local"
                value={filters.dateFrom || ''}
                onChange={e => handleFilterChange('dateFrom', e.target.value || undefined)}
                className="pl-10"
                disabled={loading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="dateTo">To Date</Label>
            <div className="relative">
              <Calendar className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                id="dateTo"
                type="datetime-local"
                value={filters.dateTo || ''}
                onChange={e => handleFilterChange('dateTo', e.target.value || undefined)}
                className="pl-10"
                disabled={loading}
              />
            </div>
          </div>
        </div>

        {/* Results per page */}
        <div className="space-y-2">
          <Label htmlFor="limit">Results per page</Label>
          <Select
            value={filters.limit?.toString() || '50'}
            onValueChange={value => handleFilterChange('limit', parseInt(value))}
            disabled={loading}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
              <SelectItem value="200">200</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuditLogFilters;
