import React from 'react';
import { Clock, Activity, RefreshCw } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import { Separator } from '@/shared/components/ui/separator';
import { AuditLog } from '../types';
import {
  formatDate,
  formatRelativeTime,
  getActionColor,
  getActionIcon,
  formatActionDetails,
  getAuditLogTimestamp,
} from '../utils';

interface AuditLogDisplayProps {
  userId?: number | null;
  auditLogs: AuditLog[];
  loading: boolean;
  onRefresh: () => void;
  title?: string;
  height?: string;
}

const AuditLogDisplay: React.FC<AuditLogDisplayProps> = ({
  userId,
  auditLogs,
  loading,
  onRefresh,
  title,
  height = '400px',
}) => {
  const displayTitle = title || (userId ? 'User Activity Log' : 'System Audit Log');

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          <h3 className="text-lg font-semibold">{displayTitle}</h3>
        </div>
        <Button variant="outline" size="sm" onClick={onRefresh} disabled={loading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <RefreshCw className="mr-2 h-6 w-6 animate-spin" />
              Loading audit logs...
            </div>
          </CardContent>
        </Card>
      ) : auditLogs.length === 0 ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-muted-foreground text-center">
              <Activity className="mx-auto mb-4 h-12 w-12 opacity-50" />
              <p>No audit logs found</p>
              <p className="text-sm">
                {userId ? 'This user has no recorded activity' : 'No system activity recorded'}
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Activity Timeline ({auditLogs.length} entries)
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className={`h-[${height}]`}>
              <div className="space-y-4 p-4">
                {auditLogs.map((log, index) => (
                  <div key={log.id} className="relative">
                    {index < auditLogs.length - 1 && (
                      <div className="bg-border absolute left-6 top-12 h-full w-px" />
                    )}

                    <div className="flex gap-4">
                      <div className="flex-shrink-0">
                        <div className="bg-muted flex h-12 w-12 items-center justify-center rounded-full text-lg">
                          {getActionIcon(log.action)}
                        </div>
                      </div>

                      <div className="min-w-0 flex-1">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="mb-1 flex items-center gap-2">
                              <Badge className={getActionColor(log.action)}>{log.action}</Badge>
                              <span className="text-muted-foreground text-sm">
                                by {log.performedByName}
                              </span>
                            </div>

                            <div className="text-muted-foreground mb-2 flex items-center gap-2 text-sm">
                              <Clock className="h-3 w-3" />
                              <span>{formatDate(getAuditLogTimestamp(log))}</span>
                              <span>({formatRelativeTime(getAuditLogTimestamp(log))})</span>
                            </div>

                            {log.ipAddress && (
                              <div className="text-muted-foreground mb-2 flex items-center gap-2 text-xs">
                                <span>IP: {log.ipAddress}</span>
                              </div>
                            )}

                            {log.resourceType && (
                              <div className="text-muted-foreground mb-2 flex items-center gap-2 text-xs">
                                <span>Resource: {log.resourceType}</span>
                                {log.resourceId && <span>ID: {log.resourceId}</span>}
                              </div>
                            )}

                            {log.details && formatActionDetails(log.details) && (
                              <div className="bg-muted mt-2 rounded p-2 text-xs">
                                <p className="mb-1 font-medium">Details:</p>
                                <p>{formatActionDetails(log.details)}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {index < auditLogs.length - 1 && <Separator className="mt-4" />}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AuditLogDisplay;
