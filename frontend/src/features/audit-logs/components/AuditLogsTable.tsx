import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { ArrowUpDown, ChevronLeft, ChevronRight, Eye, Download } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { AuditLog } from '../types';
import { formatDate, formatRelativeTime, getActionColor, getAuditLogTimestamp } from '../utils';

interface AuditLogsTableProps {
  data: AuditLog[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange?: (page: number) => void;
  onExport?: () => void;
}

const columnHelper = createColumnHelper<AuditLog>();

const AuditLogsTable: React.FC<AuditLogsTableProps> = ({
  data,
  loading = false,
  pagination,
  onPageChange,
  onExport,
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  const columns = useMemo(
    () => [
      columnHelper.accessor('timestamp', {
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-semibold"
          >
            Timestamp
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        ),
        cell: ({ row }) => {
          const timestamp = getAuditLogTimestamp(row.original);
          return (
            <div className="space-y-1">
              <div className="text-sm font-medium">{formatDate(timestamp)}</div>
              <div className="text-muted-foreground text-xs">{formatRelativeTime(timestamp)}</div>
            </div>
          );
        },
        size: 180,
      }),

      columnHelper.accessor('action', {
        header: 'Action',
        cell: ({ row }) => (
          <Badge className={getActionColor(row.original.action)}>{row.original.action}</Badge>
        ),
        size: 120,
      }),

      columnHelper.accessor('performedByName', {
        header: 'Performed By',
        cell: ({ row }) => (
          <div className="space-y-1">
            <div className="text-sm font-medium">{row.original.performedByName}</div>
            <div className="text-muted-foreground text-xs">ID: {row.original.performedBy}</div>
          </div>
        ),
        size: 150,
      }),

      columnHelper.accessor('userId', {
        header: 'Target User',
        cell: ({ row }) => (
          <div className="text-sm">
            {row.original.userId ? `User ID: ${row.original.userId}` : 'System'}
          </div>
        ),
        size: 100,
      }),

      columnHelper.accessor('resourceType', {
        header: 'Resource',
        cell: ({ row }) => (
          <div className="space-y-1">
            {row.original.resourceType && (
              <div className="text-sm font-medium">{row.original.resourceType}</div>
            )}
            {row.original.resourceId && (
              <div className="text-muted-foreground text-xs">ID: {row.original.resourceId}</div>
            )}
          </div>
        ),
        size: 120,
      }),

      columnHelper.accessor('ipAddress', {
        header: 'IP Address',
        cell: ({ row }) => (
          <div className="font-mono text-sm">{row.original.ipAddress || 'N/A'}</div>
        ),
        size: 120,
      }),

      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm">
                <Eye className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Audit Log Details</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-muted-foreground text-sm font-medium">Action</label>
                    <div className="mt-1">
                      <Badge className={getActionColor(row.original.action)}>
                        {row.original.action}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-muted-foreground text-sm font-medium">Timestamp</label>
                    <div className="mt-1 text-sm">
                      {formatDate(getAuditLogTimestamp(row.original))}
                    </div>
                  </div>
                  <div>
                    <label className="text-muted-foreground text-sm font-medium">
                      Performed By
                    </label>
                    <div className="mt-1 text-sm">{row.original.performedByName}</div>
                  </div>
                  <div>
                    <label className="text-muted-foreground text-sm font-medium">IP Address</label>
                    <div className="mt-1 font-mono text-sm">{row.original.ipAddress || 'N/A'}</div>
                  </div>
                </div>

                {Object.keys(row.original.details || {}).length > 0 && (
                  <div>
                    <label className="text-muted-foreground text-sm font-medium">Details</label>
                    <div className="bg-muted mt-1 rounded-md p-3">
                      <pre className="whitespace-pre-wrap text-xs">
                        {JSON.stringify(row.original.details, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        ),
        size: 80,
      }),
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: !!pagination,
    pageCount: pagination?.totalPages || -1,
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Audit Logs</CardTitle>
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id} style={{ width: header.getSize() }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Loading audit logs...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No audit logs found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination && (
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-muted-foreground text-sm">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total}{' '}
              entries
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AuditLogsTable;
