import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  AuditLog,
  AuditLogFilters,
  AuditLogResponse,
  AuditLogStats,
  AuditLogFilterOptions,
  AuditLogExportRequest,
  AuditLogExportResponse,
} from '../types';

export const auditLogsApi = {
  // Get system audit logs with filters and pagination
  getAuditLogs: async (filters?: AuditLogFilters): Promise<AuditLogResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.AUDIT_LOGS.LIST, {
      params: filters,
    });

    return {
      logs: response.data.data.logs,
      pagination: response.data.data.pagination,
    };
  },

  // Get audit logs for a specific user
  getUserAuditLogs: async (
    userId: number,
    filters?: Omit<AuditLogFilters, 'userId'>
  ): Promise<AuditLogResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.AUDIT_LOGS.USER_LOGS(userId), {
      params: filters,
    });

    return {
      logs: response.data.data.logs,
      pagination: response.data.data.pagination,
    };
  },

  // Get audit log statistics
  getAuditLogStats: async (
    filters?: Pick<AuditLogFilters, 'dateFrom' | 'dateTo'>
  ): Promise<AuditLogStats> => {
    const response = await apiClient.get(API_ENDPOINTS.AUDIT_LOGS.STATS, {
      params: filters,
    });

    return response.data.data;
  },

  // Get filter options for dropdowns
  getFilterOptions: async (): Promise<AuditLogFilterOptions> => {
    const response = await apiClient.get(API_ENDPOINTS.AUDIT_LOGS.FILTER_OPTIONS);

    return response.data.data;
  },

  // Export audit logs
  exportAuditLogs: async (request: AuditLogExportRequest): Promise<AuditLogExportResponse> => {
    const response = await apiClient.post(API_ENDPOINTS.AUDIT_LOGS.LIST + '/export', request, {
      responseType: 'blob',
    });

    // Create blob URL for download
    const blob = new Blob([response.data], { type: 'text/csv' });
    const downloadUrl = URL.createObjectURL(blob);

    return {
      downloadUrl,
      fileName: `audit-logs-${new Date().toISOString().split('T')[0]}.csv`,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60).toISOString(), // 1 hour from now
    };
  },

  // Get single audit log details
  getAuditLogDetails: async (logId: string): Promise<AuditLog> => {
    const response = await apiClient.get(API_ENDPOINTS.AUDIT_LOGS.GET(logId));

    return response.data.data;
  },

  // Create audit log entry
  createAuditLog: async (
    data: Omit<AuditLog, 'id' | 'timestamp' | 'performedByName'>
  ): Promise<AuditLog> => {
    const response = await apiClient.post(API_ENDPOINTS.AUDIT_LOGS.CREATE, data);

    return response.data.data;
  },

  // Delete audit log (soft delete)
  deleteAuditLog: async (logId: string): Promise<void> => {
    await apiClient.delete(API_ENDPOINTS.AUDIT_LOGS.DELETE(logId));
  },

  // Restore audit log
  restoreAuditLog: async (logId: string): Promise<void> => {
    await apiClient.post(API_ENDPOINTS.AUDIT_LOGS.RESTORE(logId));
  },

  // Get audit logs for a specific resource
  getResourceAuditLogs: async (
    resourceType: string,
    resourceId: string,
    filters?: Omit<AuditLogFilters, 'resourceType' | 'resourceId'>
  ): Promise<AuditLogResponse> => {
    const response = await apiClient.get(
      API_ENDPOINTS.AUDIT_LOGS.RESOURCE_LOGS(resourceType, resourceId),
      { params: filters }
    );

    return {
      logs: response.data.data.logs,
      pagination: response.data.data.pagination,
    };
  },
};
