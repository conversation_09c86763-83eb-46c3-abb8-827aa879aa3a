// Audit Logs Types - Aligned with Backend Model

export interface AuditLog {
  id: string;
  userId?: number | null;
  action: string;
  resourceType?: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  deletedAt?: string | null;
  // Fields from joined user data
  performedByName?: string;
  performedByEmail?: string;
  // Legacy fields for backward compatibility
  performedBy?: number;
  timestamp?: string;
  details?: Record<string, any>;
}

export interface AuditLogFilters {
  search?: string;
  action?: string;
  userId?: number;
  performedBy?: number;
  dateFrom?: string;
  dateTo?: string;
  resourceType?: string;
  page?: number;
  limit?: number;
}

export interface AuditLogResponse {
  logs: AuditLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AuditLogAction {
  value: string;
  label: string;
  color: string;
}

export interface AuditLogStats {
  totalLogs: number;
  todayLogs: number;
  weekLogs: number;
  monthLogs: number;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  topUsers: Array<{
    userId: number;
    userName: string;
    count: number;
  }>;
}

// Filter options for dropdowns
export interface AuditLogFilterOptions {
  actions: AuditLogAction[];
  users: Array<{
    userId: number;
    userName: string;
  }>;
  resourceTypes: Array<{
    value: string;
    label: string;
  }>;
}

// Table column configuration
export interface AuditLogTableColumn {
  key: keyof AuditLog | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

// Export/Import types
export interface AuditLogExportRequest {
  filters?: AuditLogFilters;
  format: 'csv' | 'json' | 'xlsx';
  dateRange?: {
    from: string;
    to: string;
  };
}

export interface AuditLogExportResponse {
  downloadUrl: string;
  fileName: string;
  expiresAt: string;
}
