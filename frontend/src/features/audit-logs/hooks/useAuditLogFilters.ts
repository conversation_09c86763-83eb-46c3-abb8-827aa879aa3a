import { useState, useCallback, useMemo } from 'react';
import { AuditLogFilters } from '../types';

export interface UseAuditLogFiltersReturn {
  filters: AuditLogFilters;
  setFilters: (filters: AuditLogFilters) => void;
  updateFilter: <K extends keyof AuditLogFilters>(key: K, value: AuditLogFilters[K]) => void;
  clearFilters: () => void;
  resetPagination: () => void;
  hasActiveFilters: boolean;
}

const defaultFilters: AuditLogFilters = {
  search: '',
  action: undefined,
  userId: undefined,
  performedBy: undefined,
  dateFrom: undefined,
  dateTo: undefined,
  resourceType: undefined,
  page: 1,
  limit: 50,
};

export function useAuditLogFilters(
  initialFilters?: Partial<AuditLogFilters>
): UseAuditLogFiltersReturn {
  const [filters, setFiltersState] = useState<AuditLogFilters>({
    ...defaultFilters,
    ...initialFilters,
  });

  const setFilters = useCallback((newFilters: AuditLogFilters) => {
    setFiltersState(newFilters);
  }, []);

  const updateFilter = useCallback(
    <K extends keyof AuditLogFilters>(key: K, value: AuditLogFilters[K]) => {
      setFiltersState(prev => ({
        ...prev,
        [key]: value,
        // Reset to first page when filters change (except when updating page itself)
        ...(key !== 'page' && { page: 1 }),
      }));
    },
    []
  );

  const clearFilters = useCallback(() => {
    setFiltersState(defaultFilters);
  }, []);

  const resetPagination = useCallback(() => {
    setFiltersState(prev => ({
      ...prev,
      page: 1,
    }));
  }, []);

  const hasActiveFilters = useMemo(() => {
    return Object.entries(filters).some(([key, value]) => {
      if (key === 'page' || key === 'limit') return false;
      return value !== undefined && value !== null && value !== '';
    });
  }, [filters]);

  return {
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    resetPagination,
    hasActiveFilters,
  };
}
