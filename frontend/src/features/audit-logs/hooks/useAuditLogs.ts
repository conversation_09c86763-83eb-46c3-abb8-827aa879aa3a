import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { auditLogsApi } from '../services/auditLogsApi';
import { AuditLogFilters, AuditLogExportRequest } from '../types';

// Query keys for audit logs
export const AUDIT_LOG_QUERY_KEYS = {
  all: ['audit-logs'] as const,
  lists: () => [...AUDIT_LOG_QUERY_KEYS.all, 'list'] as const,
  list: (filters: AuditLogFilters) => [...AUDIT_LOG_QUERY_KEYS.lists(), filters] as const,
  details: () => [...AUDIT_LOG_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...AUDIT_LOG_QUERY_KEYS.details(), id] as const,
  stats: () => [...AUDIT_LOG_QUERY_KEYS.all, 'stats'] as const,
  options: () => [...AUDIT_LOG_QUERY_KEYS.all, 'options'] as const,
  userLogs: (userId: number) => [...AUDIT_LOG_QUERY_KEYS.all, 'user', userId] as const,
};

// Main audit logs query hook
export function useAuditLogs(filters: AuditLogFilters = {}) {
  return useQuery({
    queryKey: AUDIT_LOG_QUERY_KEYS.list(filters),
    queryFn: () => auditLogsApi.getAuditLogs(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// User-specific audit logs query hook
export function useUserAuditLogs(userId: number, filters: Omit<AuditLogFilters, 'userId'> = {}) {
  return useQuery({
    queryKey: AUDIT_LOG_QUERY_KEYS.userLogs(userId),
    queryFn: () => auditLogsApi.getUserAuditLogs(userId, filters),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Audit log statistics query hook
export function useAuditLogStats(filters?: Pick<AuditLogFilters, 'dateFrom' | 'dateTo'>) {
  return useQuery({
    queryKey: [...AUDIT_LOG_QUERY_KEYS.stats(), filters],
    queryFn: () => auditLogsApi.getAuditLogStats(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

// Filter options query hook
export function useAuditLogFilterOptions() {
  return useQuery({
    queryKey: AUDIT_LOG_QUERY_KEYS.options(),
    queryFn: () => auditLogsApi.getFilterOptions(),
    staleTime: 10 * 60 * 1000, // 10 minutes - options don't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

// Single audit log details query hook
export function useAuditLogDetails(logId: string) {
  return useQuery({
    queryKey: AUDIT_LOG_QUERY_KEYS.detail(logId),
    queryFn: () => auditLogsApi.getAuditLogDetails(logId),
    enabled: !!logId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Export audit logs mutation hook
export function useExportAuditLogs() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: AuditLogExportRequest) => auditLogsApi.exportAuditLogs(request),
    onSuccess: () => {
      // Optionally invalidate related queries
      queryClient.invalidateQueries({
        queryKey: AUDIT_LOG_QUERY_KEYS.all,
      });
    },
    retry: 1,
  });
}

// Refresh audit logs helper
export function useRefreshAuditLogs() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: AUDIT_LOG_QUERY_KEYS.all,
    });
  };
}
