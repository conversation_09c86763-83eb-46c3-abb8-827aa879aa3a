import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCreateSale, useBulkCreateSales } from './useSales';
import { saleFormSchema, SaleFormData } from '../validations/salesSchemas';
import { useAuth } from '@/features/auth/hooks/useAuth';

export function useSalesEntry() {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createSaleMutation = useCreateSale();

  const form = useForm<SaleFormData>({
    resolver: zodResolver(saleFormSchema),
    defaultValues: {
      restaurantId: '',
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      description: '',
    },
  });

  const onSubmit = async (data: SaleFormData) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      await createSaleMutation.mutateAsync(data);
      form.reset();
      return { success: true };
    } catch (error) {
      return { success: false, error };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    onSubmit,
    isSubmitting: isSubmitting || createSaleMutation.isPending,
    error: createSaleMutation.error,
    isSuccess: createSaleMutation.isSuccess,
  };
}

export function useBulkSalesEntry() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const bulkCreateMutation = useBulkCreateSales();

  const handleFileUpload = async (file: File) => {
    setIsSubmitting(true);
    setUploadProgress(0);

    try {
      // Parse CSV/Excel file
      const salesData = await parseFile(file);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await bulkCreateMutation.mutateAsync(salesData);

      clearInterval(progressInterval);
      setUploadProgress(100);

      return { success: true, count: salesData.length };
    } catch (error) {
      setUploadProgress(0);
      return { success: false, error };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleFileUpload,
    isSubmitting,
    uploadProgress,
    error: bulkCreateMutation.error,
    isSuccess: bulkCreateMutation.isSuccess,
  };
}

// Helper function to parse file (simplified)
async function parseFile(file: File): Promise<SaleFormData[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = e => {
      try {
        const text = e.target?.result as string;
        const lines = text.split('\n');
        const headers = lines[0].split(',');

        const sales: SaleFormData[] = lines
          .slice(1)
          .filter(line => line.trim())
          .map(line => {
            const values = line.split(',');
            return {
              restaurantId: values[0]?.trim() || '',
              amount: parseFloat(values[1]?.trim() || '0'),
              date: values[2]?.trim() || new Date().toISOString().split('T')[0],
              description: values[3]?.trim() || '',
            };
          })
          .filter(sale => sale.restaurantId && sale.amount > 0);

        resolve(sales);
      } catch (error) {
        reject(new Error('Failed to parse file'));
      }
    };

    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}
