import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { salesApi } from '../services/salesApi';
import { SalesFiltersData } from '../types';

export function useSales(filters?: Partial<SalesFiltersData>) {
  return useQuery({
    queryKey: [QUERY_KEYS.SALES, 'list', filters],
    queryFn: () => salesApi.getSales(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useSale(id: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.SALES, 'detail', id],
    queryFn: () => salesApi.getSale(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: salesApi.createSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DASHBOARD] });
    },
  });
}

export function useUpdateSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => salesApi.updateSale(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES, 'detail', id] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DASHBOARD] });
    },
  });
}

export function useDeleteSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: salesApi.deleteSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DASHBOARD] });
    },
  });
}

export function useBulkCreateSales() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: salesApi.bulkCreateSales,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DASHBOARD] });
    },
  });
}

export function useBulkDeleteSales() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: salesApi.bulkDeleteSales,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SALES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DASHBOARD] });
    },
  });
}
