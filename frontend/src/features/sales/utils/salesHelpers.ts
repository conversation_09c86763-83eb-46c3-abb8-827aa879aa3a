import { Sale, SalesMetrics, SalesChartData } from '../types';
import { formatCurrency, formatNumber } from '@/shared/utils/format';

/**
 * Calculate total revenue from sales array
 */
export function calculateTotalRevenue(sales: Sale[]): number {
  return sales.reduce((total, sale) => total + sale.amount, 0);
}

/**
 * Calculate average order value
 */
export function calculateAverageOrderValue(sales: Sale[]): number {
  if (sales.length === 0) return 0;
  return calculateTotalRevenue(sales) / sales.length;
}

/**
 * Group sales by date
 */
export function groupSalesByDate(sales: Sale[]): Record<string, Sale[]> {
  return sales.reduce(
    (groups, sale) => {
      const date = sale.date.split('T')[0]; // Get date part only
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(sale);
      return groups;
    },
    {} as Record<string, Sale[]>
  );
}

/**
 * Generate sales chart data from sales array
 */
export function generateSalesChartData(sales: Sale[]): SalesChartData[] {
  const groupedSales = groupSalesByDate(sales);

  return Object.entries(groupedSales).map(([date, dateSales]) => ({
    date,
    sales: dateSales.length,
    revenue: calculateTotalRevenue(dateSales),
  }));
}

/**
 * Calculate sales growth percentage
 */
export function calculateGrowthPercentage(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

/**
 * Format sales metrics for display
 */
export function formatSalesMetrics(metrics: SalesMetrics) {
  return {
    todaySales: formatNumber(metrics.todaySales),
    todayRevenue: formatCurrency(metrics.todayRevenue),
    monthSales: formatNumber(metrics.monthSales),
    monthRevenue: formatCurrency(metrics.monthRevenue),
    yearSales: formatNumber(metrics.yearSales),
    yearRevenue: formatCurrency(metrics.yearRevenue),
    salesGrowth: `${metrics.growth.salesGrowth >= 0 ? '+' : ''}${formatNumber(metrics.growth.salesGrowth, 1)}%`,
    revenueGrowth: `${metrics.growth.revenueGrowth >= 0 ? '+' : ''}${formatNumber(metrics.growth.revenueGrowth, 1)}%`,
  };
}

/**
 * Filter sales by date range
 */
export function filterSalesByDateRange(sales: Sale[], startDate: string, endDate: string): Sale[] {
  const start = new Date(startDate);
  const end = new Date(endDate);

  return sales.filter(sale => {
    const saleDate = new Date(sale.date);
    return saleDate >= start && saleDate <= end;
  });
}

/**
 * Get top performing periods
 */
export function getTopPerformingPeriods(
  sales: Sale[],
  periodType: 'day' | 'week' | 'month' = 'day',
  limit: number = 5
) {
  const groupedSales = groupSalesByDate(sales);

  const periods = Object.entries(groupedSales).map(([date, dateSales]) => ({
    period: date,
    sales: dateSales.length,
    revenue: calculateTotalRevenue(dateSales),
    averageOrderValue: calculateAverageOrderValue(dateSales),
  }));

  return periods.sort((a, b) => b.revenue - a.revenue).slice(0, limit);
}

/**
 * Validate sales data
 */
export function validateSalesData(sales: any[]): { valid: Sale[]; invalid: any[] } {
  const valid: Sale[] = [];
  const invalid: any[] = [];

  sales.forEach(sale => {
    if (
      sale.id &&
      sale.restaurantId &&
      sale.userId &&
      typeof sale.amount === 'number' &&
      sale.amount > 0 &&
      sale.date &&
      !isNaN(Date.parse(sale.date))
    ) {
      valid.push(sale as Sale);
    } else {
      invalid.push(sale);
    }
  });

  return { valid, invalid };
}

/**
 * Export sales data to CSV format
 */
export function exportSalesToCSV(sales: Sale[]): string {
  const headers = ['ID', 'Restaurant ID', 'User ID', 'Amount', 'Date', 'Description'];
  const rows = sales.map(sale => [
    sale.id,
    sale.restaurantId,
    sale.userId,
    sale.amount.toString(),
    sale.date,
    sale.description || '',
  ]);

  return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
}
