import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { profileApi } from '../services/profileApi';
import { ProfileUpdateRequest, PasswordChangeRequest, ProfileData } from '../types';
import { ProfileUpdateData, PasswordChangeData } from '../validations/profileSchemas';

// Query keys for consistent cache management
export const PROFILE_QUERY_KEYS = {
  all: ['profile'] as const,
  profile: () => [...PROFILE_QUERY_KEYS.all, 'data'] as const,
  stats: () => [...PROFILE_QUERY_KEYS.all, 'stats'] as const,
  activity: (limit?: number) => [...PROFILE_QUERY_KEYS.all, 'activity', limit] as const,
};

// Get current user profile
export function useProfile() {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.profile(),
    queryFn: profileApi.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Get profile statistics
export function useProfileStats() {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.stats(),
    queryFn: profileApi.getProfileStats,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

// Get activity log
export function useActivityLog(limit: number = 50) {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.activity(limit),
    queryFn: () => profileApi.getActivityLog(limit),
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
  });
}

// Update profile mutation
export function useUpdateProfile(
  options: {
    onSuccess?: (data: ProfileData) => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ProfileUpdateData) => {
      const updateRequest: ProfileUpdateRequest = {
        fullName: data.fullName,
        email: data.email,
      };
      return await profileApi.updateProfile(updateRequest);
    },
    onSuccess: data => {
      // Update the profile cache with new data
      queryClient.setQueryData(PROFILE_QUERY_KEYS.profile(), data);

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.stats() });
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.activity() });

      toast.success('Profile updated successfully');
      options.onSuccess?.(data);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to update profile';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: (failureCount, error: any) => {
      // Retry on network errors or server errors
      if (error?.code === 'NETWORK_ERROR' && failureCount < 2) return true;
      if (error?.response?.status >= 500 && failureCount < 1) return true;
      return false;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
  });
}

// Change password mutation
export function useChangePassword(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PasswordChangeData) => {
      const changeRequest: PasswordChangeRequest = {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      };
      return await profileApi.changePassword(changeRequest);
    },
    onSuccess: () => {
      // Invalidate activity log to show password change activity
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.activity() });

      toast.success('Password changed successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to change password';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors (401, 403)
      if (error?.response?.status === 401 || error?.response?.status === 403) return false;
      // Retry on network errors or server errors
      if (error?.code === 'NETWORK_ERROR' && failureCount < 2) return true;
      if (error?.response?.status >= 500 && failureCount < 1) return true;
      return false;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
  });
}

// Upload avatar mutation
export function useUploadAvatar(
  options: {
    onSuccess?: (data: { avatarUrl: string }) => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: profileApi.uploadAvatar,
    onSuccess: data => {
      // Invalidate profile to refetch with new avatar
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.profile() });
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.activity() });

      toast.success('Avatar uploaded successfully');
      options.onSuccess?.(data);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to upload avatar';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: 1,
  });
}

// Delete avatar mutation
export function useDeleteAvatar(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: profileApi.deleteAvatar,
    onSuccess: () => {
      // Invalidate profile to refetch without avatar
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.profile() });
      queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEYS.activity() });

      toast.success('Avatar removed successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to remove avatar';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: 1,
  });
}

// Export profile data mutation
export function useExportProfileData(
  options: {
    onSuccess?: (data: Blob) => void;
    onError?: (error: any) => void;
  } = {}
) {
  return useMutation({
    mutationFn: profileApi.exportProfileData,
    onSuccess: data => {
      // Create download link for the exported data
      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = `profile-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Profile data exported successfully');
      options.onSuccess?.(data);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to export profile data';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: 1,
  });
}

// Delete account mutation
export function useDeleteAccount(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: profileApi.deleteAccount,
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();

      toast.success('Account deleted successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete account';
      toast.error(errorMessage);
      options.onError?.(error);
    },
    retry: false, // Don't retry account deletion
  });
}
