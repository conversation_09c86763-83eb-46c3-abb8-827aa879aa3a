import { User } from '@/shared/types';

export interface ProfileUpdateRequest {
  fullName?: string;
  email?: string;
}

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ProfileData extends User {
  // Additional profile-specific fields can be added here
}

export interface ProfileStats {
  totalSales: number;
  totalRevenue: number;
  salesThisMonth: number;
  revenueThisMonth: number;
  averageOrderValue: number;
  salesCount: number;
  joinedDate: string;
  lastLoginAt?: string;
}

export interface ActivityLog {
  id: string;
  action: string;
  description: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}
