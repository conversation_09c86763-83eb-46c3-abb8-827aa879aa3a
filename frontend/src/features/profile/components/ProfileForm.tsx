import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { User, Mail, Eye, EyeOff, Lock, Key, CheckCircle2 } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/shared/components/ui/form';
import { LoadingButton } from '@/shared/components/forms/LoadingButton';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  profileUpdateSchema,
  passwordChangeSchema,
  ProfileUpdateData,
  PasswordChangeData,
} from '../validations/profileSchemas';
import { useProfile, useUpdateProfile, useChangePassword } from '../hooks';
import { cn } from '@/lib/utils';

interface ProfileFormProps {
  className?: string;
}

export function ProfileForm({ className }: ProfileFormProps) {
  // All hooks called at top level in consistent order
  const { user } = useAuth();

  // React Query hooks with proper configuration
  const { data: profileData, isLoading: isLoadingProfile } = useProfile();
  const updateProfileMutation = useUpdateProfile({
    onSuccess: () => {
      // Reset form dirty state on successful update
      profileForm.reset(profileForm.getValues());
    },
  });
  const changePasswordMutation = useChangePassword({
    onSuccess: () => {
      // Reset password form on successful change
      passwordForm.reset();
    },
  });

  // State hooks for password visibility
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Form hooks with enhanced configuration
  const profileForm = useForm<ProfileUpdateData>({
    resolver: zodResolver(profileUpdateSchema),
    defaultValues: {
      fullName: '',
      email: '',
    },
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  const passwordForm = useForm<PasswordChangeData>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  // Effect to populate form with user data when available
  useEffect(() => {
    const userData = profileData || user;
    if (userData) {
      profileForm.reset({
        fullName: userData.fullName || (userData as any).name || '',
        email: userData.email || '',
      });
    }
  }, [profileData, user, profileForm]);

  // Callback handlers with proper dependency arrays
  const handleProfileSubmit = useCallback(
    async (data: ProfileUpdateData) => {
      try {
        await updateProfileMutation.mutateAsync(data);
        // Success handling is done in the mutation onSuccess callback
      } catch (error) {
        // Error handling is done in the mutation onError callback
        console.error('Profile update error:', error);
      }
    },
    [updateProfileMutation]
  );

  const handlePasswordSubmit = useCallback(
    async (data: PasswordChangeData) => {
      try {
        await changePasswordMutation.mutateAsync(data);
        // Success handling is done in the mutation onSuccess callback
      } catch (error) {
        // Error handling is done in the mutation onError callback
        console.error('Password change error:', error);
      }
    },
    [changePasswordMutation]
  );

  const togglePasswordVisibility = useCallback((field: 'current' | 'new' | 'confirm') => {
    switch (field) {
      case 'current':
        setShowCurrentPassword(prev => !prev);
        break;
      case 'new':
        setShowNewPassword(prev => !prev);
        break;
      case 'confirm':
        setShowConfirmPassword(prev => !prev);
        break;
    }
  }, []);

  // Show enhanced loading state while profile data is being fetched
  if (isLoadingProfile) {
    return (
      <div className={cn('space-y-8', className)}>
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
          <p className="text-muted-foreground">Loading your profile information...</p>
        </div>

        <div className="grid gap-6 md:gap-8 xl:grid-cols-3">
          {/* Personal Information Loading */}
          <div className="xl:col-span-2">
            <Card className="border-muted/40">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <User className="text-primary h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-20 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-16 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-12 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="bg-muted h-10 w-full animate-pulse rounded" />
              </CardContent>
            </Card>
          </div>

          {/* Security Settings Loading */}
          <div>
            <Card className="border-muted/40">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Lock className="text-primary h-5 w-5" />
                  Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-24 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-28 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="space-y-2">
                  <div className="bg-muted h-4 w-32 animate-pulse rounded" />
                  <div className="bg-muted h-10 animate-pulse rounded" />
                </div>
                <div className="bg-muted h-10 w-full animate-pulse rounded" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6 md:space-y-8', className)}>
      {/* Header Section */}
      <div className="space-y-2 md:space-y-3">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Profile</h1>
        <p className="text-muted-foreground text-sm md:text-base">
          Manage your account information and security settings.
        </p>
      </div>

      <div className="grid gap-6 md:gap-8 xl:grid-cols-3">
        {/* Personal Information Card - Main Content */}
        <div className="xl:col-span-2">
          <Card className="border-muted/40">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-2 text-lg">
                <User className="text-primary h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...profileForm}>
                <form
                  onSubmit={profileForm.handleSubmit(handleProfileSubmit)}
                  className="space-y-6 md:space-y-8"
                >
                  <FormField
                    control={profileForm.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-sm font-medium">
                          <User className="text-muted-foreground h-4 w-4" />
                          Full Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            className="h-11 md:h-10"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-sm font-medium">
                          <Mail className="text-muted-foreground h-4 w-4" />
                          Email Address
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter your email address"
                            className="h-11 md:h-10"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This email will be used for account notifications and password recovery.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="pt-6 md:pt-4">
                    <LoadingButton
                      type="submit"
                      loading={updateProfileMutation.isPending}
                      loadingText="Updating Profile..."
                      className="h-12 w-full text-base md:h-10 md:text-sm"
                      disabled={!profileForm.formState.isDirty}
                    >
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Update Profile
                    </LoadingButton>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* Security Settings - Sidebar */}
        <div>
          <Card className="border-muted/40">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Lock className="text-primary h-5 w-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...passwordForm}>
                <form
                  onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)}
                  className="space-y-6 md:space-y-5"
                >
                  <FormField
                    control={passwordForm.control}
                    name="currentPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-sm font-medium">
                          <Key className="text-muted-foreground h-4 w-4" />
                          Current Password
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showCurrentPassword ? 'text' : 'password'}
                              placeholder="Enter current password"
                              className="h-11 pr-12 md:h-10 md:pr-10"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full min-w-[44px] px-3 py-2 hover:bg-transparent md:min-w-0"
                              onClick={() => togglePasswordVisibility('current')}
                              aria-label={showCurrentPassword ? 'Hide password' : 'Show password'}
                            >
                              {showCurrentPassword ? (
                                <EyeOff className="text-muted-foreground h-4 w-4" />
                              ) : (
                                <Eye className="text-muted-foreground h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={passwordForm.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-sm font-medium">
                          <Lock className="text-muted-foreground h-4 w-4" />
                          New Password
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showNewPassword ? 'text' : 'password'}
                              placeholder="Enter new password"
                              className="h-11 pr-12 md:h-10 md:pr-10"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full min-w-[44px] px-3 py-2 hover:bg-transparent md:min-w-0"
                              onClick={() => togglePasswordVisibility('new')}
                              aria-label={showNewPassword ? 'Hide password' : 'Show password'}
                            >
                              {showNewPassword ? (
                                <EyeOff className="text-muted-foreground h-4 w-4" />
                              ) : (
                                <Eye className="text-muted-foreground h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Password must be at least 8 characters with uppercase, lowercase, and
                          numbers.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={passwordForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-sm font-medium">
                          <Lock className="text-muted-foreground h-4 w-4" />
                          Confirm New Password
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showConfirmPassword ? 'text' : 'password'}
                              placeholder="Confirm new password"
                              className="h-11 pr-12 md:h-10 md:pr-10"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full min-w-[44px] px-3 py-2 hover:bg-transparent md:min-w-0"
                              onClick={() => togglePasswordVisibility('confirm')}
                              aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="text-muted-foreground h-4 w-4" />
                              ) : (
                                <Eye className="text-muted-foreground h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="pt-6 md:pt-4">
                    <LoadingButton
                      type="submit"
                      loading={changePasswordMutation.isPending}
                      loadingText="Changing Password..."
                      className="h-12 w-full text-base md:h-10 md:text-sm"
                      disabled={!passwordForm.formState.isDirty}
                    >
                      <Lock className="mr-2 h-4 w-4" />
                      Change Password
                    </LoadingButton>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
