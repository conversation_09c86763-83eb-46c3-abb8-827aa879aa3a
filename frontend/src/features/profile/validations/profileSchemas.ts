import { z } from 'zod';
import { nameSchema, emailSchema, passwordSchema } from '@/shared/validations/baseSchemas';

export const profileUpdateSchema = z.object({
  fullName: nameSchema.min(1, 'Full name is required').optional().or(z.literal('')),
  email: emailSchema.min(1, 'Email address is required').optional().or(z.literal('')),
});

export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required to change your password'),
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "New password and confirmation don't match",
    path: ['confirmPassword'],
  })
  .refine(data => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  });

export const avatarUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine(file => file.size <= 5 * 1024 * 1024, 'File size must be less than 5MB')
    .refine(
      file => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
      'File must be a JPEG, PNG, or WebP image'
    ),
});

export const deleteAccountSchema = z.object({
  password: z.string().min(1, 'Password is required to delete account'),
  confirmation: z.literal('DELETE', {
    errorMap: () => ({ message: 'Please type DELETE to confirm' }),
  }),
});

export const exportDataSchema = z.object({
  includePersonalData: z.boolean().default(true),
  includeSalesData: z.boolean().default(true),
  includeActivityLog: z.boolean().default(false),
  format: z.enum(['json', 'csv']).default('json'),
});

// Type exports
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;
export type PasswordChangeData = z.infer<typeof passwordChangeSchema>;
export type AvatarUploadData = z.infer<typeof avatarUploadSchema>;
export type DeleteAccountData = z.infer<typeof deleteAccountSchema>;
export type ExportDataData = z.infer<typeof exportDataSchema>;
