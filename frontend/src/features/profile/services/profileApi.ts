import { apiClient } from '@/lib/api/client';
import {
  ProfileData,
  ProfileUpdateRequest,
  PasswordChangeRequest,
  ProfileStats,
  ActivityLog,
} from '../types';

export const profileApi = {
  // Get current user profile
  getProfile: async (): Promise<ProfileData> => {
    const response = await apiClient.get('/api/v1/users/profile');
    return response.data;
  },

  // Update profile
  updateProfile: async (data: ProfileUpdateRequest): Promise<ProfileData> => {
    const response = await apiClient.put('/api/v1/users/profile', data);
    return response.data;
  },

  // Change password
  changePassword: async (data: PasswordChangeRequest): Promise<void> => {
    await apiClient.post('/api/v1/users/change-password', data);
  },

  // Get profile statistics
  getProfileStats: async (): Promise<ProfileStats> => {
    const response = await apiClient.get('/api/v1/users/profile/stats');
    return response.data;
  },

  // Get activity log
  getActivityLog: async (limit: number = 50): Promise<ActivityLog[]> => {
    const response = await apiClient.get('/api/v1/users/profile/activity', {
      params: { limit },
    });
    return response.data;
  },

  // Upload profile avatar
  uploadAvatar: async (file: File): Promise<{ avatarUrl: string }> => {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await apiClient.post('/api/v1/users/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete profile avatar
  deleteAvatar: async (): Promise<void> => {
    await apiClient.delete('/api/v1/users/profile/avatar');
  },

  // Export profile data
  exportProfileData: async (): Promise<Blob> => {
    const response = await apiClient.get('/api/v1/users/profile/export', {
      responseType: 'blob',
    });
    return response.data;
  },

  // Delete account
  deleteAccount: async (password: string): Promise<void> => {
    await apiClient.delete('/api/v1/users/profile', {
      data: { password },
    });
  },
};
