import { useQuery } from '@tanstack/react-query';
import { userApi } from '../services/userApi';
import { UserRole, Department, Restaurant } from '../types';

export function useUserMetadata() {
  const {
    data: metadata,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['user-metadata'],
    queryFn: userApi.getMetadata,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const roles: UserRole[] = Array.isArray(metadata?.data?.roles) ? metadata.data.roles : [];
  const departments: Department[] = Array.isArray(metadata?.data?.departments)
    ? metadata.data.departments
    : [];
  const restaurants: Restaurant[] = Array.isArray(metadata?.data?.restaurants)
    ? metadata.data.restaurants
    : [];

  return { roles, departments, restaurants, isLoading, error };
}

export function useUserStats(userId?: number) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['user-stats', userId],
    queryFn: () => userApi.getStats(userId),
    staleTime: 1000 * 60 * 1, // 1 minute
  });

  return { stats: data, isLoading, error };
}
