import { useState, useCallback, useEffect } from 'react';
import { z } from 'zod';
import { useEmailValidation } from './useUserQueries';

interface ValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  isValidating: boolean;
}

interface UseFormValidationReturn<T> extends ValidationResult {
  validateField: (field: keyof T, value: any) => Promise<void>;
  validateForm: (data: T) => Promise<boolean>;
  clearErrors: () => void;
  clearFieldError: (field: keyof T) => void;
  setFieldError: (field: keyof T, error: string) => void;
}

export function useFormValidation<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  options: ValidationOptions = {}
): UseFormValidationReturn<T> {
  const {
    validateOnChange: _validateOnChange = false,
    validateOnBlur: _validateOnBlur = true,
    debounceMs: _debounceMs = 300,
  } = options;

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const { checkEmailExists } = useEmailValidation();

  const validateField = useCallback(
    async (field: keyof T, value: any) => {
      setIsValidating(true);

      try {
        // Create a partial object with just this field for validation
        const fieldData = { [field]: value } as Partial<T>;

        // Try to validate just this field using the schema
        // Note: Using full schema validation since pick may not be available on all schema types
        const result = schema.safeParse(fieldData);

        if (result.success) {
          // Additional validation for email field
          if (field === 'email' && value) {
            try {
              const emailExists = await checkEmailExists(value);
              if (emailExists) {
                setErrors(prev => ({ ...prev, [field]: 'Email already exists' }));
                return;
              }
            } catch (error) {
              console.error('Email validation error:', error);
            }
          }

          // Clear error if validation passes
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[field as string];
            return newErrors;
          });
        } else {
          // Set error if validation fails
          const fieldError = result.error.errors.find((err: any) => err.path[0] === field);
          if (fieldError) {
            setErrors(prev => ({ ...prev, [field]: fieldError.message }));
          }
        }
      } catch (error) {
        console.error('Field validation error:', error);
      } finally {
        setIsValidating(false);
      }
    },
    [schema, checkEmailExists]
  );

  const validateForm = useCallback(
    async (data: T): Promise<boolean> => {
      setIsValidating(true);
      const newErrors: Record<string, string> = {};

      try {
        // Validate with schema
        const result = schema.safeParse(data);

        if (!result.success) {
          result.error.errors.forEach(error => {
            const field = error.path[0] as string;
            newErrors[field] = error.message;
          });
        }

        // Additional email existence check
        if (!newErrors.email && data.email) {
          try {
            const emailExists = await checkEmailExists(data.email);
            if (emailExists) {
              newErrors.email = 'Email already exists';
            }
          } catch (error) {
            console.error('Email validation error:', error);
          }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
      } catch (error) {
        console.error('Form validation error:', error);
        setErrors({ general: 'Validation failed. Please check your input.' });
        return false;
      } finally {
        setIsValidating(false);
      }
    },
    [schema, checkEmailExists]
  );

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearFieldError = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  const setFieldError = useCallback((field: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const isValid = Object.keys(errors).length === 0;

  return {
    isValid,
    errors,
    isValidating,
    validateField,
    validateForm,
    clearErrors,
    clearFieldError,
    setFieldError,
  };
}

// Debounced validation hook for real-time validation
export function useDebouncedValidation<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  data: T,
  options: ValidationOptions = {}
): ValidationResult {
  const { debounceMs = 300 } = options;
  const [debouncedData, setDebouncedData] = useState(data);
  const validation = useFormValidation(schema, options);

  // Debounce data changes with proper cleanup
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedData(data);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [data, debounceMs]);

  // Validate when debounced data changes with proper dependency management
  useEffect(() => {
    if (options.validateOnChange) {
      validation.validateForm(debouncedData);
    }
  }, [debouncedData, options.validateOnChange]); // Removed validation from deps to prevent infinite loops

  return {
    isValid: validation.isValid,
    errors: validation.errors,
    isValidating: validation.isValidating,
  };
}

export default useFormValidation;
