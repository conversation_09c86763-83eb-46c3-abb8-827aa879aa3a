import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  CreateUserRequest,
  UpdateUserRequest,
  UserFilters,
  BulkUserOperation,
  PasswordResetRequest,
} from '../types';
import { userApi } from '../services/userApi';
import { useApiErrorHandler } from './useErrorHandler';
import { useNetworkStatus } from './useNetworkStatus';

// Query Keys
export const USER_QUERY_KEYS = {
  all: ['users'] as const,
  lists: () => [...USER_QUERY_KEYS.all, 'list'] as const,
  list: (filters: UserFilters) => [...USER_QUERY_KEYS.lists(), filters] as const,
  details: () => [...USER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...USER_QUERY_KEYS.details(), id] as const,
  stats: () => [...USER_QUERY_KEYS.all, 'stats'] as const,
  auditLogs: () => [...USER_QUERY_KEYS.all, 'audit'] as const,
  metadata: () => [...USER_QUERY_KEYS.all, 'metadata'] as const,
  roles: () => [...USER_QUERY_KEYS.metadata(), 'roles'] as const,
  departments: () => [...USER_QUERY_KEYS.metadata(), 'departments'] as const,
  restaurants: () => [...USER_QUERY_KEYS.metadata(), 'restaurants'] as const,
} as const;

// Users List Query
export function useUsers(filters: UserFilters = {}) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.list(filters),
    queryFn: () => userApi.getUsers(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Single User Query
export function useUser(userId: number) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.detail(userId.toString()),
    queryFn: () => userApi.getUser(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// User Statistics Query
export function useUserStats() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.stats(),
    queryFn: () => userApi.getStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// User Audit Logs Query
export function useUserAuditLogs(userId?: number, limit: number = 50) {
  return useQuery({
    queryKey: [...USER_QUERY_KEYS.auditLogs(), { userId, limit }],
    queryFn: () => userApi.getUserAuditLogs({ userId, limit }),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// User Roles Query
export function useUserRoles() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.roles(),
    queryFn: () => userApi.getUserRoles(),
    staleTime: 10 * 60 * 1000, // 10 minutes - roles don't change often
  });
}

// Departments Query
export function useDepartments() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.departments(),
    queryFn: () => userApi.getDepartments(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Restaurants Query
export function useRestaurants() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.restaurants(),
    queryFn: () => userApi.getRestaurants(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Create User Mutation
export function useCreateUser(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();
  const { handleApiError } = useApiErrorHandler();
  const { retryWhenOnline } = useNetworkStatus();

  return useMutation({
    mutationFn: async (userData: CreateUserRequest) => {
      return await retryWhenOnline(() => userApi.createUser(userData));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.stats() });
      toast.success('User created successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      handleApiError(error, 'create_user');
      options.onError?.(error);
    },
    retry: (failureCount, error: any) => {
      if (error?.code === 'NETWORK_ERROR' && failureCount < 3) return true;
      if (error?.response?.status >= 500 && failureCount < 2) return true;
      return false;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Update User Mutation
export function useUpdateUser(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, userData }: { userId: number; userData: UpdateUserRequest }) =>
      userApi.updateUser(userId, userData),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(userId.toString()) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.stats() });
      toast.success('User updated successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to update user';
      toast.error(errorMessage);
      options.onError?.(error);
    },
  });
}

// Delete User Mutation
export function useDeleteUser(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: number) => userApi.deleteUser(userId),
    onSuccess: (_, userId) => {
      queryClient.removeQueries({ queryKey: USER_QUERY_KEYS.detail(userId.toString()) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.stats() });
      toast.success('User deleted successfully');
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete user';
      toast.error(errorMessage);
      options.onError?.(error);
    },
  });
}

// Toggle User Status Mutation
export function useToggleUserStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, isActive }: { userId: number; isActive: boolean }) =>
      userApi.toggleUserStatus(userId, isActive),
    onSuccess: (_, { userId, isActive }) => {
      // Update user in cache and invalidate lists
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(userId.toString()) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.stats() });
      toast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || error.message || 'Failed to update user status';
      toast.error(errorMessage);
    },
  });
}

// Reset Password Mutation
export function useResetPassword() {
  return useMutation({
    mutationFn: ({
      userId,
      options,
    }: {
      userId: number;
      options?: Omit<PasswordResetRequest, 'userId'>;
    }) => userApi.resetPassword(userId, options),
    onSuccess: result => {
      toast.success(result.message);
      if (result.temporaryPassword) {
        toast.info(`Temporary password: ${result.temporaryPassword}`, {
          duration: 10000,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || error.message || 'Failed to reset password';
      toast.error(errorMessage);
    },
  });
}

// Bulk Operation Mutation
export function useBulkUserOperation(
  options: {
    onSuccess?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (operation: BulkUserOperation) => userApi.bulkOperation(operation),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.stats() });
      toast.success(`${result.message} (${result.affectedUsers} users affected)`);
      options.onSuccess?.();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to perform bulk operation';
      toast.error(errorMessage);
      options.onError?.(error);
    },
  });
}

// Email Validation Query
export function useEmailValidation() {
  const checkEmailExists = async (email: string): Promise<boolean> => {
    if (!email) return false;
    try {
      const result = await userApi.checkEmailExists(email);
      return result.exists;
    } catch (error) {
      console.error('Error checking email:', error);
      return false;
    }
  };

  return { checkEmailExists };
}
