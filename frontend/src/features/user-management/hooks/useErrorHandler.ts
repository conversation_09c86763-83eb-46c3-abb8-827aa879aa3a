import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface ErrorInfo {
  message: string;
  code?: string;
  statusCode?: number;
  details?: any;
  timestamp: Date;
  context?: string;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  retryable?: boolean;
  context?: string;
}

export interface UseErrorHandlerReturn {
  error: ErrorInfo | null;
  isRetrying: boolean;
  clearError: () => void;
  handleError: (error: any, options?: ErrorHandlerOptions) => void;
  retry: (fn: () => Promise<any>) => Promise<void>;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [error, setError] = useState<ErrorInfo | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const extractErrorMessage = useCallback((error: any): string => {
    // Handle different error types
    if (typeof error === 'string') {
      return error;
    }

    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.name === 'NetworkError' || error?.code === 'NETWORK_ERROR') {
      return 'Network connection failed. Please check your internet connection and try again.';
    }

    if (error?.name === 'TimeoutError' || error?.code === 'TIMEOUT') {
      return 'Request timed out. Please try again.';
    }

    if (error?.response?.status === 401) {
      return 'Your session has expired. Please log in again.';
    }

    if (error?.response?.status === 403) {
      return 'You do not have permission to perform this action.';
    }

    if (error?.response?.status === 404) {
      return 'The requested resource was not found.';
    }

    if (error?.response?.status === 409) {
      return 'A conflict occurred. The resource may have been modified by another user.';
    }

    if (error?.response?.status === 422) {
      return 'The data provided is invalid. Please check your input and try again.';
    }

    if (error?.response?.status >= 500) {
      return 'A server error occurred. Please try again later or contact support if the problem persists.';
    }

    return 'An unexpected error occurred. Please try again.';
  }, []);

  const getErrorCode = useCallback((error: any): string | undefined => {
    return error?.response?.data?.code || error?.code || error?.name;
  }, []);

  const getStatusCode = useCallback((error: any): number | undefined => {
    return error?.response?.status;
  }, []);

  const isRetryableError = useCallback(
    (error: any): boolean => {
      const statusCode = getStatusCode(error);
      const errorCode = getErrorCode(error);

      // Network errors are retryable
      if (errorCode === 'NETWORK_ERROR' || errorCode === 'TIMEOUT') {
        return true;
      }

      // Server errors (5xx) are retryable
      if (statusCode && statusCode >= 500) {
        return true;
      }

      // Rate limiting (429) is retryable
      if (statusCode === 429) {
        return true;
      }

      return false;
    },
    [getStatusCode, getErrorCode]
  );

  const logError = useCallback((errorInfo: ErrorInfo) => {
    console.error('Error occurred:', {
      message: errorInfo.message,
      code: errorInfo.code,
      statusCode: errorInfo.statusCode,
      context: errorInfo.context,
      timestamp: errorInfo.timestamp,
      details: errorInfo.details,
    });

    // In a real application, you might want to send this to an error tracking service
    // like Sentry, LogRocket, or Bugsnag
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exception', {
        description: errorInfo.message,
        fatal: false,
      });
    }
  }, []);

  const handleError = useCallback(
    (error: any, options: ErrorHandlerOptions = {}) => {
      const { showToast = true, logError: shouldLog = true, context } = options;

      const errorInfo: ErrorInfo = {
        message: extractErrorMessage(error),
        code: getErrorCode(error),
        statusCode: getStatusCode(error),
        details: error,
        timestamp: new Date(),
        context,
      };

      setError(errorInfo);

      if (shouldLog) {
        logError(errorInfo);
      }

      if (showToast) {
        toast.error(errorInfo.message, {
          duration: 5000,
          action: isRetryableError(error)
            ? {
                label: 'Retry',
                onClick: () => {
                  // The retry function will be called by the component
                },
              }
            : undefined,
        });
      }
    },
    [extractErrorMessage, getErrorCode, getStatusCode, logError, isRetryableError]
  );

  const retry = useCallback(
    async (fn: () => Promise<any>) => {
      setIsRetrying(true);
      clearError();

      try {
        await fn();
        toast.success('Operation completed successfully');
      } catch (retryError) {
        handleError(retryError, {
          context: 'retry_attempt',
          showToast: true,
        });
      } finally {
        setIsRetrying(false);
      }
    },
    [clearError, handleError]
  );

  return {
    error,
    isRetrying,
    clearError,
    handleError,
    retry,
  };
}

// Hook for handling specific API errors with custom recovery strategies
export function useApiErrorHandler() {
  const errorHandler = useErrorHandler();

  const handleApiError = useCallback(
    (error: any, operation: string) => {
      const statusCode = error?.response?.status;

      // Handle specific API error cases
      switch (statusCode) {
        case 401:
          // Redirect to login
          errorHandler.handleError(error, {
            context: `api_${operation}`,
            showToast: true,
          });
          // In a real app, you might want to redirect to login
          // window.location.href = '/login';
          break;

        case 403:
          errorHandler.handleError(error, {
            context: `api_${operation}`,
            showToast: true,
          });
          break;

        case 409:
          // Conflict - suggest refresh
          errorHandler.handleError(error, {
            context: `api_${operation}`,
            showToast: true,
          });
          toast.info(
            'The data may have been updated by another user. Please refresh and try again.',
            {
              action: {
                label: 'Refresh',
                onClick: () => window.location.reload(),
              },
            }
          );
          break;

        case 422:
          // Validation error - don't show toast, let form handle it
          errorHandler.handleError(error, {
            context: `api_${operation}`,
            showToast: false,
          });
          break;

        default:
          errorHandler.handleError(error, {
            context: `api_${operation}`,
            showToast: true,
          });
      }
    },
    [errorHandler]
  );

  return {
    ...errorHandler,
    handleApiError,
  };
}

export default useErrorHandler;
