import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
}

export interface UseNetworkStatusReturn extends NetworkStatus {
  retryWhenOnline: (fn: () => Promise<any>) => Promise<void>;
  showOfflineMessage: () => void;
  hideOfflineMessage: () => void;
}

export function useNetworkStatus(): UseNetworkStatusReturn {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
  });

  const [offlineToastId, setOfflineToastId] = useState<string | number | null>(null);
  const [pendingRetries, setPendingRetries] = useState<(() => Promise<any>)[]>([]);

  const updateNetworkStatus = useCallback(() => {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    const isOnline = navigator.onLine;
    let isSlowConnection = false;
    let connectionType = 'unknown';
    let effectiveType = 'unknown';
    let downlink = 0;
    let rtt = 0;

    if (connection) {
      connectionType = connection.type || 'unknown';
      effectiveType = connection.effectiveType || 'unknown';
      downlink = connection.downlink || 0;
      rtt = connection.rtt || 0;

      // Consider connection slow if:
      // - Effective type is 'slow-2g' or '2g'
      // - RTT is greater than 1000ms
      // - Downlink is less than 0.5 Mbps
      isSlowConnection =
        effectiveType === 'slow-2g' || effectiveType === '2g' || rtt > 1000 || downlink < 0.5;
    }

    setNetworkStatus({
      isOnline,
      isSlowConnection,
      connectionType,
      effectiveType,
      downlink,
      rtt,
    });

    return { isOnline, isSlowConnection };
  }, []);

  const showOfflineMessage = useCallback(() => {
    if (offlineToastId) return; // Already showing

    const toastId = toast.error('You are currently offline', {
      duration: Infinity,
      description: "Some features may not be available. We'll retry when you're back online.",
      action: {
        label: 'Retry',
        onClick: () => {
          if (navigator.onLine) {
            hideOfflineMessage();
            // Retry pending operations
            executePendingRetries();
          }
        },
      },
    });

    setOfflineToastId(toastId);
  }, [offlineToastId]);

  const hideOfflineMessage = useCallback(() => {
    if (offlineToastId) {
      toast.dismiss(offlineToastId);
      setOfflineToastId(null);
    }
  }, [offlineToastId]);

  const executePendingRetries = useCallback(async () => {
    if (pendingRetries.length === 0) return;

    const retries = [...pendingRetries];
    setPendingRetries([]);

    for (const retryFn of retries) {
      try {
        await retryFn();
      } catch (error) {
        console.error('Retry failed:', error);
      }
    }

    if (retries.length > 0) {
      toast.success(`Retried ${retries.length} operation${retries.length > 1 ? 's' : ''}`);
    }
  }, [pendingRetries]);

  const retryWhenOnline = useCallback(
    async (fn: () => Promise<any>) => {
      if (navigator.onLine) {
        // If online, execute immediately
        return await fn();
      } else {
        // If offline, queue for retry when online
        setPendingRetries(prev => [...prev, fn]);
        showOfflineMessage();
        throw new Error(
          'Currently offline. Operation will be retried when connection is restored.'
        );
      }
    },
    [showOfflineMessage]
  );

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      const { isOnline } = updateNetworkStatus();

      if (isOnline) {
        hideOfflineMessage();
        toast.success('Connection restored', {
          description: 'You are back online',
        });

        // Execute pending retries
        executePendingRetries();
      }
    };

    const handleOffline = () => {
      updateNetworkStatus();
      showOfflineMessage();
    };

    const handleConnectionChange = () => {
      const { isOnline, isSlowConnection } = updateNetworkStatus();

      if (isOnline && isSlowConnection) {
        toast.warning('Slow connection detected', {
          description: 'Some operations may take longer than usual',
          duration: 3000,
        });
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for connection changes if supported
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', handleConnectionChange);
    }

    // Initial status check
    updateNetworkStatus();

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);

      if (connection) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, [updateNetworkStatus, hideOfflineMessage, showOfflineMessage, executePendingRetries]);

  // Periodic connectivity check
  useEffect(() => {
    const checkConnectivity = async () => {
      try {
        // Try to fetch a small resource to verify actual connectivity
        // Fixed: Corrected health check URL to match backend API routing
        const response = await fetch('/api/v1/health', {
          method: 'HEAD',
          cache: 'no-cache',
        });

        if (!response.ok) {
          throw new Error('Health check failed');
        }
      } catch (error) {
        // If the health check fails but navigator.onLine is true,
        // we might have a connection but no internet access
        if (navigator.onLine) {
          console.warn('Health check failed:', error);
          toast.warning('Limited connectivity', {
            description: 'You may have connection issues. Some features may not work properly.',
            duration: 5000,
          });
        }
      }
    };

    // Check connectivity every 30 seconds when online
    const interval = setInterval(() => {
      if (navigator.onLine) {
        checkConnectivity();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return {
    ...networkStatus,
    retryWhenOnline,
    showOfflineMessage,
    hideOfflineMessage,
  };
}

export default useNetworkStatus;
