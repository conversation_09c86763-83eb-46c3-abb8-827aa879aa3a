// User Management Types - Aligned with Backend Model
export interface User {
  userId: number;
  username: string;
  email: string;
  role: 'admin' | 'staff' | 'user';
  fullName: string;
  restaurantId?: number | null;
  isActive: boolean;
  requiresOnboarding?: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  fullName: string;
  role: 'admin' | 'staff' | 'user';
  restaurantId?: number;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  fullName?: string;
  role?: 'admin' | 'staff' | 'user';
  restaurantId?: number;
  isActive?: boolean;
}

export interface UserFilters {
  search?: string;
  role?: 'admin' | 'staff' | 'user' | 'all';
  restaurantId?: number;
  page?: number;
  limit?: number;
}

export interface UserSortConfig {
  field: keyof User;
  direction: 'asc' | 'desc';
}

export interface PaginationConfig {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface UsersResponse {
  users: User[];
  pagination: PaginationConfig;
  filters: UserFilters;
}

export interface UserFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  restaurantId?: number;
  isActive: boolean;
}

export interface BulkUserOperation {
  action: 'activate' | 'deactivate' | 'delete' | 'updateRole';
  userIds: number[];
  data?: {
    role?: 'admin' | 'staff' | 'user';
    isActive?: boolean;
  };
}

export interface UserAuditLog {
  id: string;
  userId: number;
  action: string;
  performedBy: number;
  performedByName: string;
  timestamp: string;
  details: Record<string, any>;
  ipAddress?: string;
}

export interface PasswordResetRequest {
  userId: number;
  temporaryPassword?: boolean;
  sendEmail?: boolean;
}

export interface UserRole {
  value: 'admin' | 'staff' | 'user';
  label: string;
  permissions: string[];
}

export interface Restaurant {
  id?: number;
  name: string;
  location?: string;
}

export interface Department {
  id?: number;
  name: string;
  description?: string;
}

// Component Props Types
export interface UserTableProps {
  users: User[];
  loading?: boolean;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onToggleStatus: (user: User) => void;
  onResetPassword: (user: User) => void;
  onBulkOperation: (operation: BulkUserOperation) => void;
  selectedUsers: number[];
  onSelectUser: (userId: number) => void;
  onSelectAll: (selected: boolean) => void;
  // Enhanced props for TanStack Table
  pagination?: {
    pageIndex: number;
    pageSize: number;
    total: number;
  };
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void;
  sorting?: Array<{ id: string; desc: boolean }>;
  onSortingChange?: (sorting: Array<{ id: string; desc: boolean }>) => void;
  globalFilter?: string;
  onGlobalFilterChange?: (filter: string) => void;
}

export interface UserFormProps {
  user?: User;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: UserFormData) => Promise<void>;
  loading: boolean;
  restaurants: Restaurant[];
}

export interface UserStatsProps {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  newUsersThisMonth: number;
  loading: boolean;
}

// Hook Return Types
export interface UseUsersReturn {
  users: User[];
  loading: boolean;
  error: string | null;
  pagination: PaginationConfig;
  filters: UserFilters;
  sortConfig: UserSortConfig;
  selectedUsers: number[];
  fetchUsers: () => Promise<void>;
  createUser: (data: CreateUserRequest) => Promise<void>;
  updateUser: (userId: number, data: UpdateUserRequest) => Promise<void>;
  deleteUser: (userId: number) => Promise<void>;
  toggleUserStatus: (userId: number, isActive: boolean) => Promise<void>;
  resetPassword: (userId: number, options?: PasswordResetRequest) => Promise<void>;
  bulkOperation: (operation: BulkUserOperation) => Promise<void>;
  setFilters: (filters: UserFilters) => void;
  setSort: (field: keyof User) => void;
  setPagination: (page: number, limit?: number) => void;
  selectUser: (userId: number) => void;
  selectAllUsers: (selected: boolean) => void;
  clearSelection: () => void;
}

export interface UseUserStatsReturn {
  stats: {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    newUsersThisMonth: number;
    usersByRole: Record<string, number>;
  };
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseUserAuditReturn {
  auditLogs: UserAuditLog[];
  loading: boolean;
  error: string | null;
  fetchAuditLogs: (userId?: number, limit?: number) => Promise<void>;
}
