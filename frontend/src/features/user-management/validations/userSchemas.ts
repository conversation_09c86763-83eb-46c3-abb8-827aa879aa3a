import { z } from 'zod';
import {
  emailSchema,
  passwordSchema,
  nameSchema,
  paginationSchema,
} from '@/shared/validations/baseSchemas';

// Base username validation
const usernameSchema = z
  .string()
  .min(1, 'Username is required')
  .min(3, 'Username must be at least 3 characters')
  .max(50, 'Username must be less than 50 characters')
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    'Username can only contain letters, numbers, underscores, and hyphens'
  );

// Role validation
const roleSchema = z.enum(['admin', 'staff', 'user'], {
  errorMap: () => ({ message: 'Role must be admin, staff, or user' }),
});

// Restaurant ID validation
const restaurantIdSchema = z
  .number()
  .positive('Restaurant ID must be a positive number')
  .optional();

// User form validation schema
export const userFormSchema = z
  .object({
    username: usernameSchema,
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    fullName: nameSchema,
    restaurantId: restaurantIdSchema,
    isActive: z.boolean().default(true),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// User edit form validation schema (no password fields, no role field)
export const userEditFormSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  fullName: nameSchema,
  restaurantId: restaurantIdSchema,
  isActive: z.boolean().default(true),
});

// Create user request validation
export const createUserRequestSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  fullName: nameSchema,
  role: roleSchema,
  restaurantId: restaurantIdSchema,
});

// Update user request validation
export const updateUserRequestSchema = z
  .object({
    username: usernameSchema.optional(),
    email: emailSchema.optional(),
    password: passwordSchema.optional(),
    fullName: nameSchema.optional(),
    role: roleSchema.optional(),
    restaurantId: restaurantIdSchema,
    isActive: z.boolean().optional(),
  })
  .partial();

// User filters validation
export const userFiltersSchema = z.object({
  search: z.string().max(255).optional(),
  role: roleSchema.or(z.literal('all')).optional(),
  status: z.enum(['active', 'inactive', 'all']).optional(),
  restaurantId: z.number().positive().optional(),
});

// User query parameters validation
export const userQuerySchema = z.object({
  ...paginationSchema.shape,
  ...userFiltersSchema.shape,
  sortBy: z.enum(['username', 'email', 'fullName', 'role', 'createdAt']).default('createdAt'),
});

// Bulk user operation validation
export const bulkUserOperationSchema = z.object({
  action: z.enum(['activate', 'deactivate', 'delete', 'updateRole']),
  userIds: z.array(z.number().positive()).min(1, 'At least one user must be selected'),
  data: z
    .object({
      role: roleSchema.optional(),
      isActive: z.boolean().optional(),
    })
    .optional(),
});

// Password reset validation
export const passwordResetRequestSchema = z.object({
  userId: z.number().positive(),
  temporaryPassword: z.boolean().default(false),
  sendEmail: z.boolean().default(true),
});

// Password reset form validation schema
export const passwordResetFormSchema = z
  .object({
    email: emailSchema,
    resetMethod: z.enum(['auto', 'manual'], {
      errorMap: () => ({ message: 'Reset method must be auto or manual' }),
    }),
    newPassword: z.string().optional(),
    confirmPassword: z.string().optional(),
    temporaryPassword: z.boolean().default(true),
    sendEmail: z.boolean().default(true),
  })
  .refine(
    data => {
      // Only validate passwords if manual reset method is selected
      if (data.resetMethod === 'manual') {
        if (!data.newPassword) {
          return false;
        }
        if (data.newPassword.length < 8) {
          return false;
        }
        if (!data.confirmPassword) {
          return false;
        }
        if (data.newPassword !== data.confirmPassword) {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Password validation failed',
      path: ['newPassword'],
    }
  )
  .refine(
    data => {
      // Separate refinement for password confirmation
      if (data.resetMethod === 'manual' && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    }
  );

// User import validation
export const userImportSchema = z.object({
  users: z.array(createUserRequestSchema).min(1, 'At least one user is required'),
});

// User export validation
export const userExportSchema = z.object({
  format: z.enum(['csv', 'xlsx']).default('csv'),
  filters: userFiltersSchema.optional(),
  includeInactive: z.boolean().default(false),
});

// Email validation for checking existence
export const emailCheckSchema = z.object({
  email: emailSchema,
  excludeUserId: z.number().positive().optional(),
});

// Type exports
export type UserFormData = z.infer<typeof userFormSchema>;
export type UserEditFormData = z.infer<typeof userEditFormSchema>;
export type CreateUserRequestData = z.infer<typeof createUserRequestSchema>;
export type UpdateUserRequestData = z.infer<typeof updateUserRequestSchema>;
export type UserFiltersData = z.infer<typeof userFiltersSchema>;
export type UserQueryData = z.infer<typeof userQuerySchema>;
export type BulkUserOperationData = z.infer<typeof bulkUserOperationSchema>;
export type PasswordResetRequestData = z.infer<typeof passwordResetRequestSchema>;
export type PasswordResetFormData = z.infer<typeof passwordResetFormSchema>;
export type UserImportData = z.infer<typeof userImportSchema>;
export type UserExportData = z.infer<typeof userExportSchema>;
export type EmailCheckData = z.infer<typeof emailCheckSchema>;
