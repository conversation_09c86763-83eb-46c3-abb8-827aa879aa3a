import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserFilters,
  BulkUserOperation,
  UserAuditLog,
  UserRole,
  Department,
  Restaurant,
} from '../types';

export interface UsersListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const userApi = {
  // Get all users with filters
  getUsers: async (
    filters?: UserFilters & { page?: number; limit?: number }
  ): Promise<UsersListResponse> => {
    // Clone the filters to avoid modifying the original object
    const cleanedFilters = { ...filters };

    // The backend expects the role to be omitted if 'all' is selected, not the string 'all'.
    if (cleanedFilters.role === 'all') {
      delete cleanedFilters.role;
    }

    // The backend does not support a 'status' filter in the query schema, so it should be removed.
    if ('status' in cleanedFilters) {
      delete (cleanedFilters as any).status;
    }

    const response = await apiClient.get(API_ENDPOINTS.USERS.LIST, {
      params: cleanedFilters,
    });

    // The backend returns data nested under a `data` property, and pagination separately.
    // We need to map this to the expected UsersListResponse structure.
    return {
      users: response.data.data,
      ...response.data.pagination,
    };
  },

  // Get single user
  getUser: async (id: number): Promise<User> => {
    const response = await apiClient.get(API_ENDPOINTS.USERS.GET(id.toString()));
    return response.data.data.user;
  },

  // Create new user
  createUser: async (data: CreateUserRequest): Promise<User> => {
    const response = await apiClient.post(API_ENDPOINTS.USERS.CREATE, data);
    return response.data.data.user;
  },

  // Update user
  updateUser: async (id: number, data: UpdateUserRequest): Promise<User> => {
    const response = await apiClient.put(API_ENDPOINTS.USERS.UPDATE(id.toString()), data);
    return response.data.data.user;
  },

  // Delete user (soft delete)
  deleteUser: async (id: number): Promise<void> => {
    await apiClient.delete(API_ENDPOINTS.USERS.DELETE(id.toString()));
  },

  // Bulk operations
  bulkDeleteUsers: async (ids: number[]): Promise<void> => {
    await apiClient.delete(`${API_ENDPOINTS.USERS.LIST}/bulk`, {
      data: { ids },
    });
  },

  bulkUpdateUsers: async (
    updates: Array<{ id: number; data: UpdateUserRequest }>
  ): Promise<User[]> => {
    const response = await apiClient.put(`${API_ENDPOINTS.USERS.LIST}/bulk`, { updates });
    return response.data;
  },

  // Password reset for admin
  resetUserPassword: async (id: number): Promise<{ temporaryPassword: string }> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.USERS.UPDATE(id.toString())}/reset-password`
    );
    return response.data;
  },

  // Export users
  exportUsers: async (filters?: UserFilters): Promise<Blob> => {
    const response = await apiClient.get(`${API_ENDPOINTS.USERS.LIST}/export`, {
      params: filters,
      responseType: 'blob',
    });
    return response.data;
  },

  // Import users
  importUsers: async (file: File): Promise<{ success: number; errors: any[] }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${API_ENDPOINTS.USERS.CREATE}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get user statistics
  getStats: async (userId?: number): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.USERS.LIST}/stats`, {
      params: userId ? { userId } : {},
    });
    return response.data.data; // Extract the data property from the wrapped response
  },

  getMetadata: async (): Promise<{
    success: boolean;
    message: string;
    data: {
      roles: UserRole[];
      departments: Department[];
      restaurants: Restaurant[];
    };
  }> => {
    const response = await apiClient.get(`${API_ENDPOINTS.USERS.LIST}/metadata`);
    return response.data;
  },

  // Get user audit logs
  getUserAuditLogs: async (params: {
    userId?: number;
    limit?: number;
  }): Promise<UserAuditLog[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.USERS.LIST}/audit`, {
      params,
    });
    return response.data;
  },

  // Get user roles
  getUserRoles: async (): Promise<UserRole[]> => {
    const response = await apiClient.get('/api/v1/roles');
    return response.data;
  },

  // Get departments
  getDepartments: async (): Promise<Department[]> => {
    const response = await apiClient.get('/api/v1/departments');
    return response.data;
  },

  // Get restaurants
  getRestaurants: async (): Promise<Restaurant[]> => {
    const response = await apiClient.get(API_ENDPOINTS.RESTAURANTS.LIST);
    return response.data;
  },

  // Toggle user status (activate/deactivate)
  toggleUserStatus: async (userId: number, isActive: boolean): Promise<User> => {
    const response = await apiClient.put(API_ENDPOINTS.USERS.UPDATE(userId.toString()), {
      isActive,
    });
    return response.data.user;
  },

  // Bulk operations
  bulkOperation: async (
    operation: BulkUserOperation
  ): Promise<{ message: string; affectedUsers: number }> => {
    const response = await apiClient.post(`${API_ENDPOINTS.USERS.LIST}/bulk-operation`, operation);
    return response.data;
  },

  // Reset password
  resetPassword: async (
    userId: number,
    options?: any
  ): Promise<{ message: string; temporaryPassword?: string }> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.USERS.UPDATE(userId.toString())}/reset-password`,
      options
    );
    return response.data;
  },

  // Check if email exists
  checkEmailExists: async (email: string): Promise<{ exists: boolean }> => {
    const response = await apiClient.get(`${API_ENDPOINTS.USERS.LIST}/check-email`, {
      params: { email },
    });
    return response.data;
  },
};
