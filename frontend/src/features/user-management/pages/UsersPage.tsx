import React, { useState, useCallback, useMemo } from 'react';
import { Plus, Upload, UserCheck } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import {
  UserTable,
  UserForm,
  UserStats,
  UserDeleteDialog,
  UserBulkActions,
  UserPasswordReset,
  UserImportExport,
} from '../components';
import ErrorRecovery, { NetworkStatusIndicator } from '../components/ErrorRecovery';
import {
  useUsers,
  useUserStats,
  useUserMetadata,
  useCreateUser,
  useUpdateUser,
  useDeleteUser,
  useToggleUserStatus,
  useResetPassword,
  useBulkUserOperation,
} from '../hooks';
import {
  User,
  UserFilters as UserFiltersType,
  BulkUserOperation,
  CreateUserRequest,
  UpdateUserRequest,
} from '../types';
// Utils imports removed as export functionality is handled by UserImportExport component
import { userManagementDebugger, logApiCall } from '../utils/debug';

export function UsersPage() {
  // Initialize debugging
  React.useEffect(() => {
    userManagementDebugger.info('UsersPage', 'component_mount', 'UsersPage component mounted');
    return () => {
      userManagementDebugger.info(
        'UsersPage',
        'component_unmount',
        'UsersPage component unmounted'
      );
    };
  }, []);

  // State management for dialogs
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showBulkActionsDialog, setShowBulkActionsDialog] = useState(false);
  const [showPasswordResetDialog, setShowPasswordResetDialog] = useState(false);
  const [showImportExportDialog, setShowImportExportDialog] = useState(false);

  // State for specific actions
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [passwordResetUser, setPasswordResetUser] = useState<User | null>(null);

  // UI state
  const [alert, setAlert] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Filters state
  const [filters, setFilters] = useState<UserFiltersType & { page?: number; limit?: number }>({
    search: '',
    page: 1,
    limit: 10,
  });

  // TanStack Query hooks
  const {
    data: usersData,
    isLoading: usersLoading,
    error: usersError,
    refetch: refetchUsers,
  } = useUsers(filters);
  const { data: stats, isLoading: statsLoading } = useUserStats();
  const { restaurants } = useUserMetadata();

  // Mutations
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const deleteUserMutation = useDeleteUser();
  const toggleStatusMutation = useToggleUserStatus();
  const resetPasswordMutation = useResetPassword();
  const bulkOperationMutation = useBulkUserOperation();

  // Local state for user selection
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);

  // Derived state for users and pagination
  const users = usersData?.users || [];
  const pagination = useMemo(
    () =>
      usersData
        ? {
            pageIndex: (usersData.page || 1) - 1, // Convert 1-based to 0-based for TanStack Table
            pageSize: usersData.limit || 10,
            total: usersData.total || 0,
          }
        : undefined,
    [usersData]
  );

  // Event handlers
  const handleFiltersChange = useCallback(
    (newFilters: Partial<UserFiltersType & { page?: number; limit?: number }>) => {
      setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
    },
    []
  );

  const handleUserSelect = useCallback((userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId]
    );
  }, []);

  const handleSelectAll = useCallback(
    (selected: boolean) => {
      setSelectedUsers(selected ? users.map(user => user.userId) : []);
    },
    [users]
  );

  const handlePaginationChange = useCallback(
    (newPagination: { pageIndex: number; pageSize: number }) => {
      setFilters(prev => ({
        ...prev,
        page: newPagination.pageIndex + 1, // Convert 0-based to 1-based
        limit: newPagination.pageSize,
      }));
    },
    []
  );

  const handleCreateUser = useCallback(
    async (userData: CreateUserRequest) => {
      try {
        logApiCall('UsersPage', '/api/v1/users', 'POST', userData);
        await createUserMutation.mutateAsync(userData);
        logApiCall('UsersPage', '/api/v1/users', 'POST', userData, 'success');
        setShowCreateDialog(false);
      } catch (error) {
        logApiCall('UsersPage', '/api/v1/users', 'POST', userData, null, error as Error);
        throw error;
      }
    },
    [createUserMutation]
  );

  const handleEditUser = useCallback((user: User) => {
    setEditingUser(user);
    setShowEditDialog(true);
  }, []);

  const handleUpdateUser = useCallback(
    async (userData: any) => {
      if (!editingUser?.userId) return;
      // Filter out any undefined values and ensure we only send valid update fields
      const updateData: UpdateUserRequest = {
        ...(userData.username && { username: userData.username }),
        ...(userData.email && { email: userData.email }),
        ...(userData.fullName && { fullName: userData.fullName }),
        ...(userData.restaurantId !== undefined && { restaurantId: userData.restaurantId }),
        ...(userData.isActive !== undefined && { isActive: userData.isActive }),
      };

      await updateUserMutation.mutateAsync({
        userId: editingUser.userId,
        userData: updateData,
      });
      setShowEditDialog(false);
      setEditingUser(null);
    },
    [editingUser, updateUserMutation]
  );

  const handleDeleteUser = useCallback((user: User) => {
    setDeletingUser(user);
    setShowDeleteDialog(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!deletingUser?.userId) return;
    try {
      await deleteUserMutation.mutateAsync(deletingUser.userId);
      setShowDeleteDialog(false);
      setDeletingUser(null);
      setAlert({ type: 'success', message: 'User deleted successfully.' });
    } catch (error: any) {
      setAlert({
        type: 'error',
        message: error.response?.data?.message || 'Failed to delete user.',
      });
    }
  }, [deletingUser, deleteUserMutation]);

  const handleBulkAction = useCallback(
    async (operation: BulkUserOperation) => {
      if (selectedUsers.length === 0) return;
      try {
        await bulkOperationMutation.mutateAsync(operation);
        setAlert({ type: 'success', message: 'Bulk operation completed successfully.' });
        setSelectedUsers([]);
        setShowBulkActionsDialog(false);
      } catch (error: any) {
        setAlert({
          type: 'error',
          message: error.response?.data?.message || 'Failed to perform bulk operation.',
        });
      }
    },
    [selectedUsers, bulkOperationMutation]
  );

  const handlePasswordReset = useCallback((user: User) => {
    setPasswordResetUser(user);
    setShowPasswordResetDialog(true);
  }, []);

  const handleConfirmPasswordReset = useCallback(
    async (options?: any) => {
      if (!passwordResetUser?.userId) return;

      try {
        await resetPasswordMutation.mutateAsync({
          userId: passwordResetUser.userId,
          options,
        });
        setShowPasswordResetDialog(false);
        setPasswordResetUser(null);
        setAlert({ type: 'success', message: 'Password reset successfully' });
      } catch (error) {
        console.error('Failed to reset password:', error);
        setAlert({ type: 'error', message: 'Failed to reset password' });
      }
    },
    [passwordResetUser, resetPasswordMutation]
  );

  const handleToggleStatus = useCallback(
    async (user: User) => {
      try {
        const isActive = !user.isActive;
        await toggleStatusMutation.mutateAsync({ userId: user.userId, isActive });
        setAlert({
          type: 'success',
          message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
        });
      } catch (error) {
        console.error('Failed to toggle user status:', error);
        setAlert({ type: 'error', message: 'Failed to toggle user status' });
      }
    },
    [toggleStatusMutation]
  );

  // Export functions removed as they're handled by UserImportExport component

  // Clear alert after 5 seconds
  React.useEffect(() => {
    if (alert) {
      const timer = (globalThis as any).setTimeout(() => setAlert(null), 5000);
      return () => (globalThis as any).clearTimeout(timer);
    }
    return undefined;
  }, [alert]);

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground text-base">
            Manage users, roles, and permissions across your organization
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => setShowImportExportDialog(true)}
            className="hidden sm:flex"
          >
            <Upload className="mr-2 h-4 w-4" />
            Import/Export
          </Button>
          <Button onClick={() => setShowCreateDialog(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* System Status & Alerts */}
      <div className="space-y-4">
        <NetworkStatusIndicator />

        {usersError && (
          <ErrorRecovery
            error={{
              message: usersError.message || 'Failed to load users',
              code: (usersError as any).code,
              statusCode: (usersError as any).response?.status,
              context: 'users_list',
            }}
            onRetry={() => refetchUsers()}
            variant="card"
          />
        )}

        {alert && (
          <Alert className={alert.type === 'error' ? 'border-destructive' : 'border-green-500'}>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}
      </div>

      {/* User Statistics */}
      <UserStats
        stats={
          stats || {
            totalUsers: 0,
            activeUsers: 0,
            inactiveUsers: 0,
            newUsersThisMonth: 0,
            usersByRole: {},
            activeUserRatio: '0%',
            inactiveUserRatio: '0%',
          }
        }
        loading={statsLoading}
      />

      {/* Bulk Actions Bar */}
      {selectedUsers.length > 0 && (
        <div className="sticky top-4 z-10">
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="default" className="font-medium">
                    {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedUsers([])}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    Clear Selection
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowBulkActionsDialog(true)}
                    className="flex items-center gap-2"
                  >
                    <UserCheck className="h-4 w-4" />
                    Bulk Actions
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced Users Table with Integrated Filters and Export */}
      <Card>
        <CardHeader className="pb-4">
          <div className="space-y-1">
            <CardTitle className="text-xl">Users</CardTitle>
            <p className="text-muted-foreground text-sm">{pagination?.total || 0} total users</p>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <UserTable
            users={users}
            loading={usersLoading}
            selectedUsers={selectedUsers}
            onSelectUser={handleUserSelect}
            onSelectAll={handleSelectAll}
            onEdit={handleEditUser}
            onDelete={user => handleDeleteUser(user)}
            onResetPassword={handlePasswordReset}
            onToggleStatus={handleToggleStatus}
            onBulkOperation={handleBulkAction}
            {...(pagination && { pagination })}
            onPaginationChange={handlePaginationChange}
            // Integrated search functionality
            filters={filters}
            onFiltersChange={handleFiltersChange}
          />
        </CardContent>
      </Card>

      {/* Dialogs - Enhanced Mobile Responsiveness */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
          </DialogHeader>
          <UserForm
            isOpen={showCreateDialog}
            onClose={() => setShowCreateDialog(false)}
            onSubmit={async data => {
              // Add default role since form doesn't collect it
              const createUserData: CreateUserRequest = {
                ...data,
                role: 'user', // Default role
              };
              await handleCreateUser(createUserData);
            }}
            loading={createUserMutation.isPending}
            restaurants={restaurants}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <UserForm
            {...(editingUser && { user: editingUser })}
            isOpen={showEditDialog}
            onClose={() => {
              setShowEditDialog(false);
              setEditingUser(null);
            }}
            onSubmit={async data => {
              await handleUpdateUser(data);
            }}
            loading={updateUserMutation.isPending}
            restaurants={restaurants}
          />
        </DialogContent>
      </Dialog>

      <UserDeleteDialog
        user={deletingUser}
        open={showDeleteDialog}
        onConfirm={handleConfirmDelete}
        onCancel={() => {
          setShowDeleteDialog(false);
          setDeletingUser(null);
        }}
      />

      <Dialog open={showBulkActionsDialog} onOpenChange={setShowBulkActionsDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bulk Actions</DialogTitle>
          </DialogHeader>
          <UserBulkActions
            selectedCount={selectedUsers.length}
            selectedUserIds={selectedUsers}
            onAction={handleBulkAction}
            onCancel={() => setShowBulkActionsDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <UserPasswordReset
        user={passwordResetUser}
        open={showPasswordResetDialog}
        onConfirm={handleConfirmPasswordReset}
        onCancel={() => {
          setShowPasswordResetDialog(false);
          setPasswordResetUser(null);
        }}
      />

      <Dialog open={showImportExportDialog} onOpenChange={setShowImportExportDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
          <DialogHeader>
            <DialogTitle>Import/Export Users</DialogTitle>
          </DialogHeader>
          <UserImportExport
            onImportComplete={() => {
              setShowImportExportDialog(false);
              refetchUsers();
              setAlert({
                type: 'success',
                message: 'Users imported successfully',
              });
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default UsersPage;
