import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { UsersIcon, UserCheckIcon, UserXIcon, TrendingUpIcon, PieChartIcon } from 'lucide-react';

const UserStats: React.FC<{
  stats: {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    newUsersThisMonth: number;
    usersByRole: Record<string, number>;
    activeUserRatio: string;
    inactiveUserRatio: string;
  };
  loading: boolean;
}> = ({ stats, loading }) => {
  const {
    totalUsers = 0,
    activeUsers = 0,
    inactiveUsers = 0,
    newUsersThisMonth = 0,
    usersByRole = {},
    activeUserRatio = '0%',
    inactiveUserRatio = '0%',
  } = stats || {};

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-4 animate-pulse rounded bg-gray-200" />
            </CardHeader>
            <CardContent>
              <div className="mb-2 h-8 w-16 animate-pulse rounded bg-gray-200" />
              <div className="h-3 w-24 animate-pulse rounded bg-gray-200" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
      {/* Total Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          <UsersIcon className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalUsers.toLocaleString()}</div>
          <p className="text-muted-foreground text-xs">All registered users</p>
        </CardContent>
      </Card>

      {/* Active Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Users</CardTitle>
          <UserCheckIcon className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{activeUsers.toLocaleString()}</div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {activeUserRatio}
            </Badge>
            <p className="text-muted-foreground text-xs">of total users</p>
          </div>
        </CardContent>
      </Card>

      {/* Inactive Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Inactive Users</CardTitle>
          <UserXIcon className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{inactiveUsers.toLocaleString()}</div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {inactiveUserRatio}
            </Badge>
            <p className="text-muted-foreground text-xs">of total users</p>
          </div>
        </CardContent>
      </Card>

      {/* New Users This Month */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">New This Month</CardTitle>
          <TrendingUpIcon className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {newUsersThisMonth.toLocaleString()}
          </div>
          <p className="text-muted-foreground text-xs">Users added this month</p>
        </CardContent>
      </Card>

      {/* Role Distribution */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">By Role</CardTitle>
          <PieChartIcon className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground space-y-1 text-xs">
            {Object.entries(usersByRole)
              .sort(([, a]: [string, number], [, b]: [string, number]) => b - a)
              .map(([role, count]: [string, number]) => (
                <div key={role} className="flex items-center justify-between">
                  <span className="capitalize">{role}</span>
                  <strong className="text-foreground font-semibold">
                    {count.toLocaleString()}
                  </strong>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserStats;
