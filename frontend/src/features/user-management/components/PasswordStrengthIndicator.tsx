import React from 'react';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { CheckCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
}

const requirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: password => password.length >= 8,
  },
  {
    label: 'Contains uppercase letter',
    test: password => /[A-Z]/.test(password),
  },
  {
    label: 'Contains lowercase letter',
    test: password => /[a-z]/.test(password),
  },
  {
    label: 'Contains number',
    test: password => /\d/.test(password),
  },
  {
    label: 'Contains special character',
    test: password => /[@$!%*?&]/.test(password),
  },
];

export function PasswordStrengthIndicator({ password, className }: PasswordStrengthIndicatorProps) {
  const metRequirements = requirements.filter(req => req.test(password));
  const strength = metRequirements.length;
  const strengthPercentage = (strength / requirements.length) * 100;

  const getStrengthLabel = () => {
    if (strength === 0)
      return { label: 'Very Weak', color: 'text-red-600', variant: 'destructive' as const };
    if (strength <= 2)
      return { label: 'Weak', color: 'text-red-500', variant: 'destructive' as const };
    if (strength <= 3)
      return { label: 'Fair', color: 'text-yellow-500', variant: 'secondary' as const };
    if (strength <= 4)
      return { label: 'Good', color: 'text-blue-500', variant: 'default' as const };
    return { label: 'Strong', color: 'text-green-600', variant: 'default' as const };
  };

  const strengthInfo = getStrengthLabel();

  const getProgressColor = () => {
    if (strength <= 2) return 'bg-red-500';
    if (strength <= 3) return 'bg-yellow-500';
    if (strength <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  if (!password) return null;

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength Bar and Label */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Password Strength</span>
          <Badge variant={strengthInfo.variant} className={strengthInfo.color}>
            {strengthInfo.label}
          </Badge>
        </div>
        <div className="relative">
          <Progress value={strengthPercentage} className="h-2" />
          <div
            className={cn(
              'absolute left-0 top-0 h-2 rounded-full transition-all duration-300',
              getProgressColor()
            )}
            style={{ width: `${strengthPercentage}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="space-y-1">
        <span className="text-muted-foreground text-sm font-medium">Requirements:</span>
        <div className="grid grid-cols-1 gap-1">
          {requirements.map((requirement, index) => {
            const isMet = requirement.test(password);
            return (
              <div
                key={index}
                className={cn(
                  'flex items-center gap-2 text-xs transition-colors',
                  isMet ? 'text-green-600' : 'text-muted-foreground'
                )}
              >
                {isMet ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
                <span>{requirement.label}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Security Tips */}
      {strength < 4 && (
        <div className="bg-muted rounded-md p-3">
          <p className="text-muted-foreground text-xs">
            <strong>Tip:</strong> Use a mix of uppercase and lowercase letters, numbers, and special
            characters for a stronger password.
          </p>
        </div>
      )}
    </div>
  );
}

export default PasswordStrengthIndicator;
