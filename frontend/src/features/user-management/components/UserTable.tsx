import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  RowSelectionState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/shared/components/ui/avatar';
import { Input } from '@/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';
import { toast } from 'sonner';

import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Edit,
  Trash,
  Key,
  UserCheck,
  UserX,
  ArrowUpDown,
  SearchIcon,
  Loader2,
} from 'lucide-react';
import { User, UserFilters as UserFiltersType, BulkUserOperation } from '../types';
import { formatUserRole, getUserInitials, formatRelativeTime, debounce } from '../utils';
import { logButtonClick } from '../utils/debug';
import { useDeleteUser, useToggleUserStatus, useResetPassword } from '../hooks';

// Enhanced error boundary for comprehensive error handling
class ActionErrorBoundary extends React.Component<
  {
    children: React.ReactNode;
    fallback?: React.ReactNode;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  },
  { hasError: boolean; errorMessage?: string }
> {
  constructor(props: {
    children: React.ReactNode;
    fallback?: React.ReactNode;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('ActionErrorBoundary caught error:', error);
    return {
      hasError: true,
      errorMessage: error.message || 'An unexpected error occurred',
    };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('UserTable Action Error:', error, errorInfo);

    // Enhanced error logging
    console.group('Error Details');
    console.error('Error:', error);
    console.error('Error stack:', error.stack);
    console.error('Component stack:', errorInfo.componentStack);
    console.error('Error boundary props:', this.props);
    console.groupEnd();

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Show user-friendly error notification
    toast.error('An error occurred while performing this action. Please try again.');
  }

  override render() {
    if (this.state.hasError) {
      console.warn('ActionErrorBoundary rendering fallback due to error');
      return (
        this.props.fallback || (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0" disabled>
                  <span className="sr-only">Action unavailable due to error</span>
                  <MoreHorizontal className="h-4 w-4 opacity-50" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="text-xs">
                <p>Action temporarily unavailable</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      );
    }

    return this.props.children;
  }
}

// Enhanced type definitions for better TypeScript safety
interface TablePagination {
  readonly pageIndex: number;
  readonly pageSize: number;
  readonly total: number;
}

interface TableSorting {
  readonly id: string;
  readonly desc: boolean;
}

interface PaginationChangeParams {
  readonly pageIndex: number;
  readonly pageSize: number;
}

// Enhanced error types for better error handling
interface ApiError {
  readonly response?: {
    readonly data?: {
      readonly message?: string;
      readonly code?: string;
      readonly details?: Record<string, unknown>;
    };
    readonly status?: number;
  };
  readonly message?: string;
  readonly name?: string;
}

// Main component props interface with improved type safety and TanStack React Query integration
interface UserTableProps {
  readonly users: User[];
  readonly loading?: boolean;
  // Optional callback-based handlers for backward compatibility
  readonly onEdit?: (user: User) => void;
  readonly onDelete?: (user: User) => void;
  readonly onToggleStatus?: (user: User) => void;
  readonly onResetPassword?: (user: User) => void;
  readonly onBulkOperation?: (operation: BulkUserOperation) => void;
  // Selection state
  readonly selectedUsers: number[];
  readonly onSelectUser: (userId: number) => void;
  readonly onSelectAll: (selected: boolean) => void;
  // Table state
  readonly pagination?: TablePagination;
  readonly onPaginationChange?: (pagination: PaginationChangeParams) => void;
  readonly sorting?: TableSorting[];
  readonly onSortingChange?: (sorting: TableSorting[]) => void;
  readonly globalFilter?: string;
  readonly onGlobalFilterChange?: (filter: string) => void;
  // Integrated search functionality
  readonly filters?: UserFiltersType;
  readonly onFiltersChange?: (filters: UserFiltersType) => void;
  // TanStack React Query integration options
  readonly enableOptimisticUpdates?: boolean;
  readonly enableInlineEditing?: boolean;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  loading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onResetPassword,
  selectedUsers,
  onSelectUser,
  onSelectAll,
  pagination,
  onPaginationChange,
  sorting = [],
  onSortingChange,
  // Integrated search functionality
  filters,
  onFiltersChange,
  // TanStack React Query integration options
  enableOptimisticUpdates = true,
  enableInlineEditing = false,
}) => {
  // TanStack React Query mutations for CRUD operations with enhanced type safety
  const deleteUserMutation = useDeleteUser({
    onSuccess: () => {
      if (!enableOptimisticUpdates) {
        toast.success('User deleted successfully');
      }
    },
    onError: (error: ApiError) => {
      const errorMessage =
        error?.response?.data?.message || error?.message || 'Failed to delete user';
      toast.error(errorMessage);
    },
  });

  const toggleStatusMutation = useToggleUserStatus();

  const resetPasswordMutation = useResetPassword();

  // Search state management (integrated from UserFilters)
  const [searchValue, setSearchValue] = useState(filters?.search || '');
  const [isSearching, setIsSearching] = useState(false);

  // Role filter state management
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'staff' | 'user'>(
    filters?.role || 'all'
  );

  // Use ref to store current filters to avoid stale closure in debounced function
  const filtersRef = useRef(filters);
  filtersRef.current = filters;

  // Stable debounced search function with proper React Hooks compliance
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        if (onFiltersChange) {
          setIsSearching(false);
          onFiltersChange({
            ...filtersRef.current,
            search: value.trim(),
            page: 1, // Reset to first page when searching
          });
        }
      }, 300),
    [onFiltersChange] // Remove filters dependency to prevent recreation of debounced function
  );

  // Effect to trigger debounced search when searchValue changes
  useEffect(() => {
    const currentSearch = filters?.search || '';
    if (searchValue !== currentSearch) {
      setIsSearching(true);
      debouncedSearch(searchValue);
    }

    // Cleanup function to cancel pending debounced calls
    return () => {
      if (debouncedSearch.cancel) {
        debouncedSearch.cancel();
      }
    };
  }, [searchValue, debouncedSearch, filters?.search]);

  // Sync search value with external filter changes (avoid infinite loops)
  useEffect(() => {
    const externalSearch = filters?.search || '';
    if (externalSearch !== searchValue) {
      setSearchValue(externalSearch);
      setIsSearching(false);
    }
  }, [filters?.search]); // Remove searchValue to prevent infinite loop

  // Role filter change handler
  const handleRoleFilterChange = useCallback(
    (value: 'all' | 'admin' | 'staff' | 'user') => {
      setRoleFilter(value);
      if (onFiltersChange) {
        onFiltersChange({
          ...filtersRef.current,
          role: value,
          page: 1, // Reset to first page when role filter changes
        });
      }
    },
    [onFiltersChange]
  );

  // Sync role filter with external filter changes
  useEffect(() => {
    const externalRole = filters?.role || 'all';
    if (externalRole !== roleFilter) {
      setRoleFilter(externalRole);
    }
  }, [filters?.role, roleFilter]);

  // Keyboard shortcuts for search
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Focus search on Ctrl/Cmd + K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        const searchInput = document.querySelector(
          'input[aria-label*="Search users"]'
        ) as HTMLInputElement;
        searchInput?.focus();
      }
      // Clear search on Escape when search input is focused
      if (
        event.key === 'Escape' &&
        document.activeElement?.getAttribute('aria-label')?.includes('Search users')
      ) {
        setSearchValue('');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
  // Enhanced action handlers with comprehensive error handling and retry logic
  const handleEditUser = useCallback(
    (user: User) => {
      try {
        logButtonClick('UserTable', 'Edit', user);
        // Use callback if provided, otherwise handle inline editing
        if (onEdit) {
          onEdit(user);
        } else if (enableInlineEditing) {
          // TODO: Implement inline editing functionality with React Hook Form + Zod
          // This would involve:
          // 1. Converting table cells to editable form fields
          // 2. Using React Hook Form for form state management
          // 3. Zod validation for field-level validation
          // 4. Optimistic updates for better UX
          console.log('Inline editing not yet implemented');
          toast.info('Inline editing feature coming soon');
        } else {
          toast.info('Edit functionality not configured');
        }
      } catch (error) {
        const err = error as ApiError;
        logButtonClick('UserTable', 'Edit', user, error as Error);
        console.error('Edit action failed:', error);
        const errorMessage =
          err?.response?.data?.message || err?.message || 'Failed to initiate edit action';
        toast.error(`${errorMessage}. Please try again.`);
      }
    },
    [onEdit, enableInlineEditing]
  );

  const handleDeleteUser = useCallback(
    async (user: User) => {
      try {
        logButtonClick('UserTable', 'Delete', user);

        // Use callback if provided, otherwise use mutation directly
        if (onDelete) {
          onDelete(user);
        } else {
          // Show confirmation before deletion
          const confirmed = window.confirm(
            `Are you sure you want to delete ${user.fullName || user.username}? This action cannot be undone.`
          );

          if (confirmed) {
            await deleteUserMutation.mutateAsync(user.userId);
          }
        }
      } catch (error) {
        const err = error as ApiError;
        logButtonClick('UserTable', 'Delete', user, error as Error);
        console.error('Delete action failed:', error);

        // Enhanced error handling with specific error types
        const errorMessage = err?.response?.data?.message || err?.message || '';
        const statusCode = err?.response?.status;

        if (errorMessage.includes('network') || !navigator.onLine) {
          toast.error('Network error. Please check your connection and try again.');
        } else if (statusCode === 403 || errorMessage.includes('permission')) {
          toast.error('You do not have permission to delete this user.');
        } else if (statusCode === 404) {
          toast.error('User not found. It may have already been deleted.');
        } else {
          toast.error('Failed to delete user. Please try again.');
        }
      }
    },
    [onDelete, deleteUserMutation]
  );

  const handleToggleUserStatus = useCallback(
    async (user: User) => {
      try {
        logButtonClick('UserTable', 'ToggleStatus', user);

        // Use callback if provided, otherwise use mutation directly
        if (onToggleStatus) {
          onToggleStatus(user);
        } else {
          // Optimistic update with immediate feedback
          if (enableOptimisticUpdates) {
            toast.success(`User ${!user.isActive ? 'activated' : 'deactivated'} successfully`);
          }

          await toggleStatusMutation.mutateAsync({
            userId: user.userId,
            isActive: !user.isActive,
          });
        }
      } catch (error) {
        const err = error as ApiError;
        logButtonClick('UserTable', 'ToggleStatus', user, error as Error);
        console.error('Toggle status action failed:', error);

        // Enhanced error handling with rollback for optimistic updates
        const statusCode = err?.response?.status;

        if (enableOptimisticUpdates) {
          toast.error('Failed to update user status. Changes have been reverted.');
        } else if (statusCode === 403) {
          toast.error('You do not have permission to modify this user.');
        } else if (statusCode === 404) {
          toast.error('User not found. Please refresh the page.');
        } else {
          toast.error('Failed to update user status. Please try again.');
        }
      }
    },
    [onToggleStatus, toggleStatusMutation, enableOptimisticUpdates]
  );

  const handleResetUserPassword = useCallback(
    async (user: User) => {
      try {
        logButtonClick('UserTable', 'ResetPassword', user);

        // Use callback if provided, otherwise use mutation directly
        if (onResetPassword) {
          onResetPassword(user);
        } else {
          // Show confirmation before password reset
          const confirmed = window.confirm(
            `Are you sure you want to reset the password for ${user.fullName || user.username}? A temporary password will be generated.`
          );

          if (confirmed) {
            await resetPasswordMutation.mutateAsync({
              userId: user.userId,
              options: { temporaryPassword: true, sendEmail: true },
            });
          }
        }
      } catch (error) {
        const err = error as ApiError;
        logButtonClick('UserTable', 'ResetPassword', user, error as Error);
        console.error('Reset password action failed:', error);

        // Enhanced error handling
        const errorMessage = err?.response?.data?.message || err?.message || '';
        const statusCode = err?.response?.status;

        if (errorMessage.includes('email') || statusCode === 502) {
          toast.error('Failed to send password reset email. Please try again.');
        } else if (statusCode === 403) {
          toast.error("You do not have permission to reset this user's password.");
        } else if (statusCode === 404) {
          toast.error('User not found. Please refresh the page.');
        } else {
          toast.error('Failed to reset password. Please try again.');
        }
      }
    },
    [onResetPassword, resetPasswordMutation]
  );

  // Memoized column creation functions for better organization
  const createSelectionColumn = useCallback(
    (): ColumnDef<User> => ({
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={value => {
            table.toggleAllPageRowsSelected(!!value);
            onSelectAll(!!value);
          }}
          aria-label="Select all users"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedUsers.includes(row.original.userId)}
          onCheckedChange={_value => {
            onSelectUser(row.original.userId);
          }}
          aria-label={`Select user ${row.original.fullName || row.original.username}`}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 50,
    }),
    [selectedUsers, onSelectUser, onSelectAll]
  );

  const createUserColumn = useCallback(
    (): ColumnDef<User> => ({
      accessorKey: 'fullName',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={`Sort by user name ${column.getIsSorted() === 'asc' ? 'descending' : 'ascending'}`}
        >
          User
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">{getUserInitials(user)}</AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{user.fullName || user.username || 'Unnamed User'}</div>
              <div className="text-muted-foreground text-sm">{user.email}</div>
            </div>
          </div>
        );
      },
    }),
    []
  );

  const createRoleColumn = useCallback(
    (): ColumnDef<User> => ({
      accessorKey: 'role',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={`Sort by role ${column.getIsSorted() === 'asc' ? 'descending' : 'ascending'}`}
        >
          Role
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const role = formatUserRole(row.getValue('role'));
        return (
          <Badge variant="secondary" className="font-medium">
            {role}
          </Badge>
        );
      },
    }),
    []
  );

  const createStatusColumn = useCallback(
    (): ColumnDef<User> => ({
      accessorKey: 'isActive',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={`Sort by status ${column.getIsSorted() === 'asc' ? 'descending' : 'ascending'}`}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const isActive = row.getValue('isActive');
        const isTogglingStatus = toggleStatusMutation.isPending;

        return (
          <div className="flex items-center gap-2">
            <Badge
              variant={isActive ? 'default' : 'secondary'}
              className={`font-medium transition-opacity ${isTogglingStatus ? 'opacity-50' : ''}`}
            >
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
            {isTogglingStatus && <Loader2 className="text-muted-foreground h-3 w-3 animate-spin" />}
          </div>
        );
      },
    }),
    [toggleStatusMutation.isPending]
  );

  const createLastLoginColumn = useCallback(
    (): ColumnDef<User> => ({
      accessorKey: 'lastLoginAt',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={`Sort by last login ${column.getIsSorted() === 'asc' ? 'descending' : 'ascending'}`}
        >
          Last Login
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const lastLogin = row.getValue('lastLoginAt') as string;
        return (
          <div className="text-muted-foreground text-sm">
            {lastLogin ? formatRelativeTime(lastLogin) : 'Never'}
          </div>
        );
      },
    }),
    []
  );

  const createActionsColumn = useCallback(
    (): ColumnDef<User> => ({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const user = row.original;
        const isDeleting = deleteUserMutation.isPending;
        const isTogglingStatus = toggleStatusMutation.isPending;
        const isResettingPassword = resetPasswordMutation.isPending;
        const isAnyMutationPending = isDeleting || isTogglingStatus || isResettingPassword;

        return (
          <ActionErrorBoundary>
            <div className="flex items-center justify-end gap-1">
              {/* Primary Action - Edit (prominent with enhanced styling) */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="hover:bg-primary/10 hover:text-primary h-8 w-8 p-0 transition-colors duration-200"
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleEditUser(user);
                      }}
                      disabled={isAnyMutationPending}
                      aria-label={`Edit user ${user.fullName || user.username}`}
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit user</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="text-xs">
                    <p>Edit user details</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Secondary Actions - Dropdown (refined styling) */}
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="hover:bg-muted/80 data-[state=open]:bg-muted h-8 w-8 p-0 transition-colors duration-200"
                    disabled={isAnyMutationPending}
                    aria-label={`Open actions menu for user ${user.fullName || user.username}`}
                  >
                    <span className="sr-only">Open user actions menu</span>
                    {isAnyMutationPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MoreHorizontal className="h-4 w-4" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="z-[1000] w-52 shadow-lg"
                  sideOffset={5}
                  avoidCollisions={true}
                  collisionPadding={10}
                  onCloseAutoFocus={e => e.preventDefault()}
                >
                  <DropdownMenuItem
                    onClick={e => {
                      e.stopPropagation();
                      handleResetUserPassword(user);
                    }}
                    disabled={isResettingPassword}
                    className="cursor-pointer transition-colors duration-150"
                  >
                    {isResettingPassword ? (
                      <Loader2 className="text-muted-foreground mr-3 h-4 w-4 animate-spin" />
                    ) : (
                      <Key className="text-muted-foreground mr-3 h-4 w-4" />
                    )}
                    <span className="font-medium">Reset Password</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={e => {
                      e.stopPropagation();
                      handleToggleUserStatus(user);
                    }}
                    disabled={isTogglingStatus}
                    className="cursor-pointer transition-colors duration-150"
                  >
                    {isTogglingStatus ? (
                      <Loader2 className="mr-3 h-4 w-4 animate-spin" />
                    ) : user.isActive ? (
                      <>
                        <UserX className="mr-3 h-4 w-4 text-orange-500" />
                        <span className="font-medium">Deactivate User</span>
                      </>
                    ) : (
                      <>
                        <UserCheck className="mr-3 h-4 w-4 text-green-500" />
                        <span className="font-medium">Activate User</span>
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={e => {
                      e.stopPropagation();
                      handleDeleteUser(user);
                    }}
                    disabled={isDeleting}
                    className="text-destructive focus:text-destructive cursor-pointer transition-colors duration-150"
                  >
                    {isDeleting ? (
                      <Loader2 className="mr-3 h-4 w-4 animate-spin" />
                    ) : (
                      <Trash className="mr-3 h-4 w-4" />
                    )}
                    <span className="font-medium">Delete User</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </ActionErrorBoundary>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 100,
    }),
    [
      handleEditUser,
      handleDeleteUser,
      handleToggleUserStatus,
      handleResetUserPassword,
      deleteUserMutation.isPending,
      toggleStatusMutation.isPending,
      resetPasswordMutation.isPending,
    ]
  );

  // Define columns for TanStack Table using memoized column creators
  const columns = useMemo<ColumnDef<User>[]>(
    () => [
      createSelectionColumn(),
      createUserColumn(),
      createRoleColumn(),
      createStatusColumn(),
      createLastLoginColumn(),
      createActionsColumn(),
    ],
    [
      createSelectionColumn,
      createUserColumn,
      createRoleColumn,
      createStatusColumn,
      createLastLoginColumn,
      createActionsColumn,
    ]
  );

  // Memoized search and filter results info for performance
  const searchInfo = useMemo(() => {
    const hasSearch = searchValue.trim().length > 0;
    const hasRoleFilter = roleFilter !== 'all';
    const resultCount = users.length;
    return {
      hasSearch,
      hasRoleFilter,
      resultCount,
      searchTerm: searchValue.trim(),
      roleFilter,
    };
  }, [searchValue, roleFilter, users.length]);

  // Row selection state
  const rowSelection = useMemo(() => {
    const selection: RowSelectionState = {};
    const selectedUserIds = new Set(selectedUsers);
    users.forEach((user, index) => {
      if (selectedUserIds.has(user.userId)) {
        selection[index] = true;
      }
    });
    return selection;
  }, [selectedUsers, users]);

  // Table instance with optimized configuration
  const table = useReactTable({
    data: users,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: updater => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      onSortingChange?.(newSorting);
    },
    onRowSelectionChange: () => {}, // Handled by our custom logic
    state: {
      sorting,
      rowSelection,
      // Global filter state for TanStack Table (not used for server-side search)
      globalFilter: searchValue,
      pagination: pagination
        ? {
            pageIndex: pagination.pageIndex,
            pageSize: pagination.pageSize,
          }
        : {
            pageIndex: 0,
            pageSize: 10,
          },
    },
    // Use manual pagination and sorting for server-side operations
    manualPagination: !!pagination,
    manualSorting: !!onSortingChange,
    // Disable client-side filtering since we use server-side search
    manualFiltering: true,
    ...(pagination && { pageCount: Math.ceil(pagination.total / pagination.pageSize) }),
    // Performance optimizations
    enableRowSelection: true,
    enableMultiRowSelection: true,
    enableSubRowSelection: false,
  });

  if (loading) {
    return (
      <div className="space-y-4">
        {/* Search and Filters - Available even during loading */}
        {onFiltersChange && (
          <div className="flex flex-col gap-4 p-4 sm:flex-row sm:items-center sm:justify-between sm:p-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
              {/* Search Input - Primary action with enhanced mobile-first design */}
              <div className="relative max-w-md flex-1">
                <SearchIcon className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
                <Input
                  placeholder="Search by name, email, or username... (⌘K)"
                  value={searchValue}
                  onChange={e => setSearchValue(e.target.value)}
                  className="border-border/60 focus:border-primary focus:ring-primary/20 h-11 pl-10 pr-16 text-base transition-all duration-200 focus:ring-2 sm:text-sm"
                  aria-label="Search users by full name, email, or username. Press Ctrl+K or Cmd+K to focus"
                  autoComplete="off"
                  spellCheck="false"
                />
                {/* Search state indicators */}
                <div className="absolute right-3 top-1/2 flex -translate-y-1/2 items-center gap-1">
                  {isSearching && (
                    <div className="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent" />
                  )}
                  {searchValue.trim() && !isSearching && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="hover:bg-muted h-6 w-6 p-0"
                      onClick={() => setSearchValue('')}
                      aria-label="Clear search"
                    >
                      <svg
                        className="h-3 w-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </Button>
                  )}
                </div>
              </div>

              {/* Role Filter - Secondary action with progressive disclosure */}
              <div className="w-full sm:w-auto sm:min-w-[140px]">
                <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
                  <SelectTrigger
                    className="border-border/60 focus:border-primary focus:ring-primary/20 h-11 text-base transition-all duration-200 focus:ring-2 sm:text-sm"
                    aria-label="Filter users by role"
                  >
                    <SelectValue placeholder="Filter by role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="staff">Staff</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Results count - Secondary information with search context */}
            <div className="text-muted-foreground px-2 py-1 text-sm sm:px-0 sm:py-0">
              Loading...
            </div>
          </div>
        )}

        <div className="bg-card rounded-lg border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id} className="border-b">
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id} className="h-12 px-4">
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index} className="hover:bg-muted/50 border-b transition-colors">
                  <TableCell className="px-4 py-3">
                    <div className="bg-muted h-4 w-4 animate-pulse rounded" />
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="bg-muted h-8 w-8 animate-pulse rounded-full" />
                      <div className="space-y-2">
                        <div className="bg-muted h-4 w-32 animate-pulse rounded" />
                        <div className="bg-muted/70 h-3 w-24 animate-pulse rounded" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="bg-muted h-4 w-16 animate-pulse rounded" />
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="bg-muted h-4 w-16 animate-pulse rounded" />
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="bg-muted h-4 w-20 animate-pulse rounded" />
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="bg-muted h-4 w-16 animate-pulse rounded" />
                  </TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="flex justify-end gap-1">
                      <div className="bg-muted h-8 w-8 animate-pulse rounded" />
                      <div className="bg-muted h-8 w-8 animate-pulse rounded" />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Progressive Disclosure: Search and Filters */}
      {onFiltersChange && (
        <div className="flex flex-col gap-4 p-4 sm:flex-row sm:items-center sm:justify-between sm:p-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
            {/* Search Input - Primary action with enhanced mobile-first design */}
            <div className="relative max-w-md flex-1">
              <SearchIcon className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name, email, or username... (⌘K)"
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                className="border-border/60 focus:border-primary focus:ring-primary/20 h-11 pl-10 pr-16 text-base transition-all duration-200 focus:ring-2 sm:text-sm"
                aria-label="Search users by full name, email, or username. Press Ctrl+K or Cmd+K to focus"
                autoComplete="off"
                spellCheck="false"
              />
              {/* Search state indicators */}
              <div className="absolute right-3 top-1/2 flex -translate-y-1/2 items-center gap-1">
                {isSearching && (
                  <div className="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent" />
                )}
                {searchValue.trim() && !isSearching && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="hover:bg-muted h-6 w-6 p-0"
                    onClick={() => setSearchValue('')}
                    aria-label="Clear search"
                  >
                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </Button>
                )}
              </div>
            </div>

            {/* Role Filter - Secondary action with progressive disclosure */}
            <div className="w-full sm:w-auto sm:min-w-[140px]">
              <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
                <SelectTrigger
                  className="border-border/60 focus:border-primary focus:ring-primary/20 h-11 text-base transition-all duration-200 focus:ring-2 sm:text-sm"
                  aria-label="Filter users by role"
                >
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Users</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="staff">Staff</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results count - Secondary information with search and filter context */}
          <div className="text-muted-foreground px-2 py-1 text-sm sm:px-0 sm:py-0">
            {searchInfo.hasSearch || searchInfo.hasRoleFilter ? (
              <span>
                {searchInfo.resultCount} result{searchInfo.resultCount !== 1 ? 's' : ''}
                {searchInfo.hasSearch && <span> for "{searchInfo.searchTerm}"</span>}
                {searchInfo.hasRoleFilter && (
                  <span>
                    {' '}
                    in {formatUserRole(searchInfo.roleFilter as 'admin' | 'staff' | 'user')}
                  </span>
                )}
              </span>
            ) : (
              <span>
                {searchInfo.resultCount} user{searchInfo.resultCount !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Table with Better Visual Hierarchy */}
      <div className="border-border/40 bg-card overflow-hidden rounded-lg border shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow
                  key={headerGroup.id}
                  className="border-border/40 bg-muted/20 hover:bg-muted/30 border-b transition-colors"
                >
                  {headerGroup.headers.map(header => (
                    <TableHead
                      key={header.id}
                      className="text-muted-foreground h-14 px-3 text-left align-middle text-xs font-semibold sm:px-6 sm:text-sm"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className="border-border/30 hover:bg-muted/40 data-[state=selected]:bg-primary/5 data-[state=selected]:border-primary/20 border-b transition-all duration-200"
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id} className="px-3 py-4 align-middle text-sm sm:px-6">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-32 text-center">
                    <div className="text-muted-foreground flex flex-col items-center justify-center space-y-3">
                      <div className="bg-muted rounded-full p-3">
                        <svg
                          className="h-6 w-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                          />
                        </svg>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">
                          {searchInfo.hasSearch ? 'No users match your search' : 'No users found'}
                        </p>
                        <p className="text-xs">
                          {searchInfo.hasSearch
                            ? `Try searching for different terms or check your spelling`
                            : 'Try adjusting your filter criteria or create a new user'}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Enhanced Pagination with Progressive Disclosure */}
      {pagination && onPaginationChange && (
        <div className="border-border/40 bg-card/50 flex flex-col gap-3 rounded-lg border p-4 shadow-sm sm:flex-row sm:items-center sm:justify-between">
          {/* Selection info - Secondary information */}
          {table.getFilteredSelectedRowModel().rows.length > 0 && (
            <div className="text-muted-foreground text-sm">
              <span className="font-medium">{table.getFilteredSelectedRowModel().rows.length}</span>{' '}
              of <span className="font-medium">{table.getFilteredRowModel().rows.length}</span>{' '}
              selected
            </div>
          )}

          {/* Pagination controls - Primary actions */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-6">
            {/* Page size selector - Less prominent */}
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Show</span>
              <select
                value={pagination.pageSize}
                onChange={e => {
                  onPaginationChange({
                    pageIndex: 0,
                    pageSize: Number(e.target.value),
                  });
                }}
                className="border-border/60 bg-background hover:bg-accent focus:ring-primary/20 h-9 w-16 rounded-md border px-2 text-sm transition-colors focus:outline-none focus:ring-2"
                aria-label="Rows per page"
              >
                {[10, 20, 30, 40, 50].map(pageSize => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>

            {/* Page info - Prominent */}
            <div className="flex items-center justify-center text-sm font-medium">
              Page{' '}
              <span className="text-primary mx-1 font-semibold">{pagination.pageIndex + 1}</span> of{' '}
              <span className="ml-1 font-semibold">
                {Math.ceil(pagination.total / pagination.pageSize)}
              </span>
            </div>
            {/* Navigation buttons - Touch-friendly for mobile */}
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                className="hidden h-9 w-9 p-0 sm:flex"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: 0,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex === 0}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-9 w-9 p-0"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex - 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex === 0}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-9 w-9 p-0"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex + 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={
                  pagination.pageIndex >= Math.ceil(pagination.total / pagination.pageSize) - 1
                }
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="hidden h-9 w-9 p-0 sm:flex"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: Math.ceil(pagination.total / pagination.pageSize) - 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={
                  pagination.pageIndex >= Math.ceil(pagination.total / pagination.pageSize) - 1
                }
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserTable;
