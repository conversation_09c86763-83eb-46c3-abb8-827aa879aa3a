import React, { useState, useC<PERSON>back, useMemo } from 'react';
import { Users, UserCheck, UserX, Trash2, Shield } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';

import { Badge } from '@/shared/components/ui/badge';
import { BulkUserOperation } from '../types';

// Enhanced type definitions for better component architecture
type UserRole =
  | 'admin'
  | 'staff'
  | 'user'
  | 'manager'
  | 'restaurant_manager'
  | 'finance_manager'
  | 'hr_manager';

interface BulkAction {
  readonly id: 'activate' | 'deactivate' | 'delete';
  readonly label: string;
  readonly description: string;
  readonly icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  readonly color: string;
  readonly action: () => void;
}

// Main component props interface with improved type safety
interface UserBulkActionsProps {
  readonly selectedCount: number;
  readonly selectedUserIds: number[];
  readonly onAction: (operation: BulkUserOperation) => void;
  readonly onCancel: () => void;
}

const UserBulkActions: React.FC<UserBulkActionsProps> = ({
  selectedCount,
  selectedUserIds,
  onAction,
  onCancel,
}) => {
  const [selectedRole, setSelectedRole] = useState<UserRole | ''>('');

  const handleRoleUpdate = useCallback(() => {
    if (!selectedRole) return;

    onAction({
      action: 'updateRole',
      userIds: selectedUserIds,
      data: { role: selectedRole as 'admin' | 'staff' | 'user' },
    });
  }, [selectedRole, selectedUserIds, onAction]);

  // Memoized bulk actions configuration for better performance
  const bulkActions = useMemo<BulkAction[]>(
    () => [
      {
        id: 'activate',
        label: 'Activate Users',
        description: 'Set selected users as active',
        icon: UserCheck,
        color: 'text-green-600',
        action: () => onAction({ action: 'activate', userIds: selectedUserIds }),
      },
      {
        id: 'deactivate',
        label: 'Deactivate Users',
        description: 'Set selected users as inactive',
        icon: UserX,
        color: 'text-yellow-600',
        action: () => onAction({ action: 'deactivate', userIds: selectedUserIds }),
      },
      {
        id: 'delete',
        label: 'Delete Users',
        description: 'Permanently remove selected users',
        icon: Trash2,
        color: 'text-red-600',
        action: () => onAction({ action: 'delete', userIds: selectedUserIds }),
      },
    ],
    [selectedUserIds, onAction]
  );

  return (
    <div className="space-y-4">
      {/* Header with Progressive Disclosure - Prominent selection count */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Users className="text-primary h-5 w-5" />
          <h3 className="text-lg font-semibold">Bulk Actions</h3>
        </div>
        <Badge variant="default" className="w-fit">
          {selectedCount} user{selectedCount !== 1 ? 's' : ''} selected
        </Badge>
      </div>

      {/* Primary Actions - Status Management (Most Common) */}
      <Card className="border-border/60">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {bulkActions.map(action => {
            const Icon = action.icon;
            const isDestructive = action.id === 'delete';
            return (
              <div
                key={action.id}
                className="hover:bg-muted/30 border-border/40 flex items-center justify-between rounded-lg border p-3 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`rounded-md p-1.5 ${
                      isDestructive ? 'bg-destructive/10' : 'bg-muted'
                    }`}
                  >
                    <Icon className={`h-4 w-4 ${action.color}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{action.label}</p>
                    <p className="text-muted-foreground text-xs">{action.description}</p>
                  </div>
                </div>
                <Button
                  variant={isDestructive ? 'destructive' : 'outline'}
                  size="sm"
                  onClick={action.action}
                  className="h-8 px-3 text-xs font-medium"
                >
                  Apply
                </Button>
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Secondary Actions - Role Management (Less Common) */}
      <Card className="border-border/60">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base font-medium">
            <Shield className="text-muted-foreground h-4 w-4" />
            Role Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <label className="text-muted-foreground text-sm font-medium">
              Assign new role to selected users
            </label>
            <Select
              value={selectedRole}
              onValueChange={(value: string) => setSelectedRole(value as UserRole | '')}
            >
              <SelectTrigger className="border-border/60 focus:ring-primary/20 h-10">
                <SelectValue placeholder="Choose role..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="restaurant_manager">Restaurant Manager</SelectItem>
                <SelectItem value="finance_manager">Finance Manager</SelectItem>
                <SelectItem value="hr_manager">HR Manager</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleRoleUpdate}
            disabled={!selectedRole}
            className="h-10 w-full"
            variant="secondary"
          >
            Update Role for {selectedCount} User{selectedCount !== 1 ? 's' : ''}
          </Button>
        </CardContent>
      </Card>

      {/* Action Buttons - Prominent Cancel */}
      <div className="flex justify-end pt-2">
        <Button variant="outline" onClick={onCancel} className="h-10 px-6">
          Cancel Selection
        </Button>
      </div>
    </div>
  );
};

export default UserBulkActions;
