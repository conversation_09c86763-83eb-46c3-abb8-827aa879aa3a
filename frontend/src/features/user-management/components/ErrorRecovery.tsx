import React from 'react';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { AlertTriangle, RefreshCw, WifiOff, Clock, Shield, Home, Bug } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ErrorRecoveryProps {
  error?: {
    message: string;
    code?: string;
    statusCode?: number;
    context?: string;
  };
  isRetrying?: boolean;
  onRetry?: () => void;
  onGoHome?: () => void;
  onReportBug?: () => void;
  className?: string;
  variant?: 'inline' | 'page' | 'card';
}

export function ErrorRecovery({
  error,
  isRetrying = false,
  onRetry,
  onGoHome,
  onReportBug,
  className,
  variant = 'inline',
}: ErrorRecoveryProps) {
  if (!error) return null;

  const getErrorIcon = () => {
    if (error.code === 'NETWORK_ERROR') return <WifiOff className="h-5 w-5" />;
    if (error.code === 'TIMEOUT') return <Clock className="h-5 w-5" />;
    if (error.statusCode === 401 || error.statusCode === 403) return <Shield className="h-5 w-5" />;
    return <AlertTriangle className="h-5 w-5" />;
  };

  const getErrorTitle = () => {
    if (error.code === 'NETWORK_ERROR') return 'Connection Problem';
    if (error.code === 'TIMEOUT') return 'Request Timeout';
    if (error.statusCode === 401) return 'Authentication Required';
    if (error.statusCode === 403) return 'Access Denied';
    if (error.statusCode === 404) return 'Not Found';
    if (error.statusCode === 409) return 'Conflict';
    if (error.statusCode && error.statusCode >= 500) return 'Server Error';
    return 'Error';
  };

  const getErrorSuggestions = () => {
    const suggestions: string[] = [];

    if (error.code === 'NETWORK_ERROR') {
      suggestions.push('Check your internet connection');
      suggestions.push('Try refreshing the page');
      suggestions.push('Contact your network administrator if the problem persists');
    } else if (error.code === 'TIMEOUT') {
      suggestions.push('The request took too long to complete');
      suggestions.push('Try again with a more specific search');
      suggestions.push('Check your internet connection speed');
    } else if (error.statusCode === 401) {
      suggestions.push('Your session may have expired');
      suggestions.push('Please log in again');
    } else if (error.statusCode === 403) {
      suggestions.push("You don't have permission for this action");
      suggestions.push('Contact your administrator for access');
    } else if (error.statusCode === 404) {
      suggestions.push('The requested resource was not found');
      suggestions.push('It may have been moved or deleted');
    } else if (error.statusCode === 409) {
      suggestions.push('The resource was modified by another user');
      suggestions.push('Refresh the page and try again');
    } else if (error.statusCode && error.statusCode >= 500) {
      suggestions.push('This is a temporary server issue');
      suggestions.push('Please try again in a few moments');
      suggestions.push('Contact support if the problem continues');
    } else {
      suggestions.push('Please try again');
      suggestions.push('If the problem persists, contact support');
    }

    return suggestions;
  };

  const isRetryable = () => {
    return (
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT' ||
      (error.statusCode && error.statusCode >= 500) ||
      error.statusCode === 429
    );
  };

  const renderActions = () => (
    <div className="mt-4 flex flex-wrap gap-2">
      {isRetryable() && onRetry && (
        <Button
          onClick={onRetry}
          disabled={isRetrying}
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn('h-4 w-4', isRetrying && 'animate-spin')} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </Button>
      )}

      {onGoHome && (
        <Button variant="outline" onClick={onGoHome} size="sm" className="flex items-center gap-2">
          <Home className="h-4 w-4" />
          Go Home
        </Button>
      )}

      {onReportBug && (
        <Button
          variant="outline"
          onClick={onReportBug}
          size="sm"
          className="flex items-center gap-2"
        >
          <Bug className="h-4 w-4" />
          Report Issue
        </Button>
      )}
    </div>
  );

  if (variant === 'page') {
    return (
      <div className={cn('flex min-h-[400px] items-center justify-center p-4', className)}>
        <Card className="w-full max-w-lg">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center gap-2">
              {getErrorIcon()}
              {getErrorTitle()}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">{error.message}</p>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">What you can try:</h4>
              <ul className="text-muted-foreground list-inside list-disc space-y-1 text-sm">
                {getErrorSuggestions().map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            </div>

            {renderActions()}

            {error.code && (
              <div className="text-muted-foreground border-t pt-2 text-xs">
                Error Code: {error.code}
                {error.context && ` • Context: ${error.context}`}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className={cn('border-destructive', className)}>
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="text-destructive">{getErrorIcon()}</div>
            <div className="flex-1 space-y-2">
              <h4 className="text-destructive font-medium">{getErrorTitle()}</h4>
              <p className="text-muted-foreground text-sm">{error.message}</p>
              {renderActions()}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Inline variant
  return (
    <Alert variant="destructive" className={className}>
      <div className="flex items-start gap-3">
        {getErrorIcon()}
        <div className="flex-1">
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">{getErrorTitle()}</p>
              <p className="text-sm">{error.message}</p>
              {renderActions()}
            </div>
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
}

// Network status indicator component
export function NetworkStatusIndicator() {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <Alert variant="destructive" className="mb-4">
      <WifiOff className="h-4 w-4" />
      <AlertDescription>
        You are currently offline. Some features may not be available.
      </AlertDescription>
    </Alert>
  );
}

export default ErrorRecovery;
