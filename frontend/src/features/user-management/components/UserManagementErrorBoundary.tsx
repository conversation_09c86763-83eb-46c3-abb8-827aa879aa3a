import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class UserManagementErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('User Management Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/admin/dashboard';
  };

  override render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="bg-background flex min-h-screen items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-6 w-6" />
                User Management Error
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="border-red-200">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  An error occurred while loading the user management interface. This could be due
                  to:
                </AlertDescription>
              </Alert>

              <ul className="ml-4 list-inside list-disc space-y-1 text-sm text-gray-600">
                <li>Missing or corrupted component dependencies</li>
                <li>Network connectivity issues</li>
                <li>Authentication or authorization problems</li>
                <li>Server-side API errors</li>
                <li>Browser compatibility issues</li>
              </ul>

              {this.state.error && (
                <div className="rounded-md bg-gray-50 p-4">
                  <h4 className="mb-2 text-sm font-semibold">Error Details:</h4>
                  <pre className="max-h-32 overflow-auto text-xs text-red-600">
                    {this.state.error.message}
                  </pre>
                </div>
              )}

              {import.meta.env.DEV && this.state.errorInfo && (
                <div className="rounded-md bg-gray-50 p-4">
                  <h4 className="mb-2 text-sm font-semibold">Stack Trace:</h4>
                  <pre className="max-h-48 overflow-auto text-xs text-gray-600">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </div>
              )}

              <div className="flex gap-3 pt-4">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Button>
              </div>

              <div className="pt-2 text-center text-sm text-gray-500">
                If this problem persists, please contact your system administrator.
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default UserManagementErrorBoundary;
