import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { Input } from '@/shared/components/ui/input';
import { But<PERSON> } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Switch } from '@/shared/components/ui/switch';
import { LoadingSpinner } from '@/shared/components/ui/loading-spinner';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/components/ui/form';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { EyeIcon, EyeOffIcon, AlertCircle } from 'lucide-react';
import { UserFormProps, UserFormData } from '../types';
import { userFormSchema, userEditFormSchema, UserEditFormData } from '../validations/userSchemas';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';

const UserForm: React.FC<UserFormProps> = ({
  user,
  isOpen,
  onClose,
  onSubmit,
  loading,
  restaurants,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const isEditing = !!user;
  const schema = isEditing ? userEditFormSchema : userFormSchema;

  const form = useForm<UserFormData | UserEditFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          username: '',
          email: '',
          fullName: '',
          restaurantId: undefined,
          isActive: true,
        }
      : {
          username: '',
          email: '',
          password: '',
          confirmPassword: '',
          fullName: '',
          restaurantId: undefined,
          isActive: true,
        },
    mode: 'onBlur',
  });

  const { handleSubmit, reset, control, watch } = form;

  // Reset form when user changes or dialog opens/closes
  // Optimized to prevent unnecessary re-renders
  useEffect(() => {
    if (isOpen) {
      setSubmitError(null); // Clear any previous errors when opening
      if (user) {
        // Edit mode - only reset fields that exist in edit schema
        reset({
          username: user.username || '',
          email: user.email,
          fullName: user.fullName || '',
          restaurantId: user.restaurantId || undefined,
          isActive: user.isActive !== false,
        });
      } else {
        // Create mode - reset all fields including password
        reset({
          username: '',
          email: '',
          password: '',
          confirmPassword: '',
          fullName: '',
          restaurantId: undefined,
          isActive: true,
        });
      }
    }
  }, [user?.userId, isOpen, reset]); // Optimized dependency array

  const onFormSubmit = async (data: UserFormData | UserEditFormData) => {
    try {
      setSubmitError(null); // Clear any previous errors
      await onSubmit(data as UserFormData);
      // onClose() is called by parent component on success
    } catch (error: any) {
      console.error('Form submission error:', error);
      // Extract error message from API response
      const errorMessage =
        error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setSubmitError(errorMessage);

      // Also show toast for immediate feedback
      toast.error(errorMessage);
    }
  };

  // Watch password for strength indicator
  const passwordValue = watch('password');

  return (
    <div className="space-y-6">
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}
      <Form {...form}>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-8">
          {/* Basic Information Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-medium leading-6">Basic Information</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter username" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="Enter email address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Full Name Field */}
          <FormField
            control={control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter full name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!isEditing && (
            <div className="space-y-4">
              <h3 className="text-foreground text-lg font-medium leading-6">Security</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter password"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOffIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                      {passwordValue && (
                        <PasswordStrengthIndicator password={passwordValue} className="mt-2" />
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showConfirmPassword ? 'text' : 'password'}
                            placeholder="Confirm password"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? (
                              <EyeOffIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          {/* Assignment & Status Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-medium leading-6">Assignment & Status</h3>

            {restaurants.length > 0 && (
              <FormField
                control={control}
                name="restaurantId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Restaurant (Optional)</FormLabel>
                    <Select
                      onValueChange={value => field.onChange(value ? parseInt(value) : undefined)}
                      value={field.value?.toString() || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a restaurant" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No restaurant assigned</SelectItem>
                        {restaurants.map((restaurant, index) => (
                          <SelectItem
                            key={restaurant.id || `restaurant-${index}`}
                            value={restaurant.id?.toString() || ''}
                          >
                            {restaurant.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Assign user to a specific restaurant for role-based access
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <FormDescription>Inactive users cannot log in to the system</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Primary Action Section - Progressive Disclosure */}
          <div className="flex flex-col-reverse gap-3 pt-6 sm:flex-row sm:justify-end sm:gap-2">
            <Button type="button" variant="outline" onClick={onClose} className="sm:order-1">
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-primary text-primary-foreground hover:bg-primary/90 sm:order-2"
            >
              {loading && <LoadingSpinner size="sm" className="mr-2" />}
              {isEditing ? 'Update User' : 'Create User'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default UserForm;
