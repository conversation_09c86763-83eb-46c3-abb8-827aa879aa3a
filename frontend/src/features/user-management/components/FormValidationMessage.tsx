import React from 'react';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ValidationMessageProps {
  type: 'error' | 'warning' | 'success' | 'info';
  message: string;
  className?: string;
}

interface ValidationSummaryProps {
  errors: Record<string, string>;
  warnings?: Record<string, string>;
  className?: string;
}

interface FieldValidationProps {
  error?: string;
  warning?: string;
  success?: string;
  isValidating?: boolean;
  className?: string;
}

export function ValidationMessage({ type, message, className }: ValidationMessageProps) {
  const getIcon = () => {
    switch (type) {
      case 'error':
        return <XCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
    }
  };

  const _getVariant = () => {
    switch (type) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'success':
        return 'default';
      case 'info':
        return 'default';
    }
  };

  const getTextColor = () => {
    switch (type) {
      case 'error':
        return 'text-destructive';
      case 'warning':
        return 'text-yellow-600';
      case 'success':
        return 'text-green-600';
      case 'info':
        return 'text-blue-600';
    }
  };

  return (
    <div className={cn('flex items-center gap-2 text-sm', getTextColor(), className)}>
      {getIcon()}
      <span>{message}</span>
    </div>
  );
}

export function ValidationSummary({ errors, warnings = {}, className }: ValidationSummaryProps) {
  const errorCount = Object.keys(errors).length;
  const warningCount = Object.keys(warnings).length;

  if (errorCount === 0 && warningCount === 0) {
    return null;
  }

  return (
    <Alert variant={errorCount > 0 ? 'destructive' : 'default'} className={className}>
      <AlertDescription>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {errorCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {errorCount} error{errorCount > 1 ? 's' : ''}
              </Badge>
            )}
            {warningCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {warningCount} warning{warningCount > 1 ? 's' : ''}
              </Badge>
            )}
          </div>

          {errorCount > 0 && (
            <div className="space-y-1">
              <p className="text-sm font-medium">Errors:</p>
              <ul className="list-inside list-disc space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field} className="text-sm">
                    <span className="font-medium capitalize">{field}:</span> {error}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {warningCount > 0 && (
            <div className="space-y-1">
              <p className="text-sm font-medium">Warnings:</p>
              <ul className="list-inside list-disc space-y-1">
                {Object.entries(warnings).map(([field, warning]) => (
                  <li key={field} className="text-sm">
                    <span className="font-medium capitalize">{field}:</span> {warning}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

export function FieldValidation({
  error,
  warning,
  success,
  isValidating,
  className,
}: FieldValidationProps) {
  if (isValidating) {
    return (
      <div className={cn('text-muted-foreground flex items-center gap-2 text-sm', className)}>
        <div className="border-t-primary h-4 w-4 animate-spin rounded-full border-2 border-gray-300" />
        <span>Validating...</span>
      </div>
    );
  }

  if (error) {
    return <ValidationMessage type="error" message={error} className={className} />;
  }

  if (warning) {
    return <ValidationMessage type="warning" message={warning} className={className} />;
  }

  if (success) {
    return <ValidationMessage type="success" message={success} className={className} />;
  }

  return null;
}

// Real-time validation indicator for form fields
export function ValidationIndicator({
  isValid,
  isValidating,
  hasValue,
}: {
  isValid?: boolean;
  isValidating?: boolean;
  hasValue?: boolean;
}) {
  if (isValidating) {
    return (
      <div className="absolute right-3 top-1/2 -translate-y-1/2">
        <div className="border-t-primary h-4 w-4 animate-spin rounded-full border-2 border-gray-300" />
      </div>
    );
  }

  if (hasValue && isValid !== undefined) {
    return (
      <div className="absolute right-3 top-1/2 -translate-y-1/2">
        {isValid ? (
          <CheckCircle className="h-4 w-4 text-green-600" />
        ) : (
          <XCircle className="h-4 w-4 text-red-600" />
        )}
      </div>
    );
  }

  return null;
}

export default ValidationMessage;
