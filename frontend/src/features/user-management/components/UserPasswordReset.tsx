import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Key, Mail, AlertTriangle, Eye, EyeOff } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/components/ui/alert-dialog';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Separator } from '@/shared/components/ui/separator';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/components/ui/form';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { LoadingSpinner } from '@/shared/components/ui/loading-spinner';
import { User } from '../types';
import { passwordResetFormSchema, PasswordResetFormData } from '../validations/userSchemas';
import { useResetPassword } from '../hooks/useUserQueries';

interface UserPasswordResetProps {
  user: User | null;
  open: boolean;
  onConfirm: (options?: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

const UserPasswordReset: React.FC<UserPasswordResetProps> = ({
  user,
  open,
  onConfirm,
  onCancel,
  loading: externalLoading = false,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const resetPasswordMutation = useResetPassword();

  const form = useForm<PasswordResetFormData>({
    resolver: zodResolver(passwordResetFormSchema),
    defaultValues: {
      email: '',
      resetMethod: 'auto',
      newPassword: '',
      confirmPassword: '',
      temporaryPassword: true,
      sendEmail: true,
    },
    mode: 'onBlur',
  });

  const { handleSubmit, reset, control, watch, setValue } = form;
  const resetMethod = watch('resetMethod');

  const generateRandomPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setValue('newPassword', password);
    setValue('confirmPassword', password);
  };

  const onFormSubmit = async (data: PasswordResetFormData) => {
    try {
      setSubmitError(null);

      if (!user) return;

      const options = {
        temporaryPassword: data.temporaryPassword,
        sendEmail: data.sendEmail,
        ...(data.resetMethod === 'manual' && { newPassword: data.newPassword }),
      };

      // Use the mutation if available, otherwise fall back to the prop callback
      if (resetPasswordMutation) {
        await resetPasswordMutation.mutateAsync({
          userId: user.userId,
          options,
        });
      } else {
        onConfirm(options);
      }
    } catch (error: any) {
      console.error('Password reset error:', error);
      const errorMessage =
        error?.response?.data?.message || error?.message || 'Failed to reset password';
      setSubmitError(errorMessage);
    }
  };

  const handleReset = () => {
    setShowPassword(false);
    setSubmitError(null);
    reset({
      email: user?.email || '',
      resetMethod: 'auto',
      newPassword: '',
      confirmPassword: '',
      temporaryPassword: true,
      sendEmail: true,
    });
  };

  useEffect(() => {
    if (open && user) {
      handleReset();
    }
  }, [open, user?.userId, reset]);

  if (!user) return null;

  const isLoading = externalLoading || resetPasswordMutation?.isPending;

  return (
    <AlertDialog open={open} onOpenChange={isOpen => !isOpen && onCancel()}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Reset Password
          </AlertDialogTitle>
          <AlertDialogDescription>
            Reset password for <strong>{user.fullName || user.username}</strong>
          </AlertDialogDescription>
        </AlertDialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
            {/* Email Field */}
            <FormField
              control={control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter email address"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reset Method Selection */}
            <FormField
              control={control}
              name="resetMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">Reset Method</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="auto"
                          name="resetMethod"
                          value="auto"
                          checked={field.value === 'auto'}
                          onChange={() => field.onChange('auto')}
                          className="h-4 w-4"
                          disabled={isLoading}
                          aria-labelledby="auto-label"
                          title="Generate temporary password automatically"
                        />
                        <Label htmlFor="auto" id="auto-label" className="text-sm">
                          Generate temporary password automatically
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="manual"
                          name="resetMethod"
                          value="manual"
                          checked={field.value === 'manual'}
                          onChange={() => field.onChange('manual')}
                          className="h-4 w-4"
                          disabled={isLoading}
                          aria-labelledby="manual-label"
                          title="Set custom password"
                        />
                        <Label htmlFor="manual" id="manual-label" className="text-sm">
                          Set custom password
                        </Label>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Manual Password Input */}
            {resetMethod === 'manual' && (
              <div className="space-y-3">
                <Separator />

                <FormField
                  control={control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter new password"
                            disabled={isLoading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Confirm new password"
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateRandomPassword}
                  className="w-full"
                  disabled={isLoading}
                >
                  Generate Random Password
                </Button>
              </div>
            )}

            {/* Options */}
            <div className="space-y-3">
              <Separator />

              <div className="space-y-2">
                <FormField
                  control={control}
                  name="sendEmail"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormLabel className="flex items-center gap-2 text-sm font-normal">
                        <Mail className="h-4 w-4" />
                        Send password reset email to user
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="temporaryPassword"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Require password change on next login
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Warning */}
            <div className="flex items-start gap-2 rounded-md border border-yellow-200 bg-yellow-50 p-3">
              <AlertTriangle className="mt-0.5 h-4 w-4 text-yellow-600" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Security Notice</p>
                <p>
                  The user will need to log in with the new password. If sending via email, ensure
                  the email address is correct.
                </p>
              </div>
            </div>
            <AlertDialogFooter>
              <Button variant="outline" onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoadingSpinner className="mr-2 h-4 w-4" />}
                Reset Password
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default UserPasswordReset;
