import React from 'react';
import { AlertTriangle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/components/ui/alert-dialog';
import { User } from '../types';

interface UserDeleteDialogProps {
  user: User | null;
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const UserDeleteDialog: React.FC<UserDeleteDialogProps> = ({ user, open, onConfirm, onCancel }) => {
  if (!user) return null;

  return (
    <AlertDialog open={open} onOpenChange={isOpen => !isOpen && onCancel()}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="text-destructive h-5 w-5" />
            Delete User
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              Are you sure you want to delete <strong>{user.fullName || user.username}</strong>?
            </p>
            <p className="text-muted-foreground text-sm">
              This action cannot be undone. The user will be permanently removed from the system,
              and all associated data will be deleted.
            </p>
            <div className="bg-muted rounded-md p-3">
              <p className="text-sm font-medium">User Details:</p>
              <ul className="text-muted-foreground mt-1 space-y-1 text-sm">
                <li>Email: {user.email}</li>
                <li>Status: {user.isActive ? 'Active' : 'Inactive'}</li>
              </ul>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            Delete User
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default UserDeleteDialog;
