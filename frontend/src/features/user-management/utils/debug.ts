/**
 * Debug utilities for user management feature
 * Provides comprehensive logging and diagnostics for troubleshooting
 */

interface DebugContext {
  component: string;
  action: string;
  data?: any;
  timestamp: Date;
}

interface DebugConfig {
  enabled: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  includeStackTrace: boolean;
  persistLogs: boolean;
}

class UserManagementDebugger {
  private config: DebugConfig;
  private logs: Array<DebugContext & { level: string; message: string }> = [];

  constructor() {
    this.config = {
      enabled: import.meta.env.DEV || localStorage.getItem('debug-user-management') === 'true',
      logLevel: (localStorage.getItem('debug-level') as any) || 'info',
      includeStackTrace: true,
      persistLogs: true,
    };
  }

  private shouldLog(level: string): boolean {
    if (!this.config.enabled) return false;

    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex <= currentLevelIndex;
  }

  private formatMessage(context: DebugContext, level: string, message: string): string {
    const timestamp = context.timestamp.toISOString();
    return `[${timestamp}] [${level.toUpperCase()}] [${context.component}] ${context.action}: ${message}`;
  }

  private log(level: string, context: DebugContext, message: string, ...args: any[]): void {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(context, level, message);

    // Store log entry
    if (this.config.persistLogs) {
      this.logs.push({ ...context, level, message: formattedMessage });

      // Keep only last 1000 logs
      if (this.logs.length > 1000) {
        this.logs = this.logs.slice(-1000);
      }
    }

    // Console output with appropriate method
    const consoleMethod =
      level === 'error' ? 'error' : level === 'warn' ? 'warn' : level === 'debug' ? 'debug' : 'log';

    console[consoleMethod](formattedMessage, context.data, ...args);

    if (this.config.includeStackTrace && (level === 'error' || level === 'warn')) {
      console.trace('Stack trace:');
    }
  }

  // Public logging methods
  error(component: string, action: string, message: string, data?: any, ...args: any[]): void {
    this.log('error', { component, action, data, timestamp: new Date() }, message, ...args);
  }

  warn(component: string, action: string, message: string, data?: any, ...args: any[]): void {
    this.log('warn', { component, action, data, timestamp: new Date() }, message, ...args);
  }

  info(component: string, action: string, message: string, data?: any, ...args: any[]): void {
    this.log('info', { component, action, data, timestamp: new Date() }, message, ...args);
  }

  debug(component: string, action: string, message: string, data?: any, ...args: any[]): void {
    this.log('debug', { component, action, data, timestamp: new Date() }, message, ...args);
  }

  // Specialized methods for common scenarios
  buttonClick(component: string, buttonName: string, user?: any, error?: Error): void {
    if (error) {
      this.error(component, 'button_click', `Button "${buttonName}" failed`, {
        user,
        error: error.message,
      });
    } else {
      this.debug(component, 'button_click', `Button "${buttonName}" clicked`, { user });
    }
  }

  apiCall(
    component: string,
    endpoint: string,
    method: string,
    data?: any,
    response?: any,
    error?: Error
  ): void {
    if (error) {
      this.error(component, 'api_call', `${method} ${endpoint} failed: ${error.message}`, {
        data,
        error,
      });
    } else {
      this.info(component, 'api_call', `${method} ${endpoint} success`, { data, response });
    }
  }

  stateChange(component: string, stateName: string, oldValue: any, newValue: any): void {
    this.debug(component, 'state_change', `${stateName} changed`, { oldValue, newValue });
  }

  hookExecution(
    component: string,
    hookName: string,
    params?: any,
    result?: any,
    error?: Error
  ): void {
    if (error) {
      this.error(component, 'hook_execution', `Hook ${hookName} failed: ${error.message}`, {
        params,
        error,
      });
    } else {
      this.debug(component, 'hook_execution', `Hook ${hookName} executed`, { params, result });
    }
  }

  // Utility methods
  getLogs(): Array<DebugContext & { level: string; message: string }> {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
    console.clear();
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  setConfig(config: Partial<DebugConfig>): void {
    this.config = { ...this.config, ...config };

    // Persist debug settings
    if (config.enabled !== undefined) {
      localStorage.setItem('debug-user-management', config.enabled.toString());
    }
    if (config.logLevel) {
      localStorage.setItem('debug-level', config.logLevel);
    }
  }

  // Performance monitoring
  time(label: string): void {
    if (this.config.enabled) {
      console.time(`[UserManagement] ${label}`);
    }
  }

  timeEnd(label: string): void {
    if (this.config.enabled) {
      console.timeEnd(`[UserManagement] ${label}`);
    }
  }

  // Network diagnostics
  async checkConnectivity(): Promise<{
    online: boolean;
    apiReachable: boolean;
    latency?: number;
    error?: string;
  }> {
    const result = {
      online: navigator.onLine,
      apiReachable: false,
      latency: undefined as number | undefined,
      error: undefined as string | undefined,
    };

    if (!result.online) {
      result.error = 'Browser reports offline';
      return result;
    }

    try {
      const startTime = performance.now();
      const response = await fetch('/api/v1/health', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      const endTime = performance.now();

      result.latency = endTime - startTime;
      result.apiReachable = response.ok;

      if (!response.ok) {
        result.error = `API returned ${response.status} ${response.statusText}`;
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error';
    }

    this.info('Debugger', 'connectivity_check', 'Connectivity check completed', result);
    return result;
  }

  // Component health check
  checkComponentHealth(
    component: string,
    props: any,
    state?: any
  ): {
    healthy: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check for required props
    if (!props) {
      issues.push('Props object is null or undefined');
    }

    // Check for common prop issues
    if (props && typeof props === 'object') {
      Object.entries(props).forEach(([key, value]) => {
        if (value === undefined) {
          issues.push(`Prop "${key}" is undefined`);
        }
        if (typeof value === 'function' && value.toString().includes('[native code]')) {
          // Skip native functions
          return;
        }
        if (typeof value === 'function' && value.length === 0 && key.startsWith('on')) {
          issues.push(`Event handler "${key}" might be missing parameters`);
        }
      });
    }

    const healthy = issues.length === 0;

    this.debug(
      component,
      'health_check',
      `Component health: ${healthy ? 'healthy' : 'issues found'}`,
      {
        healthy,
        issues,
        props,
        state,
      }
    );

    return { healthy, issues };
  }
}

// Create singleton instance
export const userManagementDebugger = new UserManagementDebugger();

// Convenience functions for common use cases
export const logButtonClick = (component: string, buttonName: string, user?: any, error?: Error) =>
  userManagementDebugger.buttonClick(component, buttonName, user, error);

export const logApiCall = (
  component: string,
  endpoint: string,
  method: string,
  data?: any,
  response?: any,
  error?: Error
) => userManagementDebugger.apiCall(component, endpoint, method, data, response, error);

export const logStateChange = (
  component: string,
  stateName: string,
  oldValue: any,
  newValue: any
) => userManagementDebugger.stateChange(component, stateName, oldValue, newValue);

export const logHookExecution = (
  component: string,
  hookName: string,
  params?: any,
  result?: any,
  error?: Error
) => userManagementDebugger.hookExecution(component, hookName, params, result, error);

// Global debug utilities for browser console
if (typeof window !== 'undefined') {
  (window as any).userManagementDebug = {
    debugger: userManagementDebugger,
    getLogs: () => userManagementDebugger.getLogs(),
    clearLogs: () => userManagementDebugger.clearLogs(),
    exportLogs: () => userManagementDebugger.exportLogs(),
    checkConnectivity: () => userManagementDebugger.checkConnectivity(),
    enable: () => userManagementDebugger.setConfig({ enabled: true }),
    disable: () => userManagementDebugger.setConfig({ enabled: false }),
    setLevel: (level: 'error' | 'warn' | 'info' | 'debug') =>
      userManagementDebugger.setConfig({ logLevel: level }),
  };
}
