export type RiskLevel = 'low' | 'medium' | 'high';
export type TrendDirection = 'up' | 'down' | 'neutral';
export type SortDirection = 'ascending' | 'descending';
export type TargetSortOrder = 'asc' | 'desc';

export type CurrencyFormatter = (value: number) => string;
export type ColorClassGetter = (value: number) => string;
export type ProgressColorGetter = (percentage: number) => string;
export interface RestaurantPerformanceData {
  id: string;
  restaurant: string;
  actual: number;
  target: number;
  forecast: number;
  risk: 'low' | 'medium' | 'high';
  achievement: number;
  trend: 'up' | 'down' | 'neutral';
  q3Adjusted?: number; // Optional Q3 adjustment field
}

// Alias for compatibility with existing code
export type RestaurantDataItem = RestaurantPerformanceData;

export type RestaurantSortKeys = keyof RestaurantPerformanceData;

export interface RestaurantSortConfig {
  key: RestaurantSortKeys;
  direction: 'ascending' | 'descending';
}

export interface ChartDataItem {
  month: string;
  forecast: number;
  actual: number;
}

export interface RevenueOverview {
  actualRevenue: number;
  forecastRevenue: number;
  achievementPercentage: number;
}

export interface OverallRevenueData {
  overview: RevenueOverview;
  chartData: ChartDataItem[];
  performanceData: RestaurantPerformanceData[];
}

export interface OverallRevenueProps {
  className?: string;
  refreshInterval?: number;
  enablePolling?: boolean;
}

export type RestaurantViewMode = 'table' | 'targets' | 'combined';

export interface TargetDataItem {
  restaurant: string;
  percentage: number;
  trend: 'up' | 'down' | 'neutral';
}

export interface RestaurantPerformanceCardProps {
  restaurantData?: RestaurantPerformanceData[];
  targetData?: TargetDataItem[];
  onRefresh?: () => void;
  isRefreshing?: boolean;
  className?: string;
  enableSearch?: boolean;
  enableSorting?: boolean;
  maxHeight?: string;
  defaultView?: RestaurantViewMode;
  allowViewToggle?: boolean;
}

export interface RestaurantPerformanceState {
  sortConfig: RestaurantSortConfig;
  searchQuery: string;
  viewMode: RestaurantViewMode;
}

// Additional missing types
export interface ProgressBarProps {
  value: number;
  className?: string;
  'aria-label'?: string;
}

export interface TrendIconProps {
  trend: TrendDirection;
  className?: string;
  'aria-label'?: string;
}

export interface SortIconProps {
  direction: SortDirection;
  className?: string;
}

export interface RiskBadgeProps {
  risk: RiskLevel;
  className?: string;
}

export interface RestaurantTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export interface TargetFilters {
  searchQuery: string;
  sortOrder: TargetSortOrder;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, unknown>;
}

export interface LoadingStates {
  isLoading: boolean;
  isRefreshing: boolean;
  isError: boolean;
}

export interface OverallRevenueState {
  data: OverallRevenueData | null;
  isLoading: boolean;
  error: ApiError | null;
  lastUpdated: Date | null;
}

export interface UseOverallRevenueReturn {
  data: OverallRevenueData | undefined;
  isLoading: boolean;
  error: ApiError | null;
  refetch: () => void;
}

export interface UseRestaurantSortingReturn {
  sortConfig: RestaurantSortConfig;
  setSortConfig: (config: RestaurantSortConfig) => void;
  sortedData: RestaurantDataItem[];
  handleSort: (key: keyof RestaurantPerformanceData) => void;
}

export interface UseTargetFilteringReturn {
  filteredData: TargetDataItem[];
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortOrder: TargetSortOrder;
  setSortOrder: (order: TargetSortOrder) => void;
  toggleSortOrder: () => void;
}

export interface ChartConfig {
  margin: {
    top: number;
    right: number;
    left: number;
    bottom: number;
  };
  colors: {
    forecast: string;
    actual: string;
  };
}

export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

export interface EnhancedRestaurantData extends RestaurantDataItem {
  achievementRank?: number;
}

export interface OverallRevenueParams {
  period: string;
  includeForecasts: boolean;
}
