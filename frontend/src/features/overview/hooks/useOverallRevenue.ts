import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { overallRevenueApi } from '../services/overallRevenueApi';
import { DEFAULT_QUERY_PARAMS } from '../utils/constants';
import {
  type OverallRevenueData,
  type OverallRevenueParams,
} from '../validations/overallRevenueSchemas';

const STALE_TIME = 5 * 60 * 1000; // 5 minutes
const RETRY_COUNT = 3;

/**
 * Hook for fetching overall revenue data following established codebase patterns
 */
export function useOverallRevenue(
  params: Partial<OverallRevenueParams> = {},
  options: Partial<UseQueryOptions<OverallRevenueData, Error>> = {}
) {
  // Merge with default parameters
  const queryParams: OverallRevenueParams = {
    ...DEFAULT_QUERY_PARAMS,
    ...params,
  };

  return useQuery<OverallRevenueData, Error>({
    queryKey: [QUERY_KEYS.DASHBOARD, 'overall-revenue', queryParams],
    queryFn: () => overallRevenueApi.getOverallRevenue(queryParams),
    staleTime: STALE_TIME,
    retry: RETRY_COUNT,
    refetchOnWindowFocus: false,
    ...options,
  });
}

/**
 * Hook for getting overall revenue data with polling
 */
export function useOverallRevenueWithPolling(
  params: Partial<OverallRevenueParams> = {},
  pollingInterval: number = 30000
) {
  return useOverallRevenue(params, {
    refetchInterval: pollingInterval,
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook for getting overall revenue data without polling
 */
export function useOverallRevenueStatic(params: Partial<OverallRevenueParams> = {}) {
  return useOverallRevenue(params, {
    refetchInterval: false,
    refetchOnWindowFocus: false,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Legacy hook name for backward compatibility
 * @deprecated Use useOverallRevenue instead
 */
export const useGetOverallRevenue = useOverallRevenue;
