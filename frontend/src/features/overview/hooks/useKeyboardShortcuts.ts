import { useEffect, useCallback } from 'react';
import { KEYBOARD_SHORTCUTS } from '../utils/constants';

export interface UseKeyboardShortcutsOptions {
  onRefresh?: () => void;
  enabled?: boolean;
  preventDefault?: boolean;
}

/**
 * Hook for managing keyboard shortcuts following existing patterns in the codebase
 * Supports Ctrl+R and F5 for refresh functionality
 */
export const useKeyboardShortcuts = ({
  onRefresh,
  enabled = true,
  preventDefault = true,
}: UseKeyboardShortcutsOptions = {}) => {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      // Handle Ctrl+R or Cmd+R
      if (
        event.key.toLowerCase() === KEYBOARD_SHORTCUTS.REFRESH &&
        (event.ctrlKey || event.metaKey)
      ) {
        if (preventDefault) {
          event.preventDefault();
        }
        onRefresh?.();
        return;
      }

      // Handle F5
      if (event.key === KEYBOARD_SHORTCUTS.REFRESH_ALT) {
        if (preventDefault) {
          event.preventDefault();
        }
        onRefresh?.();
        return;
      }
    },
    [enabled, preventDefault, onRefresh]
  );

  useEffect(() => {
    if (!enabled) return;

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown, enabled]);

  return {
    handleKeyDown,
  };
};

/**
 * Hook for managing accessibility announcements for keyboard shortcuts
 */
export const useKeyboardShortcutAnnouncements = () => {
  const announceShortcut = useCallback((shortcut: string, action: string) => {
    // Create a temporary element for screen reader announcements
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = `Keyboard shortcut ${shortcut} triggered: ${action}`;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  const announceRefreshShortcut = useCallback(() => {
    announceShortcut('Ctrl+R or F5', 'Data refresh initiated');
  }, [announceShortcut]);

  return {
    announceShortcut,
    announceRefreshShortcut,
  };
};

/**
 * Combined hook that provides keyboard shortcuts with accessibility announcements
 */
export const useAccessibleKeyboardShortcuts = (options: UseKeyboardShortcutsOptions = {}) => {
  const { announceRefreshShortcut } = useKeyboardShortcutAnnouncements();

  const enhancedOptions = {
    ...options,
    onRefresh: useCallback(() => {
      announceRefreshShortcut();
      options.onRefresh?.();
    }, [options.onRefresh, announceRefreshShortcut]),
  };

  return useKeyboardShortcuts(enhancedOptions);
};
