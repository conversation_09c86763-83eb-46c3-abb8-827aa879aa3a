import { useState, useEffect, useCallback } from 'react';
import { formatLastUpdated } from '../utils/formatters';

/**
 * Custom hook for managing last updated timestamp
 */
export const useLastUpdated = (data?: unknown) => {
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Update timestamp when data changes
  useEffect(() => {
    if (data) {
      setLastUpdated(new Date());
    }
  }, [data]);

  // Manual update function
  const updateTimestamp = useCallback(() => {
    setLastUpdated(new Date());
  }, []);

  // Formatted timestamp
  const formattedLastUpdated = formatLastUpdated(lastUpdated);

  return {
    lastUpdated,
    formattedLastUpdated,
    updateTimestamp,
  };
};

/**
 * Hook for auto-updating timestamp display
 */
export const useAutoUpdatingTimestamp = (
  data?: unknown,
  updateInterval: number = 1000 // Update every second
) => {
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  // Update timestamp when data changes
  useEffect(() => {
    if (data) {
      setLastUpdated(new Date());
    }
  }, [data]);

  // Update current time periodically for relative time display
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, updateInterval);

    return () => clearInterval(interval);
  }, [updateInterval]);

  // Calculate time ago
  const getTimeAgo = useCallback(() => {
    const diffMs = currentTime.getTime() - lastUpdated.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffSeconds < 60) {
      return `${diffSeconds} seconds ago`;
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    } else {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    }
  }, [currentTime, lastUpdated]);

  return {
    lastUpdated,
    formattedLastUpdated: formatLastUpdated(lastUpdated),
    timeAgo: getTimeAgo(),
    updateTimestamp: () => setLastUpdated(new Date()),
  };
};
