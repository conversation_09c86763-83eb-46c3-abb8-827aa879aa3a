import { useMemo, useCallback, useRef } from 'react';
import type {
  RestaurantDataItem,
  TargetDataItem,
  RestaurantSortKeys,
  SortDirection,
  TargetSortOrder,
} from '../types';

/**
 * Hook for memoizing expensive calculations and event handlers
 */
export const usePerformanceOptimizations = () => {
  // Stable references for callback dependencies
  const stableRefs = useRef({
    lastSortKey: null as RestaurantSortKeys | null,
    lastSortDirection: null as SortDirection | null,
  });

  // Memoized currency formatter
  const formatCurrency = useCallback((value: number): string => {
    if (value >= 0) {
      return `$${value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
    return `-$${Math.abs(value).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
  }, []);

  // Memoized color class getter
  const getAdjustmentColor = useCallback((value: number): string => {
    if (value < 0) return 'text-red-500';
    if (value > 0) return 'text-green-500';
    return 'text-slate-400';
  }, []);

  // Memoized progress color class getter
  const getProgressColorClass = useCallback((percentage: number): string => {
    if (percentage < 75) return 'bg-red-500';
    if (percentage < 90) return 'bg-amber-500';
    return 'bg-green-500';
  }, []);

  // Optimized restaurant data sorter
  const sortRestaurantData = useCallback(
    (
      data: RestaurantDataItem[],
      key: RestaurantSortKeys,
      direction: SortDirection
    ): RestaurantDataItem[] => {
      // Early return for empty data
      if (!data || data.length === 0) return [];

      // Check if we can reuse previous sort result
      const refs = stableRefs.current;
      if (refs.lastSortKey === key && refs.lastSortDirection === direction) {
        // Return the same reference if sorting hasn't changed
        return data;
      }

      // Update refs
      refs.lastSortKey = key;
      refs.lastSortDirection = direction;

      // Perform sort
      const sortableItems = [...data];
      const riskOrder = { low: 1, medium: 2, high: 3 };

      sortableItems.sort((a, b) => {
        const aValue = a[key];
        const bValue = b[key];
        let comparison = 0;

        if (key === 'risk') {
          comparison =
            riskOrder[aValue as keyof typeof riskOrder] -
            riskOrder[bValue as keyof typeof riskOrder];
        } else if (typeof aValue === 'string' && typeof bValue === 'string') {
          comparison = aValue.localeCompare(bValue, 'en', {
            numeric: true,
            sensitivity: 'base',
          });
        } else if (typeof aValue === 'number' && typeof bValue === 'number') {
          comparison = aValue - bValue;
        }

        return direction === 'descending' ? comparison * -1 : comparison;
      });

      return sortableItems;
    },
    []
  );

  // Optimized target data filter and sorter
  const filterAndSortTargetData = useCallback(
    (data: TargetDataItem[], searchQuery: string, sortOrder: TargetSortOrder): TargetDataItem[] => {
      if (!data || data.length === 0) return [];

      let result = data;

      // Apply search filter if query exists
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase().trim();
        result = result.filter(item => item.restaurant.toLowerCase().includes(query));
      }

      // Apply sort
      if (result.length > 1) {
        result = [...result].sort((a, b) => {
          const comparison = a.percentage - b.percentage;
          return sortOrder === 'asc' ? comparison : comparison * -1;
        });
      }

      return result;
    },
    []
  );

  // Memoized chart data processor
  const processChartData = useCallback((chartData: any[]) => {
    if (!chartData || chartData.length === 0) return [];

    return chartData.map(item => ({
      ...item,
      // Pre-calculate any derived values here if needed
    }));
  }, []);

  // Debounced search handler
  const createDebouncedHandler = useCallback(
    (handler: (value: string) => void, delay: number = 300) => {
      let timeoutId: NodeJS.Timeout;

      return (value: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => handler(value), delay);
      };
    },
    []
  );

  // Memoized event handlers factory
  const createEventHandlers = useCallback(
    (
      onSort: (key: RestaurantSortKeys) => void,
      onSearch: (query: string) => void,
      onToggleSort: () => void,
      onRefresh: () => void
    ) => {
      return {
        handleSort: (key: RestaurantSortKeys) => onSort(key),
        handleSearch: createDebouncedHandler(onSearch),
        handleToggleSort: onToggleSort,
        handleRefresh: onRefresh,
      };
    },
    [createDebouncedHandler]
  );

  return {
    formatCurrency,
    getAdjustmentColor,
    getProgressColorClass,
    sortRestaurantData,
    filterAndSortTargetData,
    processChartData,
    createDebouncedHandler,
    createEventHandlers,
  };
};

/**
 * Hook for memoizing component props to prevent unnecessary re-renders
 */
export const useMemoizedProps = <T extends Record<string, any>>(props: T): T => {
  return useMemo(
    () => props,
    // Create dependency array from prop values
    Object.values(props)
  );
};

/**
 * Hook for creating stable callback references
 */
export const useStableCallbacks = <T extends Record<string, (...args: any[]) => any>>(
  callbacks: T
): T => {
  const stableCallbacks = useMemo(() => {
    const result = {} as T;

    for (const [key, callback] of Object.entries(callbacks)) {
      result[key as keyof T] = useCallback(callback, [callback]) as T[keyof T];
    }

    return result;
  }, [callbacks]);

  return stableCallbacks;
};

/**
 * Hook for optimizing list rendering with virtualization support
 */
export const useOptimizedList = <T>(
  items: T[],
  keyExtractor: (item: T, index: number) => string,
  maxVisibleItems: number = 50
) => {
  // Memoize the key extraction
  const itemKeys = useMemo(() => items.map(keyExtractor), [items, keyExtractor]);

  // Slice items for performance if list is too long
  const visibleItems = useMemo(() => {
    if (items.length <= maxVisibleItems) return items;
    return items.slice(0, maxVisibleItems);
  }, [items, maxVisibleItems]);

  // Memoize whether we're showing all items
  const isShowingAll = useMemo(
    () => items.length <= maxVisibleItems,
    [items.length, maxVisibleItems]
  );

  return {
    visibleItems,
    itemKeys: itemKeys.slice(0, maxVisibleItems),
    isShowingAll,
    totalItems: items.length,
    hiddenItems: Math.max(0, items.length - maxVisibleItems),
  };
};
