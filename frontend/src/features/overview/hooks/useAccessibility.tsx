import { useCallback, useRef } from 'react';
import type { RestaurantDataItem, TargetDataItem, RiskLevel, TrendDirection } from '../types';

/**
 * Hook for managing accessibility features and ARIA attributes
 */
export const useAccessibility = () => {
  // Refs for managing focus
  const announcementRef = useRef<HTMLDivElement>(null);
  const lastAnnouncementRef = useRef<string>('');

  // Announce changes to screen readers
  const announceToScreenReader = useCallback((message: string) => {
    if (message === lastAnnouncementRef.current) return;

    lastAnnouncementRef.current = message;

    if (announcementRef.current) {
      announcementRef.current.textContent = message;
    }
  }, []);

  // Generate accessible labels for restaurant data
  const getRestaurantRowLabel = useCallback((item: RestaurantDataItem): string => {
    const riskText = item.risk === 'low' ? 'low risk' : `${item.risk} risk`;
    return `${item.restaurant}, actual revenue ${item.actual} dollars, forecast ${item.forecast} dollars, ${riskText}`;
  }, []);

  // Generate accessible labels for target data
  const getTargetRowLabel = useCallback((item: TargetDataItem): string => {
    const trendText =
      item.trend === 'up'
        ? 'trending up'
        : item.trend === 'down'
          ? 'trending down'
          : 'neutral trend';
    return `${item.restaurant}, ${item.percentage}% of target achieved, ${trendText}`;
  }, []);

  // Generate accessible labels for sort buttons
  const getSortButtonLabel = useCallback(
    (columnName: string, isCurrentSort: boolean, direction: 'ascending' | 'descending'): string => {
      if (isCurrentSort) {
        return `Sort ${columnName}, currently sorted ${direction}. Click to sort ${direction === 'ascending' ? 'descending' : 'ascending'}.`;
      }
      return `Sort by ${columnName}`;
    },
    []
  );

  // Generate accessible labels for progress bars
  const getProgressBarLabel = useCallback((restaurant: string, percentage: number): string => {
    const status =
      percentage < 75 ? 'below target' : percentage < 90 ? 'approaching target' : 'meeting target';
    return `${restaurant} progress: ${percentage}% of target, ${status}`;
  }, []);

  // Generate accessible labels for risk indicators
  const getRiskIndicatorLabel = useCallback((risk: RiskLevel): string => {
    switch (risk) {
      case 'high':
        return 'High risk indicator, red dot';
      case 'medium':
        return 'Medium risk indicator, amber dot';
      case 'low':
        return 'Low risk, no indicator shown';
      default:
        return 'Unknown risk level';
    }
  }, []);

  // Generate accessible labels for trend indicators
  const getTrendIndicatorLabel = useCallback((trend: TrendDirection): string => {
    switch (trend) {
      case 'up':
        return 'Trending upward, green arrow pointing up';
      case 'down':
        return 'Trending downward, red arrow pointing down';
      case 'neutral':
        return 'Neutral trend, horizontal line';
      default:
        return 'Unknown trend direction';
    }
  }, []);

  // Handle keyboard navigation for sortable headers
  const handleSortKeyDown = useCallback(
    (event: React.KeyboardEvent, onSort: () => void, columnName: string) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        onSort();
        announceToScreenReader(`Sorted by ${columnName}`);
      }
    },
    [announceToScreenReader]
  );

  // Handle keyboard navigation for search
  const handleSearchKeyDown = useCallback(
    (event: React.KeyboardEvent, onSearch: (query: string) => void) => {
      if (event.key === 'Escape') {
        const target = event.target as HTMLInputElement;
        target.value = '';
        onSearch('');
        announceToScreenReader('Search cleared');
      }
    },
    [announceToScreenReader]
  );

  // Generate live region announcements for data updates
  const announceDataUpdate = useCallback(
    (type: 'refresh' | 'sort' | 'filter', details?: string) => {
      const messages = {
        refresh: 'Revenue data refreshed',
        sort: `Data sorted${details ? ` by ${details}` : ''}`,
        filter: `Data filtered${details ? `, ${details}` : ''}`,
      };

      announceToScreenReader(messages[type]);
    },
    [announceToScreenReader]
  );

  // Generate chart accessibility description
  const getChartDescription = useCallback(
    (chartData: Array<{ month: string; forecast: number; actual: number }>): string => {
      if (!chartData || chartData.length === 0) {
        return 'No chart data available';
      }

      const totalForecast = chartData.reduce((sum, item) => sum + item.forecast, 0);
      const totalActual = chartData.reduce((sum, item) => sum + item.actual, 0);
      const performance = totalActual >= totalForecast ? 'exceeding' : 'below';

      return (
        `Line chart showing ${chartData.length} months of revenue data. ` +
        `Total forecast: ${totalForecast} dollars. Total actual: ${totalActual} dollars. ` +
        `Performance is ${performance} forecast.`
      );
    },
    []
  );

  // Create screen reader announcement element
  const createAnnouncementElement = useCallback(() => {
    return (
      <div
        ref={announcementRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
        role="status"
      />
    );
  }, []);

  // Focus management utilities
  const focusManagement = {
    // Focus the first interactive element in a container
    focusFirst: useCallback((container: HTMLElement) => {
      const focusable = container.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement;
      focusable?.focus();
    }, []),

    // Focus the next/previous element in tab order
    focusNext: useCallback(
      (currentElement: HTMLElement, direction: 'next' | 'previous' = 'next') => {
        const focusableElements = Array.from(
          document.querySelectorAll(
            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
          )
        ) as HTMLElement[];

        const currentIndex = focusableElements.indexOf(currentElement);
        if (currentIndex === -1) return;

        const nextIndex =
          direction === 'next'
            ? (currentIndex + 1) % focusableElements.length
            : (currentIndex - 1 + focusableElements.length) % focusableElements.length;

        focusableElements[nextIndex]?.focus();
      },
      []
    ),
  };

  return {
    announceToScreenReader,
    getRestaurantRowLabel,
    getTargetRowLabel,
    getSortButtonLabel,
    getProgressBarLabel,
    getRiskIndicatorLabel,
    getTrendIndicatorLabel,
    handleSortKeyDown,
    handleSearchKeyDown,
    announceDataUpdate,
    getChartDescription,
    createAnnouncementElement,
    focusManagement,
  };
};

/**
 * Hook for managing keyboard navigation in tables
 */
export const useTableKeyboardNavigation = (rowCount: number, columnCount: number) => {
  const currentCellRef = useRef<{ row: number; col: number }>({ row: 0, col: 0 });

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, rowIndex: number, colIndex: number) => {
      const { row, col } = currentCellRef.current;

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault();
          if (row > 0) {
            currentCellRef.current = { row: row - 1, col };
            // Focus logic would go here
          }
          break;
        case 'ArrowDown':
          event.preventDefault();
          if (row < rowCount - 1) {
            currentCellRef.current = { row: row + 1, col };
            // Focus logic would go here
          }
          break;
        case 'ArrowLeft':
          event.preventDefault();
          if (col > 0) {
            currentCellRef.current = { row, col: col - 1 };
            // Focus logic would go here
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (col < columnCount - 1) {
            currentCellRef.current = { row, col: col + 1 };
            // Focus logic would go here
          }
          break;
        case 'Home':
          event.preventDefault();
          currentCellRef.current = { row, col: 0 };
          break;
        case 'End':
          event.preventDefault();
          currentCellRef.current = { row, col: columnCount - 1 };
          break;
      }
    },
    [rowCount, columnCount]
  );

  return {
    handleKeyDown,
    currentCell: currentCellRef.current,
  };
};
