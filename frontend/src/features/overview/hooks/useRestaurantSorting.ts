import { useState, useMemo, useCallback } from 'react';
import { sortRestaurantData, getNextSortDirection } from '../utils/sorting';
import type {
  RestaurantDataItem,
  RestaurantSortKeys,
  RestaurantSortConfig,
  UseRestaurantSortingReturn,
} from '../types';

/**
 * Custom hook for managing restaurant data sorting
 */
export const useRestaurantSorting = (
  data: RestaurantDataItem[] = [],
  initialSortConfig: RestaurantSortConfig = {
    key: 'restaurant',
    direction: 'ascending',
  }
): UseRestaurantSortingReturn => {
  const [sortConfig, setSortConfig] = useState<RestaurantSortConfig>(initialSortConfig);

  // Memoized sorted data
  const sortedData = useMemo(() => {
    return sortRestaurantData(data, sortConfig.key, sortConfig.direction);
  }, [data, sortConfig.key, sortConfig.direction]);

  // Handle sort column click
  const handleSort = useCallback((key: RestaurantSortKeys) => {
    setSortConfig(prevConfig => ({
      key,
      direction: getNextSortDirection(prevConfig.key, key, prevConfig.direction),
    }));
  }, []);

  return {
    sortedData,
    sortConfig,
    setSortConfig,
    handleSort,
  };
};

/**
 * Hook for restaurant sorting with persistence
 */
export const useRestaurantSortingWithPersistence = (
  data: RestaurantDataItem[] = [],
  storageKey: string = 'restaurant-sort-config'
): UseRestaurantSortingReturn => {
  // Load initial sort config from localStorage
  const getInitialSortConfig = (): RestaurantSortConfig => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsed = JSON.parse(stored) as RestaurantSortConfig;
        // Validate the stored config
        if (
          parsed.key &&
          ['restaurant', 'q3Adjusted', 'actual', 'risk'].includes(parsed.key) &&
          ['ascending', 'descending'].includes(parsed.direction)
        ) {
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load sort config from localStorage:', error);
    }

    return {
      key: 'restaurant',
      direction: 'ascending',
    };
  };

  const [sortConfig, setSortConfig] = useState<RestaurantSortConfig>(getInitialSortConfig);

  // Memoized sorted data
  const sortedData = useMemo(() => {
    return sortRestaurantData(data, sortConfig.key, sortConfig.direction);
  }, [data, sortConfig.key, sortConfig.direction]);

  // Handle sort column click with persistence
  const handleSort = useCallback(
    (key: RestaurantSortKeys) => {
      setSortConfig(prevConfig => {
        const newConfig = {
          key,
          direction: getNextSortDirection(prevConfig.key, key, prevConfig.direction),
        };

        // Save to localStorage
        try {
          localStorage.setItem(storageKey, JSON.stringify(newConfig));
        } catch (error) {
          console.warn('Failed to save sort config to localStorage:', error);
        }

        return newConfig;
      });
    },
    [storageKey]
  );

  return {
    sortedData,
    sortConfig,
    setSortConfig,
    handleSort,
  };
};
