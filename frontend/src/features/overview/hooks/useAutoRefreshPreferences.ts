import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from '@/shared/hooks/useLocalStorage';
import { REFRESH_INTERVALS, STORAGE_KEYS } from '../utils/constants';

export interface AutoRefreshPreferences {
  interval: number;
  enabled: boolean;
  lastUpdated: Date | null;
}

export interface UseAutoRefreshPreferencesReturn {
  preferences: AutoRefreshPreferences;
  setRefreshInterval: (interval: number) => void;
  toggleAutoRefresh: () => void;
  updateLastUpdated: () => void;
  getFormattedLastUpdated: () => string;
  isAutoRefreshEnabled: boolean;
  currentInterval: number;
}

/**
 * Hook for managing auto-refresh preferences with localStorage persistence
 * Follows existing patterns in the codebase for user preferences management
 */
export const useAutoRefreshPreferences = (): UseAutoRefreshPreferencesReturn => {
  // Use localStorage hook for persistence
  const [storedInterval, setStoredInterval] = useLocalStorage<number>(
    STORAGE_KEYS.REFRESH_INTERVAL,
    REFRESH_INTERVALS.DEFAULT
  );

  const [storedLastUpdated, setStoredLastUpdated] = useLocalStorage<string | null>(
    STORAGE_KEYS.LAST_UPDATED,
    null
  );

  // Local state for preferences
  const [preferences, setPreferences] = useState<AutoRefreshPreferences>(() => ({
    interval: storedInterval,
    enabled: storedInterval > 0,
    lastUpdated: storedLastUpdated ? new Date(storedLastUpdated) : null,
  }));

  // Update preferences when stored values change
  useEffect(() => {
    setPreferences(prev => ({
      ...prev,
      interval: storedInterval,
      enabled: storedInterval > 0,
    }));
  }, [storedInterval]);

  // Update last updated when stored value changes
  useEffect(() => {
    setPreferences(prev => ({
      ...prev,
      lastUpdated: storedLastUpdated ? new Date(storedLastUpdated) : null,
    }));
  }, [storedLastUpdated]);

  // Set refresh interval
  const setRefreshInterval = useCallback(
    (interval: number) => {
      setStoredInterval(interval);
      setPreferences(prev => ({
        ...prev,
        interval,
        enabled: interval > 0,
      }));
    },
    [setStoredInterval]
  );

  // Toggle auto-refresh on/off
  const toggleAutoRefresh = useCallback(() => {
    const newInterval = preferences.enabled
      ? REFRESH_INTERVALS.DISABLED
      : REFRESH_INTERVALS.DEFAULT;
    setRefreshInterval(newInterval);
  }, [preferences.enabled, setRefreshInterval]);

  // Update last updated timestamp
  const updateLastUpdated = useCallback(() => {
    const now = new Date();
    setStoredLastUpdated(now.toISOString());
    setPreferences(prev => ({
      ...prev,
      lastUpdated: now,
    }));
  }, [setStoredLastUpdated]);

  // Get formatted last updated string
  const getFormattedLastUpdated = useCallback((): string => {
    if (!preferences.lastUpdated) {
      return 'Never';
    }

    const now = new Date();
    const diffMs = now.getTime() - preferences.lastUpdated.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffSeconds < 60) {
      return diffSeconds <= 1 ? 'Just now' : `${diffSeconds} seconds ago`;
    } else if (diffMinutes < 60) {
      return diffMinutes === 1 ? '1 minute ago' : `${diffMinutes} minutes ago`;
    } else if (diffHours < 24) {
      return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
    } else {
      return preferences.lastUpdated.toLocaleDateString();
    }
  }, [preferences.lastUpdated]);

  return {
    preferences,
    setRefreshInterval,
    toggleAutoRefresh,
    updateLastUpdated,
    getFormattedLastUpdated,
    isAutoRefreshEnabled: preferences.enabled,
    currentInterval: preferences.interval,
  };
};

/**
 * Hook for auto-updating the formatted timestamp display
 * Updates every second to keep the "X seconds ago" display current
 */
export const useAutoUpdatingTimestamp = (
  getFormattedLastUpdated: () => string,
  updateInterval: number = 1000
) => {
  const [formattedTime, setFormattedTime] = useState(getFormattedLastUpdated);

  useEffect(() => {
    const interval = setInterval(() => {
      setFormattedTime(getFormattedLastUpdated());
    }, updateInterval);

    return () => clearInterval(interval);
  }, [getFormattedLastUpdated, updateInterval]);

  return formattedTime;
};
