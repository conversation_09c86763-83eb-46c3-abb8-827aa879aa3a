import { z } from 'zod';

// Base enum schemas
export const riskLevelSchema = z.enum(['low', 'medium', 'high']);
export const trendDirectionSchema = z.enum(['up', 'down', 'neutral']);
export const sortDirectionSchema = z.enum(['ascending', 'descending']);
export const targetSortOrderSchema = z.enum(['asc', 'desc']);
export const restaurantSortKeysSchema = z.enum([
  'restaurant',
  'actual',
  'target',
  'risk',
  'achievement',
  'forecast',
  'trend',
]);

// Unified performance data schema
export const performanceDataItemSchema = z.object({
  restaurant: z.string().min(1, 'Restaurant name is required'),
  forecast: z.number(),
  target: z.number(),
  actual: z.number(),
  risk: riskLevelSchema,
  achievement: z.number(),
  trend: trendDirectionSchema,
});

// Chart data schema
export const chartDataItemSchema = z.object({
  month: z.string().min(1, 'Month is required'),
  forecast: z.number(),
  actual: z.number(),
});

// Revenue overview schema
export const revenueOverviewSchema = z.object({
  actualRevenue: z.number(),
  forecastRevenue: z.number(),
  achievementPercentage: z
    .number()
    .min(0)
    .max(200, 'Achievement percentage must be between 0 and 200'),
});

// Complete API response schema
export const overallRevenueDataSchema = z.object({
  overview: revenueOverviewSchema,
  chartData: z.array(chartDataItemSchema),
  performanceData: z.array(performanceDataItemSchema),
});

// Query parameters schema
export const overallRevenueParamsSchema = z.object({
  period: z.string().min(1, 'Period is required'),
  includeForecasts: z.boolean(),
});

// Sorting configuration schema
export const restaurantSortConfigSchema = z.object({
  key: restaurantSortKeysSchema,
  direction: sortDirectionSchema,
});

// Component prop schemas
export const trendIconPropsSchema = z.object({
  trend: trendDirectionSchema,
  className: z.string().optional(),
  'aria-label': z.string().optional(),
});

export const sortIconPropsSchema = z.object({
  direction: sortDirectionSchema,
  className: z.string().optional(),
});

export const progressBarPropsSchema = z.object({
  value: z.number().min(0).max(100),
  className: z.string().optional(),
  'aria-label': z.string().optional(),
});

export const riskBadgePropsSchema = z.object({
  risk: riskLevelSchema,
  className: z.string().optional(),
});

// Filter schemas
export const targetFiltersSchema = z.object({
  searchQuery: z.string(),
  sortOrder: targetSortOrderSchema,
});

// Error schema
export const apiErrorSchema = z.object({
  message: z.string(),
  code: z.string().optional(),
  details: z.record(z.unknown()).optional(),
});

// Component state schema
export const overallRevenueStateSchema = z.object({
  performanceData: z.array(performanceDataItemSchema),
  lastUpdated: z.date(),
  searchQuery: z.string(),
  targetSortOrder: targetSortOrderSchema,
  restaurantSortConfig: restaurantSortConfigSchema,
});

// Chart configuration schema
export const chartConfigSchema = z.object({
  margin: z.object({
    top: z.number(),
    right: z.number(),
    left: z.number(),
    bottom: z.number(),
  }),
  colors: z.object({
    forecast: z.string(),
    actual: z.string(),
  }),
});

// Component props schema
export const overallRevenuePropsSchema = z.object({
  className: z.string().optional(),
  refreshInterval: z.number().min(1000).optional(),
  enablePolling: z.boolean().optional(),
});

// Restaurant view mode schema
export const restaurantViewModeSchema = z.enum(['table', 'targets', 'combined']);

// Enhanced restaurant data schema
export const enhancedRestaurantDataSchema = performanceDataItemSchema.extend({
  id: z.string(), // ID for React key prop
  achievementRank: z.number().min(1).optional(),
});

// Restaurant performance card props schema
export const restaurantPerformanceCardPropsSchema = z.object({
  performanceData: z.array(performanceDataItemSchema),
  onRefresh: z.function().optional(),
  isRefreshing: z.boolean().optional(),
  className: z.string().optional(),
  enableSearch: z.boolean().optional(),
  enableSorting: z.boolean().optional(),
  maxHeight: z.string().optional(),
  defaultView: restaurantViewModeSchema.optional(),
  allowViewToggle: z.boolean().optional(),
});

// Restaurant performance state schema
export const restaurantPerformanceStateSchema = z.object({
  currentView: restaurantViewModeSchema,
  searchQuery: z.string(),
  restaurantSortConfig: restaurantSortConfigSchema,
  targetSortOrder: targetSortOrderSchema,
  isSearchFocused: z.boolean(),
  expandedRows: z.set(z.string()),
});

// Alias schemas for compatibility
export const restaurantDataItemSchema = performanceDataItemSchema;
export const targetDataItemSchema = z.object({
  restaurant: z.string().min(1, 'Restaurant name is required'),
  percentage: z.number().min(0).max(100),
  trend: trendDirectionSchema,
});

// Type exports for runtime validation
export type PerformanceDataItem = z.infer<typeof performanceDataItemSchema>;
export type ChartDataItem = z.infer<typeof chartDataItemSchema>;
export type RevenueOverview = z.infer<typeof revenueOverviewSchema>;
export type OverallRevenueData = z.infer<typeof overallRevenueDataSchema>;
export type OverallRevenueParams = z.infer<typeof overallRevenueParamsSchema>;
export type RestaurantSortConfig = z.infer<typeof restaurantSortConfigSchema>;
export type TrendIconProps = z.infer<typeof trendIconPropsSchema>;
export type SortIconProps = z.infer<typeof sortIconPropsSchema>;
export type ProgressBarProps = z.infer<typeof progressBarPropsSchema>;
export type RiskBadgeProps = z.infer<typeof riskBadgePropsSchema>;
export type TargetFilters = z.infer<typeof targetFiltersSchema>;
export type ApiError = z.infer<typeof apiErrorSchema>;
export type OverallRevenueState = z.infer<typeof overallRevenueStateSchema>;
export type ChartConfig = z.infer<typeof chartConfigSchema>;
export type OverallRevenueProps = z.infer<typeof overallRevenuePropsSchema>;

// Validation helper functions
export const validateOverallRevenueData = (data: unknown): OverallRevenueData => {
  return overallRevenueDataSchema.parse(data);
};

export const validateOverallRevenueParams = (params: unknown): OverallRevenueParams => {
  return overallRevenueParamsSchema.parse(params);
};

export const isValidRestaurantDataItem = (item: unknown): item is PerformanceDataItem => {
  return performanceDataItemSchema.safeParse(item).success;
};

export const isValidTargetDataItem = (item: unknown): boolean => {
  return targetDataItemSchema.safeParse(item).success;
};

export const isValidPerformanceDataItem = (item: unknown): item is PerformanceDataItem => {
  return performanceDataItemSchema.safeParse(item).success;
};
