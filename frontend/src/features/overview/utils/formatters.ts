import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cy<PERSON><PERSON>att<PERSON>, ColorClassGetter, ProgressColorGetter } from '../types';

/**
 * Formats a number as currency with proper sign handling
 */
export const formatCurrency: CurrencyFormatter = (value: number): string => {
  if (value >= 0) {
    return `$${value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
  }
  return `-$${Math.abs(value).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
};

/**
 * Formats large numbers with K, M, B suffixes using compact notation.
 * Handles values like 3,075,000 to display as $3.075M.
 */
export const formatCurrencyCompact = (value: number): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '$0';
  }

  const sign = value < 0 ? '-' : '';
  const absValue = Math.abs(value);

  if (absValue < 1000) {
    return formatCurrency(value);
  }

  // Use Intl.NumberFormat for robust compact number formatting.
  // It handles different magnitudes (K, M, B) and locale-specific formatting.
  const formatter = new Intl.NumberFormat('en-US', {
    notation: 'compact',
    compactDisplay: 'short',
    // Using significant digits gives us better control over precision for compact numbers.
    // e.g., 3.075M for 3,075,000 or 1.23K for 1,230
    minimumSignificantDigits: 1,
    maximumSignificantDigits: 4,
  });

  const formattedAbsValue = formatter.format(absValue);

  return `${sign}$${formattedAbsValue}`;
};

/**
 * Gets the appropriate color class for adjustment values
 */
export const getAdjustmentColor: ColorClassGetter = (value: number): string => {
  if (value < 0) return 'text-red-500';
  if (value > 0) return 'text-green-500';
  return 'text-slate-400';
};

/**
 * Gets the appropriate progress bar color class based on percentage
 */
export const getProgressColorClass: ProgressColorGetter = (percentage: number): string => {
  if (percentage < 75) return 'from-red-500 to-red-400';
  if (percentage < 90) return 'from-amber-500 to-amber-400';
  return 'from-green-500 to-green-400';
};

/**
 * Gets the appropriate color class for risk levels
 */
export const getRiskColorClass = (risk: RiskLevel): string => {
  switch (risk) {
    case 'high':
      return 'bg-red-500';
    case 'medium':
      return 'bg-amber-500';
    case 'low':
    default:
      return 'bg-green-500';
  }
};

/**
 * Formats percentage with proper decimal places
 */
export const formatPercentage = (value: number, decimals = 0): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Formats time for display
 */
export const formatLastUpdated = (date: Date): string => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

/**
 * Truncates text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

/**
 * Capitalizes the first letter of a string
 */
export const capitalizeFirst = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Formats chart tick values
 */
export const formatChartTick = (value: number): string => {
  return `$${value}`;
};

/**
 * Gets accessible label for risk level
 */
export const getRiskAccessibleLabel = (risk: RiskLevel): string => {
  switch (risk) {
    case 'high':
      return 'High risk indicator';
    case 'medium':
      return 'Medium risk indicator';
    case 'low':
      return 'Low risk - no indicator shown';
    default:
      return 'Unknown risk level';
  }
};

/**
 * Gets accessible label for trend direction
 */
export const getTrendAccessibleLabel = (trend: 'up' | 'down' | 'neutral'): string => {
  switch (trend) {
    case 'up':
      return 'Trending upward';
    case 'down':
      return 'Trending downward';
    case 'neutral':
      return 'Neutral trend';
    default:
      return 'Unknown trend';
  }
};

/**
 * Gets accessible label for progress bar
 */
export const getProgressAccessibleLabel = (percentage: number, restaurant: string): string => {
  return `${restaurant} achievement: ${percentage}% of target`;
};

/**
 * Validates if a number is a valid currency amount
 */
export const isValidCurrencyAmount = (value: number): boolean => {
  return !isNaN(value) && isFinite(value) && value >= -999999999 && value <= 999999999;
};

/**
 * Validates if a percentage is within valid range
 */
export const isValidPercentage = (value: number): boolean => {
  return !isNaN(value) && isFinite(value) && value >= 0 && value <= 200;
};
