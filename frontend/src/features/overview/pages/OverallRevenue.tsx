import React from 'react';

// Import our refactored components and hooks
import { useOverallRevenue } from '../hooks/useOverallRevenue';
import { useLastUpdated } from '../hooks/useLastUpdated';
import { useAutoRefreshPreferences } from '../hooks/useAutoRefreshPreferences';

// Import components
import { ErrorState } from '../components/ErrorState';
import { RevenueOverviewCard } from '../components/RevenueOverviewCard';
import { RestaurantPerformanceCard } from '../components/RestaurantPerformanceCard';
import { RefreshControls, CompactRefreshControls } from '../components/RefreshControls';
import {
  RefreshingCardWrapper,
  RevenueOverviewSkeleton,
  RestaurantPerformanceSkeleton,
} from '../components/EnhancedLoadingStates';

// Import types
import type { OverallRevenueProps } from '../types';

/**
 * OverallRevenue component - Refactored with clean architecture
 *
 * This component displays revenue overview, restaurant performance table,
 * and target achievement monitoring with proper accessibility and performance optimizations.
 */
const OverallRevenue = React.memo<OverallRevenueProps>(
  ({ className, refreshInterval = 30000, enablePolling = true }) => {
    // Auto-refresh preferences hook
    const {
      currentInterval,
      isAutoRefreshEnabled,
      setRefreshInterval,
      updateLastUpdated,
      getFormattedLastUpdated,
    } = useAutoRefreshPreferences();

    // Use the current interval from preferences, fallback to props
    const effectiveInterval = isAutoRefreshEnabled
      ? currentInterval
      : enablePolling
        ? refreshInterval
        : false;

    const {
      data: revenueData,
      isLoading,
      isError,
      error,
      refetch,
      isRefetching,
    } = useOverallRevenue(
      {
        period: '12m',
        includeForecasts: true,
      },
      {
        refetchInterval: effectiveInterval,
      }
    );

    // Last updated hook (keeping for backward compatibility)
    const { formattedLastUpdated: _formattedLastUpdated } = useLastUpdated(revenueData);

    // Memoized restaurant data with target information
    const processedRestaurantData = React.useMemo(() => {
      if (!revenueData?.performanceData) {
        return [];
      }
      return revenueData.performanceData.map((restaurant, index) => ({
        ...restaurant,
        id: `restaurant-${index}`,
      }));
    }, [revenueData]);

    // --- EVENT HANDLERS ---
    const handleRefresh = React.useCallback(() => {
      updateLastUpdated();
      refetch();
    }, [refetch, updateLastUpdated]);

    const handleIntervalChange = React.useCallback(
      (interval: number) => {
        setRefreshInterval(interval);
      },
      [setRefreshInterval]
    );

    // --- RENDER STATES ---
    if (isLoading) {
      return (
        <div className={className}>
          {/* Header with skeleton controls */}
          <header className="mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold tracking-tight">Overall Revenue</h1>
              <div className="flex items-center gap-3">
                <div className="h-4 w-32 animate-pulse rounded bg-slate-200" />
                <div className="h-8 w-24 animate-pulse rounded bg-slate-200" />
                <div className="h-8 w-28 animate-pulse rounded bg-slate-200" />
              </div>
            </div>
          </header>

          {/* Skeleton cards */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <RevenueOverviewSkeleton />
            <RestaurantPerformanceSkeleton />
          </div>
        </div>
      );
    }

    if (isError || !revenueData) {
      return <ErrorState error={error} onRetry={handleRefresh} showRetryButton={true} />;
    }

    // --- MAIN RENDER ---
    return (
      <div className={className}>
        {/* Screen reader announcements */}

        {/* Header with enhanced refresh controls */}
        <header className="mb-6">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">Overall Revenue</h1>
              <p className="text-muted-foreground text-base">
                Monitor revenue performance, restaurant metrics, and target achievements
              </p>
            </div>

            {/* Desktop refresh controls */}
            <div className="hidden lg:block">
              <RefreshControls
                onIntervalChange={handleIntervalChange}
                currentInterval={currentInterval}
                isAutoRefreshEnabled={isAutoRefreshEnabled}
                getFormattedLastUpdated={getFormattedLastUpdated}
              />
            </div>

            {/* Mobile refresh controls */}
            <div className="lg:hidden">
              <CompactRefreshControls
                onIntervalChange={handleIntervalChange}
                currentInterval={currentInterval}
                isAutoRefreshEnabled={isAutoRefreshEnabled}
                getFormattedLastUpdated={getFormattedLastUpdated}
              />
            </div>
          </div>
        </header>

        {/* Main content grid */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Revenue Overview Card with enhanced loading */}
          <RefreshingCardWrapper
            isRefreshing={isRefetching}
            overlayMessage="Updating revenue data..."
          >
            <RevenueOverviewCard
              overview={revenueData.overview}
              chartData={revenueData.chartData}
              isRefreshing={isRefetching}
              className="border-gray-200 bg-white shadow-sm"
            />
          </RefreshingCardWrapper>

          {/* Restaurant Performance Card with enhanced loading */}
          <RefreshingCardWrapper
            isRefreshing={isRefetching}
            overlayMessage="Updating restaurant data..."
          >
            <RestaurantPerformanceCard
              restaurantData={processedRestaurantData}
              className="border-gray-200 bg-white shadow-sm"
            />
          </RefreshingCardWrapper>
        </div>

        {/* Footer with enhanced information */}
        <footer className="mt-6 space-y-1 text-center text-xs text-slate-400">
          <p>
            Data refreshes{' '}
            {isAutoRefreshEnabled
              ? `every ${currentInterval / 1000}s`
              : 'automatically when enabled'}
          </p>
        </footer>
      </div>
    );
  }
);

OverallRevenue.displayName = 'OverallRevenue';

export default OverallRevenue;
