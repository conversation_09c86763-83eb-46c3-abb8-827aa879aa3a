import React from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { SortIconProps } from '../types';

/**
 * SortIcon component for displaying sort direction
 */
export const SortIcon = React.memo<SortIconProps>(({ direction, className }) => {
  const iconClass = cn('h-4 w-4 text-slate-600', className);

  if (direction === 'descending') {
    return <ChevronDown className={iconClass} aria-label="Sorted descending" />;
  }

  return <ChevronUp className={iconClass} aria-label="Sorted ascending" />;
});

SortIcon.displayName = 'SortIcon';

/**
 * SortableColumnHeader component with built-in sort functionality
 */
export const SortableColumnHeader = React.memo<{
  children: React.ReactNode;
  sortKey: string;
  currentSortKey: string;
  sortDirection: 'ascending' | 'descending';
  onSort: (key: any) => void;
  className?: string;
  align?: 'left' | 'center' | 'right';
}>(({ children, sortKey, currentSortKey, sortDirection, onSort, className, align = 'left' }) => {
  const isActive = sortKey === currentSortKey;
  const alignmentClass = {
    left: 'justify-start text-left',
    center: 'justify-center text-center',
    right: 'justify-end text-right',
  }[align];

  return (
    <button
      type="button"
      onClick={() => onSort(sortKey)}
      className={cn(
        'flex w-full items-center gap-1.5 transition-colors hover:text-slate-800',
        alignmentClass,
        className
      )}
      aria-label={`Sort by ${children}${isActive ? `, currently sorted ${sortDirection}` : ''}`}
    >
      <span>{children}</span>
      {isActive && <SortIcon direction={sortDirection} />}
    </button>
  );
});

SortableColumnHeader.displayName = 'SortableColumnHeader';
