import React from 'react';
import { Setting<PERSON>, Clock, Zap, ZapOff } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { Badge } from '@/shared/components/ui/badge';
import { cn } from '@/lib/utils';
import { REFRESH_INTERVAL_OPTIONS } from '../utils/constants';
import { useAutoUpdatingTimestamp } from '../hooks/useAutoRefreshPreferences';

interface RefreshControlsProps {
  onIntervalChange: (interval: number) => void;
  currentInterval: number;
  isAutoRefreshEnabled: boolean;
  getFormattedLastUpdated: () => string;
  className?: string;
}

/**
 * Comprehensive refresh controls component with manual refresh, auto-refresh settings, and last updated display
 */
export const RefreshControls = React.memo<RefreshControlsProps>(
  ({
    onIntervalChange,
    currentInterval,
    isAutoRefreshEnabled,
    getFormattedLastUpdated,
    className,
  }) => {
    // Auto-updating timestamp display
    const formattedLastUpdated = useAutoUpdatingTimestamp(getFormattedLastUpdated);

    // Get current interval option for display
    const currentOption = REFRESH_INTERVAL_OPTIONS.find(option => option.value === currentInterval);

    return (
      <div className={cn('flex items-center gap-3', className)}>
        {/* Last Updated Display */}
        <div className="flex items-center gap-2 text-sm text-slate-500">
          <Clock className="h-4 w-4" aria-hidden="true" />
          <span>Last updated: {formattedLastUpdated}</span>
        </div>

        {/* Auto-refresh Status Badge */}
        {isAutoRefreshEnabled && (
          <Badge variant="secondary" className="gap-1">
            <Zap className="h-3 w-3" aria-hidden="true" />
            Auto: {currentOption?.label || 'Custom'}
          </Badge>
        )}

        {!isAutoRefreshEnabled && (
          <Badge variant="outline" className="gap-1">
            <ZapOff className="h-3 w-3" aria-hidden="true" />
            Manual only
          </Badge>
        )}

        {/* Auto-refresh Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              aria-label="Auto-refresh settings"
            >
              <Settings className="h-4 w-4" aria-hidden="true" />
              <span className="hidden sm:inline">Auto-refresh</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Auto-refresh Interval</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {REFRESH_INTERVAL_OPTIONS.map(option => (
              <DropdownMenuItem
                key={option.value}
                onClick={() => onIntervalChange(option.value)}
                className={cn(
                  'flex items-center justify-between',
                  currentInterval === option.value && 'bg-accent'
                )}
              >
                <div className="flex flex-col">
                  <span className="font-medium">{option.label}</span>
                  <span className="text-muted-foreground text-xs">{option.description}</span>
                </div>
                {currentInterval === option.value && (
                  <div className="h-2 w-2 rounded-full bg-blue-600" aria-hidden="true" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }
);

RefreshControls.displayName = 'RefreshControls';

interface CompactRefreshControlsProps {
  onIntervalChange: (interval: number) => void;
  currentInterval: number;
  isAutoRefreshEnabled: boolean;
  getFormattedLastUpdated: () => string;
  className?: string;
}

/**
 * Compact version of refresh controls for mobile/smaller screens
 */
export const CompactRefreshControls = React.memo<CompactRefreshControlsProps>(
  ({
    onIntervalChange,
    currentInterval,
    isAutoRefreshEnabled,
    getFormattedLastUpdated,
    className,
  }) => {
    const formattedLastUpdated = useAutoUpdatingTimestamp(getFormattedLastUpdated);
    const currentOption = REFRESH_INTERVAL_OPTIONS.find(option => option.value === currentInterval);

    return (
      <div className={cn('flex flex-col gap-2', className)}>
        {/* Status Row */}
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>Last updated: {formattedLastUpdated}</span>
          {isAutoRefreshEnabled && (
            <Badge variant="secondary" className="text-xs">
              Auto: {currentOption?.label || 'Custom'}
            </Badge>
          )}
        </div>

        {/* Controls Row */}
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex-1">
                <Settings className="mr-2 h-4 w-4" aria-hidden="true" />
                Auto-refresh
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel>Interval</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {REFRESH_INTERVAL_OPTIONS.map(option => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => onIntervalChange(option.value)}
                  className={cn(currentInterval === option.value && 'bg-accent')}
                >
                  {option.label}
                  {currentInterval === option.value && (
                    <div className="ml-auto h-2 w-2 rounded-full bg-blue-600" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  }
);

CompactRefreshControls.displayName = 'CompactRefreshControls';
