import React from 'react';
import { RefreshCw } from 'lucide-react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import { RevenueChart } from './RevenueChart';
import { InlineLoadingState } from './LoadingState';
import type { RevenueOverview, ChartDataItem } from '../types';
import { formatCurrencyCompact } from '../utils/formatters';
import { ARIA_LABELS } from '../utils/constants';

interface RevenueOverviewCardProps {
  overview: RevenueOverview;
  chartData: ChartDataItem[];
  onRefresh?: () => void;
  isRefreshing?: boolean;
  className?: string;
}

/**
 * RevenueOverviewCard component displaying key revenue metrics and chart
 */
export const RevenueOverviewCard = React.memo<RevenueOverviewCardProps>(
  ({ overview, chartData, onRefresh, isRefreshing = false, className }) => {
    const { actualRevenue, forecastRevenue, achievementPercentage } = overview;

    return (
      <Card
        className={`h-[600px] ${className}`}
        role="region"
        aria-labelledby="revenue-overview-title"
      >
        <CardContent className="h-full p-6">
          <div className="flex h-full flex-col space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2 id="revenue-overview-title" className="text-lg font-semibold text-slate-800">
                Revenue Overview
              </h2>
              {onRefresh && (
                <Button
                  onClick={onRefresh}
                  variant="outline"
                  size="sm"
                  disabled={isRefreshing}
                  aria-label={ARIA_LABELS.REFRESH_BUTTON}
                  className="gap-2"
                >
                  <RefreshCw
                    className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                    aria-hidden="true"
                  />
                  {isRefreshing ? 'Refreshing...' : 'Refresh'}
                </Button>
              )}
            </div>

            {/* Main revenue figure */}
            <div>
              <div
                className="text-5xl font-bold tracking-tight text-slate-800"
                aria-label={`Actual revenue: ${formatCurrencyCompact(actualRevenue)}`}
              >
                {formatCurrencyCompact(actualRevenue)}
              </div>
              <p className="mt-2 text-slate-500">Actual Revenue</p>
            </div>

            {/* Progress bar and achievement */}
            <div className="space-y-2">
              <Progress
                value={achievementPercentage}
                className="h-3 bg-slate-100"
                indicatorClassName="bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-500 ease-out"
                aria-label={`Achievement: ${achievementPercentage}% of forecast target`}
              />
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium text-blue-600">{achievementPercentage}%</span>
                <span className="text-slate-500">
                  vs {formatCurrencyCompact(forecastRevenue)} Forecast
                </span>
              </div>
            </div>

            {/* Chart section */}
            <div className="min-h-0 flex-1">
              <div className="h-full w-full">
                {isRefreshing ? (
                  <div className="flex h-full items-center justify-center">
                    <InlineLoadingState message="Updating chart..." />
                  </div>
                ) : (
                  <RevenueChart
                    data={chartData}
                    height={256}
                    showLegend={true}
                    showTooltip={true}
                  />
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

RevenueOverviewCard.displayName = 'RevenueOverviewCard';

/**
 * Compact version for smaller screens
 */
export const CompactRevenueOverviewCard = React.memo<RevenueOverviewCardProps>(
  ({ overview, chartData, onRefresh, isRefreshing = false, className }) => {
    const { actualRevenue, forecastRevenue: _forecastRevenue, achievementPercentage } = overview;

    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2 className="text-base font-semibold text-slate-800">Revenue</h2>
              {onRefresh && (
                <Button
                  onClick={onRefresh}
                  variant="ghost"
                  size="sm"
                  disabled={isRefreshing}
                  aria-label={ARIA_LABELS.REFRESH_BUTTON}
                >
                  <RefreshCw
                    className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                    aria-hidden="true"
                  />
                </Button>
              )}
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-slate-800">
                  {formatCurrencyCompact(actualRevenue)}
                </div>
                <p className="text-xs text-slate-500">Actual</p>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{achievementPercentage}%</div>
                <p className="text-xs text-slate-500">Achievement</p>
              </div>
            </div>

            {/* Mini chart */}
            <div className="h-24">
              <RevenueChart data={chartData} height={96} showLegend={false} showTooltip={false} />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

CompactRevenueOverviewCard.displayName = 'CompactRevenueOverviewCard';
