import React from 'react';
import { Progress } from '@/shared/components/ui/progress';
import { cn } from '@/lib/utils';
import type { ProgressBarProps } from '../types';
import { getProgressColorClass, getProgressAccessibleLabel } from '../utils/formatters';

/**
 * Enhanced ProgressBar component with accessibility and color coding
 */
export const ProgressBar = React.memo<ProgressBarProps>(
  ({ value, className, 'aria-label': ariaLabel }) => {
    const colorClass = getProgressColorClass(value);

    return (
      <Progress
        value={value}
        className={cn('h-2 transition-all duration-500 ease-in-out', className)}
        aria-label={ariaLabel}
        indicatorClassName={cn(`bg-gradient-to-r ${colorClass}`)}
      />
    );
  }
);

ProgressBar.displayName = 'ProgressBar';

/**
 * ProgressBarWithLabel component that includes percentage display
 */
interface ProgressBarWithLabelProps extends ProgressBarProps {
  restaurant?: string;
  showPercentage?: boolean;
  labelPosition?: 'top' | 'bottom' | 'right';
}

export const ProgressBarWithLabel = React.memo<ProgressBarWithLabelProps>(
  ({
    value,
    className,
    restaurant,
    showPercentage = true,
    labelPosition = 'right',
    'aria-label': ariaLabel,
  }) => {
    const accessibleLabel =
      ariaLabel ||
      (restaurant ? getProgressAccessibleLabel(value, restaurant) : `Progress: ${value}%`);

    const progressBar = (
      <ProgressBar value={value} className={className} aria-label={accessibleLabel} />
    );

    if (!showPercentage) {
      return progressBar;
    }

    const percentageLabel = (
      <span className="min-w-[3rem] text-right text-sm font-semibold text-slate-600">{value}%</span>
    );

    switch (labelPosition) {
      case 'top':
        return (
          <div className="space-y-1">
            {percentageLabel}
            {progressBar}
          </div>
        );
      case 'bottom':
        return (
          <div className="space-y-1">
            {progressBar}
            {percentageLabel}
          </div>
        );
      case 'right':
      default:
        return (
          <div className="flex w-full items-center gap-3">
            <div className="flex-grow">{progressBar}</div>
            {percentageLabel}
          </div>
        );
    }
  }
);

ProgressBarWithLabel.displayName = 'ProgressBarWithLabel';

/**
 * ProgressBarGrid component for displaying multiple progress bars
 */
interface ProgressBarGridProps {
  items: Array<{
    id: string;
    label: string;
    value: number;
    trend?: 'up' | 'down' | 'neutral';
  }>;
  className?: string;
}

export const ProgressBarGrid = React.memo<ProgressBarGridProps>(({ items, className }) => {
  return (
    <div className={cn('space-y-3', className)}>
      {items.map(item => (
        <div key={item.id} className="grid grid-cols-12 items-center gap-x-4">
          <div className="col-span-1">
            {item.trend && (
              <div className="flex justify-center">{/* Trend icon would go here */}</div>
            )}
          </div>
          <p className="col-span-5 truncate text-sm font-medium text-slate-800">{item.label}</p>
          <div className="col-span-6">
            <ProgressBarWithLabel value={item.value} restaurant={item.label} />
          </div>
        </div>
      ))}
    </div>
  );
});

ProgressBarGrid.displayName = 'ProgressBarGrid';
