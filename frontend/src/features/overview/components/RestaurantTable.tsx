import React from 'react';
import { cn } from '@/lib/utils';
import { SortableColumnHeader } from './SortIcon';
import { RiskBadge } from './RiskBadge';
import type { RestaurantDataItem, RestaurantSortKeys, RestaurantSortConfig } from '../types';
import { formatCurrency, getAdjustmentColor } from '../utils/formatters';
import { RESTAURANT_TABLE_COLUMNS } from '../utils/constants';

interface RestaurantTableProps {
  data: RestaurantDataItem[];
  sortConfig: RestaurantSortConfig;
  onSort: (key: RestaurantSortKeys) => void;
  className?: string;
  maxHeight?: string;
}

/**
 * RestaurantTable component for displaying restaurant revenue data
 */
export const RestaurantTable = React.memo<RestaurantTableProps>(
  ({ data, sortConfig, onSort, className, maxHeight = 'max-h-96' }) => {
    if (!data || data.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <p className="font-semibold text-slate-700">No Restaurant Data</p>
          <p className="text-sm text-slate-500">
            Restaurant revenue data is not available at this time.
          </p>
        </div>
      );
    }

    return (
      <div className={cn('overflow-hidden', className)}>
        {/* Table Header */}
        <div className="grid grid-cols-5 gap-4 border-b pb-2 text-sm font-medium text-slate-500">
          {RESTAURANT_TABLE_COLUMNS.map(column => (
            <div key={column.key} className={column.className}>
              {column.sortable ? (
                <SortableColumnHeader
                  sortKey={column.key}
                  currentSortKey={sortConfig.key}
                  sortDirection={sortConfig.direction}
                  onSort={onSort}
                  align={column.align}
                >
                  {column.label}
                </SortableColumnHeader>
              ) : (
                <span
                  className={cn(
                    'text-sm font-medium text-slate-500',
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right'
                  )}
                >
                  {column.label}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* Table Body */}
        <div className={cn('mt-2 space-y-1 overflow-y-auto', maxHeight)}>
          {data.map((item, index) => (
            <RestaurantTableRow key={`${item.restaurant}-${index}`} item={item} />
          ))}
        </div>
      </div>
    );
  }
);

RestaurantTable.displayName = 'RestaurantTable';

/**
 * Individual table row component
 */
const RestaurantTableRow = React.memo<{ item: RestaurantDataItem }>(({ item }) => {
  return (
    <div
      className="grid grid-cols-5 items-center gap-4 rounded-md py-2 text-sm transition-colors focus-within:bg-gray-50 hover:bg-gray-50"
      tabIndex={0}
      aria-label={`Restaurant: ${item.restaurant}, Actual: ${formatCurrency(item.actual)}, Risk: ${item.risk}`}
    >
      {/* Restaurant Name and Forecast */}
      <div className="col-span-2">
        <p className="truncate font-semibold text-slate-800" title={item.restaurant}>
          {item.restaurant}
        </p>
        <p className="text-xs text-slate-500">FC: {formatCurrency(item.forecast)}</p>
      </div>

      {/* Q3 Adjusted */}
      <div
        className={cn(
          'text-right font-medium',
          item.q3Adjusted != null ? getAdjustmentColor(item.q3Adjusted) : ''
        )}
        title={`Q3 Adjusted: ${item.q3Adjusted != null ? formatCurrency(item.q3Adjusted) : 'N/A'}`}
      >
        {item.q3Adjusted != null ? formatCurrency(item.q3Adjusted) : 'N/A'}
      </div>

      {/* Actual Revenue */}
      <div
        className="text-right font-medium text-slate-800"
        title={`Actual Revenue: ${formatCurrency(item.actual)}`}
      >
        {formatCurrency(item.actual)}
      </div>

      {/* Risk Badge */}
      <div className="text-center">
        <RiskBadge risk={item.risk} />
      </div>
    </div>
  );
});

RestaurantTableRow.displayName = 'RestaurantTableRow';

/**
 * Compact version of the restaurant table for smaller screens
 */
export const CompactRestaurantTable = React.memo<RestaurantTableProps>(({ data, className }) => {
  if (!data || data.length === 0) {
    return (
      <div className="py-4 text-center">
        <p className="text-slate-500">No data available</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {data.map((item, index) => (
        <div key={`${item.restaurant}-${index}`} className="rounded-lg bg-gray-50 p-3">
          <div className="mb-2 flex items-start justify-between">
            <h4 className="truncate font-semibold text-slate-800">{item.restaurant}</h4>
            <RiskBadge risk={item.risk} />
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-slate-500">Forecast:</span>
              <span className="ml-1 font-medium">{formatCurrency(item.forecast)}</span>
            </div>
            <div>
              <span className="text-slate-500">Actual:</span>
              <span className="ml-1 font-medium">{formatCurrency(item.actual)}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});

CompactRestaurantTable.displayName = 'CompactRestaurantTable';
