import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * LoadingState component with accessibility support
 */
export const LoadingState = React.memo<LoadingStateProps>(
  ({ message = 'Loading revenue data...', className, size = 'md' }) => {
    const sizeClasses = {
      sm: 'w-6 h-6',
      md: 'w-8 h-8',
      lg: 'w-12 h-12',
    };

    return (
      <div
        className={cn('flex min-h-[200px] items-center justify-center', className)}
        role="status"
        aria-live="polite"
      >
        <div className="text-center">
          <Loader2
            className={cn('mx-auto mb-4 animate-spin text-blue-600', sizeClasses[size])}
            aria-hidden="true"
          />
          <p className="text-gray-600" id="loading-message">
            {message}
          </p>
          <span className="sr-only">Loading, please wait</span>
        </div>
      </div>
    );
  }
);

LoadingState.displayName = 'LoadingState';

/**
 * Inline loading spinner for smaller components
 */
export const InlineLoadingState = React.memo<{
  message?: string;
  className?: string;
}>(({ message = 'Loading...', className }) => {
  return (
    <div className={cn('flex items-center gap-2', className)} role="status" aria-live="polite">
      <Loader2 className="h-4 w-4 animate-spin text-blue-600" aria-hidden="true" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
});

InlineLoadingState.displayName = 'InlineLoadingState';
