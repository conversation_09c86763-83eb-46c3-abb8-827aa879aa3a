import React from 'react';
import { Loader2, RefreshCw } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '@/shared/components/ui/card';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { Progress } from '@/shared/components/ui/progress';
import { cn } from '@/lib/utils';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

/**
 * Loading overlay that appears over content during refresh
 */
export const LoadingOverlay = React.memo<LoadingOverlayProps>(
  ({ isVisible, message = 'Refreshing data...', className }) => {
    if (!isVisible) return null;

    return (
      <div
        className={cn(
          'absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm',
          'transition-opacity duration-200',
          className
        )}
        role="status"
        aria-live="polite"
      >
        <div className="flex items-center gap-3 rounded-lg border bg-white px-4 py-3 shadow-lg">
          <RefreshCw className="h-5 w-5 animate-spin text-blue-600" aria-hidden="true" />
          <span className="text-sm font-medium text-slate-700">{message}</span>
        </div>
      </div>
    );
  }
);

LoadingOverlay.displayName = 'LoadingOverlay';

interface ProgressIndicatorProps {
  isVisible: boolean;
  progress?: number;
  message?: string;
  className?: string;
}

/**
 * Progress indicator for data refresh operations
 */
export const ProgressIndicator = React.memo<ProgressIndicatorProps>(
  ({ isVisible, progress, message = 'Loading...', className }) => {
    if (!isVisible) return null;

    return (
      <div className={cn('space-y-2', className)} role="status" aria-live="polite">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin text-blue-600" aria-hidden="true" />
          <span className="text-sm text-slate-600">{message}</span>
        </div>
        {typeof progress === 'number' && (
          <Progress
            value={progress}
            className="h-1"
            aria-label={`Loading progress: ${progress}%`}
          />
        )}
      </div>
    );
  }
);

ProgressIndicator.displayName = 'ProgressIndicator';

/**
 * Skeleton for Revenue Overview Card
 */
export const RevenueOverviewSkeleton = React.memo(() => (
  <Card className="border-gray-200 bg-white shadow-sm">
    <CardContent className="p-6">
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-20" />
        </div>

        {/* Main revenue figure skeleton */}
        <div>
          <Skeleton className="mb-2 h-12 w-48" />
          <Skeleton className="h-4 w-24" />
        </div>

        {/* Progress bar skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-2 w-full" />
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>

        {/* Chart skeleton */}
        <div className="flex h-64 w-full items-center justify-center rounded-lg bg-slate-50">
          <div className="text-center">
            <Skeleton className="mx-auto mb-2 h-8 w-8 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
));

RevenueOverviewSkeleton.displayName = 'RevenueOverviewSkeleton';

/**
 * Skeleton for Restaurant Performance Card
 */
export const RestaurantPerformanceSkeleton = React.memo(() => (
  <Card className="border-gray-200 bg-white shadow-sm">
    <CardHeader>
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <Skeleton className="h-8 w-20" />
      </div>
      <div className="mt-4 flex items-center gap-2">
        <Skeleton className="h-9 flex-1" />
        <Skeleton className="h-9 w-12" />
      </div>
    </CardHeader>
    <CardContent>
      <Skeleton className="mb-2 h-5 w-32" />

      {/* Table skeleton */}
      <div className="space-y-3">
        {/* Header row */}
        <div className="flex items-center gap-4 border-b pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-24" />
        </div>

        {/* Data rows */}
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center gap-4 py-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-3 rounded-full" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-2 w-24" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-4" />
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
));

RestaurantPerformanceSkeleton.displayName = 'RestaurantPerformanceSkeleton';

interface RefreshingCardWrapperProps {
  isRefreshing: boolean;
  children: React.ReactNode;
  className?: string;
  overlayMessage?: string;
}

/**
 * Wrapper component that adds loading overlay to cards during refresh
 */
export const RefreshingCardWrapper = React.memo<RefreshingCardWrapperProps>(
  ({ isRefreshing, children, className, overlayMessage = 'Updating...' }) => (
    <div className={cn('relative', className)}>
      {children}
      <LoadingOverlay isVisible={isRefreshing} message={overlayMessage} />
    </div>
  )
);

RefreshingCardWrapper.displayName = 'RefreshingCardWrapper';
