import React from 'react';
import { cn } from '@/lib/utils';
import type { RiskBadgeProps } from '../types';
import { getRiskAccessibleLabel } from '../utils/formatters';
import { RISK_CONFIG } from '../utils/constants';

/**
 * RiskBadge component for displaying risk level indicators
 */
export const RiskBadge = React.memo<RiskBadgeProps>(({ risk, className }) => {
  const config = RISK_CONFIG[risk];

  // Don't show indicator for low risk
  if (!config.showIndicator) {
    return (
      <div className="flex justify-center" aria-label={getRiskAccessibleLabel(risk)} role="img">
        <span className="sr-only">{config.label}</span>
      </div>
    );
  }

  return (
    <div className="flex justify-center">
      <div
        className={cn('h-2 w-2 rounded-full', config.color, className)}
        aria-label={getRiskAccessibleLabel(risk)}
        role="img"
        title={config.label}
      />
      <span className="sr-only">{config.label}</span>
    </div>
  );
});

RiskBadge.displayName = 'RiskBadge';

/**
 * RiskBadgeWithLabel component that includes text label
 */
export const RiskBadgeWithLabel = React.memo<RiskBadgeProps & { showLabel?: boolean }>(
  ({ risk, className, showLabel = false }) => {
    const config = RISK_CONFIG[risk];

    return (
      <div className="flex items-center justify-center gap-2">
        <RiskBadge risk={risk} className={className} />
        {showLabel && <span className="text-sm text-slate-600">{config.label}</span>}
      </div>
    );
  }
);

RiskBadgeWithLabel.displayName = 'RiskBadgeWithLabel';

/**
 * RiskLegend component for explaining risk indicators
 */
export const RiskLegend = React.memo(() => {
  return (
    <div className="flex items-center gap-4 text-xs text-slate-500">
      <span>Risk Levels:</span>
      <div className="flex items-center gap-1">
        <div className="h-2 w-2 rounded-full bg-red-500" />
        <span>High</span>
      </div>
      <div className="flex items-center gap-1">
        <div className="h-2 w-2 rounded-full bg-amber-500" />
        <span>Medium</span>
      </div>
      <div className="flex items-center gap-1">
        <span>Low (no indicator)</span>
      </div>
    </div>
  );
});

RiskLegend.displayName = 'RiskLegend';
