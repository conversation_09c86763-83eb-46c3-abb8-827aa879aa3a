import React from 'react';
import { <PERSON><PERSON><PERSON>ircle, RefreshCw } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/lib/utils';
import type { ApiError } from '../types';

interface ErrorStateProps {
  error: ApiError | Error | null;
  onRetry?: () => void;
  className?: string;
  showRetryButton?: boolean;
}

/**
 * ErrorState component with accessibility and retry functionality
 */
export const ErrorState = React.memo<ErrorStateProps>(
  ({ error, onRetry, className, showRetryButton = true }) => {
    const getErrorMessage = (error: ApiError | Error | null): string => {
      if (!error) return 'An unknown error occurred';

      if ('code' in error && error.code) {
        switch (error.code) {
          case 'NETWORK_ERROR':
            return 'Network connection failed. Please check your internet connection.';
          case 'VALIDATION_ERROR':
            return 'Invalid data received. Please try refreshing the page.';
          case 'HTTP_404':
            return 'The requested data could not be found.';
          case 'HTTP_500':
            return 'Server error occurred. Please try again later.';
          default:
            return error.message || 'Failed to load revenue data';
        }
      }

      return error.message || 'An unexpected error occurred';
    };

    const getErrorTitle = (error: ApiError | Error | null): string => {
      if (!error) return 'Error';

      if ('code' in error && error.code) {
        switch (error.code) {
          case 'NETWORK_ERROR':
            return 'Connection Error';
          case 'VALIDATION_ERROR':
            return 'Data Error';
          case 'HTTP_404':
            return 'Not Found';
          case 'HTTP_500':
            return 'Server Error';
          default:
            return 'Error Loading Data';
        }
      }

      return 'Error';
    };

    const errorMessage = getErrorMessage(error);
    const errorTitle = getErrorTitle(error);

    return (
      <div
        className={cn('flex min-h-[200px] items-center justify-center', className)}
        role="alert"
        aria-live="assertive"
      >
        <div className="max-w-md text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" aria-hidden="true" />
          <h2 className="mb-2 text-xl font-semibold text-red-600" id="error-title">
            {errorTitle}
          </h2>
          <p className="mb-4 text-gray-600" id="error-message">
            {errorMessage}
          </p>
          {showRetryButton && onRetry && (
            <Button
              onClick={onRetry}
              variant="outline"
              className="gap-2"
              aria-describedby="error-message"
            >
              <RefreshCw className="h-4 w-4" aria-hidden="true" />
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }
);

ErrorState.displayName = 'ErrorState';

/**
 * Inline error component for smaller displays
 */
export const InlineErrorState = React.memo<{
  error: ApiError | Error | null;
  onRetry?: () => void;
  className?: string;
}>(({ error, onRetry, className }) => {
  const errorMessage = error?.message || 'An error occurred';

  return (
    <div
      className={cn(
        'flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-2',
        className
      )}
      role="alert"
    >
      <AlertCircle className="h-4 w-4 flex-shrink-0 text-red-500" aria-hidden="true" />
      <span className="flex-grow text-sm text-red-700">{errorMessage}</span>
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="ghost"
          size="sm"
          className="p-1 text-red-700 hover:text-red-800"
        >
          <RefreshCw className="h-3 w-3" aria-hidden="true" />
          <span className="sr-only">Retry</span>
        </Button>
      )}
    </div>
  );
});

InlineErrorState.displayName = 'InlineErrorState';

/**
 * Empty state component for when no data is available
 */
export const EmptyState = React.memo<{
  title?: string;
  message?: string;
  className?: string;
}>(
  ({
    title = 'No Data Available',
    message = 'Revenue data is not available at this time.',
    className,
  }) => {
    return (
      <div
        className={cn('flex min-h-[200px] items-center justify-center', className)}
        role="status"
      >
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-slate-700">{title}</h2>
          <p className="text-gray-600">{message}</p>
        </div>
      </div>
    );
  }
);

EmptyState.displayName = 'EmptyState';
