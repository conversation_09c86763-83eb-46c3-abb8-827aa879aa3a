import React from 'react';
import { ChevronUp, ChevronDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { TrendIconProps, TrendDirection } from '../types';
import { getTrendAccessibleLabel } from '../utils/formatters';
import { TREND_CONFIG } from '../utils/constants';

/**
 * TrendIcon component for displaying trend direction with proper accessibility
 */
export const TrendIcon = React.memo<TrendIconProps>(
  ({ trend, className, 'aria-label': ariaLabel }) => {
    const config = TREND_CONFIG[trend];
    const accessibleLabel = ariaLabel || getTrendAccessibleLabel(trend);

    const iconProps = {
      className: cn('w-5 h-5', config.color, className),
      'aria-label': accessibleLabel,
      role: 'img',
    };

    switch (trend) {
      case 'up':
        return <ChevronUp {...iconProps} />;
      case 'down':
        return <ChevronDown {...iconProps} />;
      case 'neutral':
      default:
        return (
          <div
            className="flex h-5 w-5 items-center justify-center"
            aria-label={accessibleLabel}
            role="img"
          >
            <Minus className={cn('h-3 w-3', config.color, className)} />
          </div>
        );
    }
  }
);

TrendIcon.displayName = 'TrendIcon';

/**
 * TrendIconWithLabel component that includes text label
 */
export const TrendIconWithLabel = React.memo<TrendIconProps & { showLabel?: boolean }>(
  ({ trend, className, showLabel = false, 'aria-label': ariaLabel }) => {
    const config = TREND_CONFIG[trend];

    return (
      <div className="flex items-center gap-1">
        <TrendIcon trend={trend} className={className} aria-label={ariaLabel} />
        {showLabel && <span className={cn('text-sm', config.color)}>{config.label}</span>}
      </div>
    );
  }
);

TrendIconWithLabel.displayName = 'TrendIconWithLabel';
