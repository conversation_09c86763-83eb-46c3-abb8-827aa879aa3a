import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  Legend,
  TooltipProps,
} from 'recharts';
import type { ChartDataItem } from '../types';
import { formatChartTick } from '../utils/formatters';
import { DEFAULT_CHART_CONFIG, CHART_LEGEND_ITEMS, ARIA_LABELS } from '../utils/constants';

interface RevenueChartProps {
  data: ChartDataItem[];
  className?: string;
  height?: number;
  showLegend?: boolean;
  showTooltip?: boolean;
}

/**
 * Custom tooltip component for the chart
 */
const CustomTooltip = React.memo<TooltipProps<number, string>>(({ active, payload, label }) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
      <p className="mb-2 font-medium text-slate-800">{label}</p>
      {payload.map((entry: any, index: number) => (
        <div key={index} className="flex items-center gap-2 text-sm">
          <div className="h-3 w-3 rounded-full" style={{ backgroundColor: entry.color }} />
          <span className="text-slate-600">{entry.name}:</span>
          <span className="font-medium text-slate-800">{formatChartTick(entry.value)}</span>
        </div>
      ))}
    </div>
  );
});

CustomTooltip.displayName = 'CustomTooltip';

/**
 * RevenueChart component for displaying forecast vs actual revenue
 */
export const RevenueChart = React.memo<RevenueChartProps>(
  ({ data, className, height = 256, showLegend = true, showTooltip = true }) => {
    if (!data || data.length === 0) {
      return (
        <div
          className="flex items-center justify-center text-slate-500"
          style={{ height }}
          role="img"
          aria-label="No chart data available"
        >
          <p>No data available</p>
        </div>
      );
    }

    return (
      <div className={className}>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={DEFAULT_CHART_CONFIG.margin}
              role="img"
              aria-label={ARIA_LABELS.CHART}
            >
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#94a3b8', fontSize: 12 }}
                aria-label="Month axis"
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#94a3b8', fontSize: 12 }}
                tickFormatter={formatChartTick}
                domain={['dataMin - 50', 'dataMax + 50']}
                aria-label="Revenue axis"
              />
              {showTooltip && <Tooltip content={<CustomTooltip />} />}
              <Line
                type="monotone"
                dataKey="forecast"
                stroke={DEFAULT_CHART_CONFIG.colors.forecast}
                strokeWidth={2}
                dot={{ fill: DEFAULT_CHART_CONFIG.colors.forecast, strokeWidth: 0, r: 3 }}
                name="Forecast"
                aria-label="Forecast revenue line"
              />
              <Line
                type="monotone"
                dataKey="actual"
                stroke={DEFAULT_CHART_CONFIG.colors.actual}
                strokeWidth={2}
                dot={{ fill: DEFAULT_CHART_CONFIG.colors.actual, strokeWidth: 0, r: 3 }}
                name="Actual"
                aria-label="Actual revenue line"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {showLegend && (
          <div className="mt-4 flex justify-center gap-6 text-sm">
            {CHART_LEGEND_ITEMS.map(item => (
              <div key={item.dataKey} className="flex items-center gap-2">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{ backgroundColor: item.color }}
                  aria-hidden="true"
                />
                <span className="text-slate-600">{item.label}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }
);

RevenueChart.displayName = 'RevenueChart';

/**
 * Simplified chart component for smaller displays
 */
export const MiniRevenueChart = React.memo<{
  data: ChartDataItem[];
  className?: string;
}>(({ data, className }) => {
  return (
    <RevenueChart
      data={data}
      className={className}
      height={120}
      showLegend={false}
      showTooltip={false}
    />
  );
});

MiniRevenueChart.displayName = 'MiniRevenueChart';
