import React, { useState, useMemo } from 'react';
import { Search, ChevronDown, ArrowUp, ArrowDown } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Input } from '@/shared/components/ui/input';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { formatCurrency } from '../utils/formatters';
import type { RestaurantPerformanceData, RestaurantSortConfig, RestaurantSortKeys } from '../types';

interface RestaurantPerformanceCardProps {
  restaurantData: RestaurantPerformanceData[];
  className?: string;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

const RiskIndicator = ({ riskLevel }: { riskLevel: 'low' | 'medium' | 'high' }) => {
  const riskColor = {
    low: 'bg-green-500',
    medium: 'bg-yellow-500',
    high: 'bg-red-500',
  }[riskLevel];
  return <div className={`h-2.5 w-2.5 rounded-full ${riskColor}`} />;
};

const getAchievementColor = (percentage: number) => {
  if (percentage < 40) return 'from-red-500 to-red-600';
  if (percentage < 70) return 'from-amber-500 to-amber-600';
  return 'from-emerald-500 to-emerald-600';
};

const TrendIndicator = ({ trend }: { trend: 'up' | 'down' | 'neutral' }) => {
  if (trend === 'up') return <ArrowUp className="h-4 w-4 text-green-500" />;
  if (trend === 'down') return <ArrowDown className="h-4 w-4 text-red-500" />;
  return null;
};

export const RestaurantPerformanceCard: React.FC<RestaurantPerformanceCardProps> = ({
  restaurantData,
  className,
  onRefresh,
  isRefreshing,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState<RestaurantSortConfig>({
    key: 'restaurant',
    direction: 'ascending',
  });

  const handleSort = (key: RestaurantSortKeys) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const sortedAndFilteredData = useMemo(() => {
    let data = [...restaurantData];

    if (searchQuery) {
      data = data.filter(item => item.restaurant.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    data.sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      if (aValue < bValue) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });

    return data;
  }, [restaurantData, searchQuery, sortConfig]);

  const SortArrow = ({ columnKey }: { columnKey: keyof RestaurantPerformanceData }) => {
    if (sortConfig.key !== columnKey) return null;
    return sortConfig.direction === 'ascending' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    );
  };

  return (
    <Card className={`h-[600px] w-full ${className || ''}`}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-xl font-bold">Restaurant Performance & Targets</CardTitle>
            <CardDescription>Comprehensive restaurant performance overview</CardDescription>
          </div>
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh} disabled={isRefreshing}>
              {isRefreshing ? 'Reloading...' : 'Auto Reload'}
            </Button>
          )}
        </div>
        <div className="mt-4 flex items-center gap-2">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Search restaurants..."
              className="pl-9"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="shrink-0">
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onSelect={() => handleSort('risk')}>Sort by Risk</DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleSort('actual')}>
                Sort by Actual
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleSort('target')}>
                Sort by Target
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-6">
        <div className="flex h-full flex-col">
          <h2 className="mb-2 flex-shrink-0 text-lg font-semibold">Performance Overview</h2>
          <div className="flex-1 overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead onClick={() => handleSort('restaurant')} className="cursor-pointer">
                    <div className="flex items-center">
                      Restaurant <SortArrow columnKey="restaurant" />
                    </div>
                  </TableHead>
                  <TableHead
                    onClick={() => handleSort('actual')}
                    className="cursor-pointer text-right"
                  >
                    <div className="flex items-center justify-end">
                      Actual <SortArrow columnKey="actual" />
                    </div>
                  </TableHead>
                  <TableHead
                    onClick={() => handleSort('target')}
                    className="cursor-pointer text-right"
                  >
                    <div className="flex items-center justify-end">
                      Target <SortArrow columnKey="target" />
                    </div>
                  </TableHead>
                  <TableHead onClick={() => handleSort('risk')} className="cursor-pointer">
                    <div className="flex items-center">
                      Risk <SortArrow columnKey="risk" />
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Achievement</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedAndFilteredData.map(item => {
                  return (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="font-medium">{item.restaurant}</div>
                      </TableCell>
                      <TableCell className="text-right font-semibold text-green-600">
                        {formatCurrency(item.actual)}
                      </TableCell>
                      <TableCell className="text-right">{formatCurrency(item.target)}</TableCell>
                      <TableCell>
                        <RiskIndicator riskLevel={item.risk} />
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Progress
                            value={item.achievement}
                            className="h-2 w-24 bg-slate-100"
                            indicatorClassName={`bg-gradient-to-r ${getAchievementColor(item.achievement)} transition-all duration-300 ease-out`}
                          />
                          <span className="font-semibold">{item.achievement.toFixed(1)}%</span>
                          <TrendIndicator trend={item.trend} />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

RestaurantPerformanceCard.displayName = 'RestaurantPerformanceCard';
