import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  validateOverallRevenueData,
  validateOverallRevenueParams,
  type OverallRevenueData,
  type OverallRevenueParams,
} from '../validations/overallRevenueSchemas';

/**
 * API service for overall revenue data following established codebase patterns
 */
export const overallRevenueApi = {
  /**
   * Fetches overall revenue data from the API
   */
  getOverallRevenue: async (params: OverallRevenueParams): Promise<OverallRevenueData> => {
    // Validate input parameters
    const validatedParams = validateOverallRevenueParams(params);

    // Make API request to the analytics endpoint
    const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.OVERVIEW, {
      params: validatedParams,
    });

    // The backend returns data directly without success/message wrapper
    // Validate and return response data
    const validatedData = validateOverallRevenueData(response.data);
    return validatedData;
  },
};
