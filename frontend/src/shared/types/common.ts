export type UserRole = 'admin' | 'staff' | 'user';

export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

export interface User {
  userId: number;
  username: string;
  email: string;
  fullName: string;
  role: UserRole;
  restaurantId: number | null;
  isActive?: boolean;
  lastLoginAt?: string | null;
  // Legacy property for backward compatibility
  name?: string;
}

export interface Restaurant extends BaseEntity {
  restaurantId?: number;
  name: string;
  address: string;
  phone: string;
  phoneNumber?: string;
  email?: string;
  managerId?: string;
  isActive: boolean;
  openingHours?: string;
  closingHours?: string;
  description?: string;
}

export interface Sale extends BaseEntity {
  restaurantId: number;
  userId: number;
  amount: number;
  date: string;
  description?: string;
  restaurant?: Restaurant;
  user?: User;
}

export interface DashboardMetrics {
  totalSales: number;
  totalRevenue: number;
  totalRestaurants: number;
  totalUsers: number;
  recentSales: Sale[];
  topRestaurants: Array<{
    restaurant: Restaurant;
    totalSales: number;
    totalRevenue: number;
  }>;
}

export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

export interface DateRange {
  from: Date;
  to: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
}

export interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  passwordChangeRequired: boolean;
}
