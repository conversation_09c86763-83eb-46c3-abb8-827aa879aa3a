// Types for header component enhancements

/**
 * Breadcrumb navigation item interface
 */
export interface BreadcrumbItem {
  /** Display label for the breadcrumb */
  label: string;
  /** Optional href for navigation (undefined for current page) */
  href?: string;
  /** Whether this is the current page */
  isCurrentPage?: boolean;
  /** Optional icon component */
  icon?: React.ComponentType<{ className?: string }>;
}

/**
 * Breadcrumb navigation metadata
 */
export interface BreadcrumbMetadata {
  /** Array of breadcrumb items */
  breadcrumbs: BreadcrumbItem[];
  /** Current page label */
  currentPage: string;
  /** Whether navigation breadcrumbs exist */
  hasNavigation: boolean;
  /** Whether user is on home page */
  isHomePage: boolean;
  /** Depth of current path */
  pathDepth: number;
}

/**
 * Header restaurant display data
 */
export interface HeaderRestaurantData {
  /** Restaurant ID */
  id: string;
  /** Restaurant name for display */
  name: string;
  /** Whether restaurant is active */
  isActive: boolean;
  /** Optional restaurant address */
  address?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
}

/**
 * Header user display data
 */
export interface HeaderUserData {
  /** User ID */
  userId: number;
  /** Username */
  username: string;
  /** User's email address */
  email: string;
  /** User's full name */
  fullName: string;
  /** User role */
  role: 'admin' | 'staff' | 'user';
  /** Associated restaurant ID */
  restaurantId: number | null;
}

/**
 * Enhanced header component props
 */
export interface EnhancedHeaderProps {
  /** Optional custom logo component */
  logoComponent?: React.ComponentType<any>;
  /** Whether to show breadcrumbs */
  showBreadcrumbs?: boolean;
  /** Whether to show restaurant name */
  showRestaurantName?: boolean;
  /** Custom breadcrumb items (overrides auto-generated) */
  customBreadcrumbs?: BreadcrumbItem[];
  /** Additional CSS classes */
  className?: string;
  /** Whether to hide breadcrumbs on mobile */
  hideBreadcrumbsOnMobile?: boolean;
}

/**
 * Header state interface for managing loading and error states
 */
export interface HeaderState {
  /** Restaurant data loading state */
  restaurantLoading: boolean;
  /** Restaurant data error */
  restaurantError: string | null;
  /** User data loading state */
  userLoading: boolean;
  /** User data error */
  userError: string | null;
  /** Breadcrumbs loading state */
  breadcrumbsLoading: boolean;
}

/**
 * Header actions interface
 */
export interface HeaderActions {
  /** Handle logo click */
  onLogoClick?: () => void;
  /** Handle breadcrumb item click */
  onBreadcrumbClick?: (item: BreadcrumbItem) => void;
  /** Handle restaurant name click */
  onRestaurantClick?: (restaurant: HeaderRestaurantData) => void;
  /** Handle user dropdown actions */
  onUserAction?: (action: 'profile' | 'settings' | 'logout') => void;
}

/**
 * Logo component props interface
 */
export interface LogoProps {
  /** Additional CSS classes */
  className?: string;
  /** Logo size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show text alongside logo */
  showText?: boolean;
  /** Navigation href */
  href?: string;
  /** Click handler */
  onClick?: () => void;
}

/**
 * Restaurant name display component props
 */
export interface RestaurantNameProps {
  /** Restaurant data */
  restaurant: HeaderRestaurantData | null;
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Click handler */
  onClick?: (restaurant: HeaderRestaurantData) => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Enhanced user dropdown props
 */
export interface EnhancedUserDropdownProps {
  /** User data */
  user: HeaderUserData | null;
  /** Logout handler */
  onLogout: () => void;
  /** Navigation handler */
  onNavigate: (path: string) => void;
  /** Additional CSS classes */
  className?: string;
}
