import { toast } from 'sonner';

type Toast = {
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
};

const useToast = () => {
  return {
    toast: ({ title, description, action }: Toast) => {
      toast(description, {
        action,
      });
    },
    success: ({ title, description, action }: Toast) => {
      toast.success(title, {
        description,
        action,
      });
    },
    error: ({ title, description, action }: Toast) => {
      toast.error(title, {
        description,
        action,
      });
    },
    info: ({ title, description, action }: Toast) => {
      toast.info(title, {
        description,
        action,
      });
    },
    warning: ({ title, description, action }: Toast) => {
      toast.warning(title, {
        description,
        action,
      });
    },
  };
};

export { useToast };
