import { ReactNode } from 'react';
import { FieldError } from 'react-hook-form';
import { Label } from '@/shared/components/ui/label';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  label?: string;
  error?: FieldError | string;
  required?: boolean;
  children: ReactNode;
  className?: string;
  description?: string;
}

export function FormField({
  label,
  error,
  required,
  children,
  className,
  description,
}: FormFieldProps) {
  const errorMessage = typeof error === 'string' ? error : error?.message;

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}
      {children}
      {description && <p className="text-muted-foreground text-xs">{description}</p>}
      {errorMessage && <p className="text-destructive text-xs">{errorMessage}</p>}
    </div>
  );
}

interface FormSectionProps {
  title: string;
  description?: string;
  children: ReactNode;
  className?: string;
}

export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn('space-y-4', className)}>
      <div>
        <h3 className="text-lg font-medium">{title}</h3>
        {description && <p className="text-muted-foreground text-sm">{description}</p>}
      </div>
      <div className="space-y-4">{children}</div>
    </div>
  );
}

interface FormActionsProps {
  children: ReactNode;
  className?: string;
}

export function FormActions({ children, className }: FormActionsProps) {
  return (
    <div className={cn('flex items-center justify-end space-x-2 pt-4', className)}>{children}</div>
  );
}
