import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { cn } from '@/lib/utils';
import { LayoutDashboard, Users, PlusCircle, BarChart3, Settings, User } from 'lucide-react';
import {
  Sidebar as SidebarPrimitive,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from '@/shared/components/ui/sidebar';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/shared/components/ui/tooltip';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  adminOnly?: boolean;
  regularUserOnly?: boolean;
}

const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/',
    icon: LayoutDashboard,
  },
  {
    title: 'Overview',
    href: '/overview',
    icon: BarChart3, // Changed from FileIcon to BarChart3 for better semantic meaning
    adminOnly: true,
  },
  // {
  //   title: 'Admin Dashboard',
  //   href: '/admin',
  //   icon: LayoutDashboard,
  //   adminOnly: true,
  // },
  {
    title: 'Users',
    href: '/users',
    icon: Users,
    adminOnly: true,
  },
  // {
  //   title: 'Restaurants',
  //   href: '/restaurants',
  //   icon: Building2,
  //   adminOnly: true,
  // },
  {
    title: 'Sales Entry',
    href: '/sales/entry',
    icon: PlusCircle, // Changed from TrendingUp to PlusCircle for better semantic meaning
    regularUserOnly: true,
  },
  {
    title: 'WhatsApp Login',
    href: '/whatsapp/login',
    icon: Users,
    adminOnly: true,
  },
  // {
  //   title: 'Sales Reports',
  //   href: '/sales/reports',
  //   icon: FileText,
  // },
  // {
  //   title: 'Audit Logs',
  //   href: '/audit-logs',
  //   icon: Activity,
  //   adminOnly: true,
  // },
  {
    title: 'Profile',
    href: '/profile',
    icon: User,
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
];

export function Sidebar() {
  const location = useLocation();
  const { user } = useAuth();
  const { state } = useSidebar();
  const isAdmin = user?.role === 'admin';
  const isCollapsed = state === 'collapsed';

  const filteredNavItems = navItems.filter(item => {
    if (item.adminOnly) {
      return isAdmin;
    }
    if (item.regularUserOnly) {
      return !isAdmin;
    }
    return true;
  });

  return (
    <SidebarPrimitive side="left" variant="sidebar" collapsible="icon">
      <SidebarHeader>
        <div
          className={cn(
            'flex items-center gap-2 px-4 py-2',
            isCollapsed ? 'justify-center' : 'justify-start',
          )}
        >
          <div className="flex items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground aspect-square size-8">
            <span className="text-sm font-semibold">B</span>
          </div>
          {!isCollapsed && (
            <div className="grid flex-1 text-sm leading-tight text-left">
              <span className="font-semibold truncate">Broku Sales</span>
              <span className="text-xs truncate">Dashboard</span>
            </div>
          )}
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredNavItems.map(item => {
                const Icon = item.icon;
                const isActive = location.pathname === item.href;

                return (
                  <SidebarMenuItem key={item.href}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton
                          asChild
                          isActive={isActive}
                          className={cn(
                            'w-full justify-start',
                            isActive && 'bg-accent text-accent-foreground',
                          )}
                        >
                          <Link to={item.href}>
                            <Icon className="w-4 h-4" aria-hidden="true" />
                            <span
                              className={cn(
                                'transition-opacity duration-200',
                                isCollapsed && 'opacity-0',
                              )}
                            >
                              {item.title}
                            </span>
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      {isCollapsed && (
                        <TooltipContent side="right" align="center">
                          <p>{item.title}</p>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </SidebarPrimitive>
  );
}
