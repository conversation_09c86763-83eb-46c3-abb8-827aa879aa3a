import { ReactNode } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { SidebarProvider, SidebarInset } from '@/shared/components/ui/sidebar';

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider>
      <Sidebar />
      <SidebarInset>
        <Header />
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mx-auto grid w-full max-w-6xl gap-2">
            <main className="flex-1 space-y-4">{children}</main>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
