import apiClient from './client';

// Restaurant interface
export interface Restaurant {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Restaurant API service
export const restaurantApi = {
  // Get all restaurants
  getAll: async (): Promise<Restaurant[]> => {
    const response = await apiClient.get('/api/v1/restaurants');
    return response.data;
  },

  // Get restaurant by ID
  getById: async (id: string): Promise<Restaurant> => {
    const response = await apiClient.get(`/api/v1/restaurants/${id}`);
    return response.data;
  },

  // Create new restaurant
  create: async (data: Omit<Restaurant, 'id' | 'createdAt' | 'updatedAt'>): Promise<Restaurant> => {
    const response = await apiClient.post('/api/v1/restaurants', data);
    return response.data;
  },

  // Update restaurant
  update: async (
    id: string,
    data: Partial<Omit<Restaurant, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<Restaurant> => {
    const response = await apiClient.put(`/api/v1/restaurants/${id}`, data);
    return response.data;
  },

  // Delete restaurant
  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/v1/restaurants/${id}`);
  },

  // Get active restaurants only
  getActive: async (): Promise<Restaurant[]> => {
    const response = await apiClient.get('/api/v1/restaurants?active=true');
    return response.data;
  },
};

export default restaurantApi;
