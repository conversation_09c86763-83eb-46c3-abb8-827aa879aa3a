import { z } from 'zod';

// Common field validations
export const emailSchema = z.string().min(1, 'Email is required').email('Invalid email format');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number');

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters');

export const phoneSchema = z
  .string()
  .min(1, 'Phone number is required')
  .regex(/^\+?[\d\s\-\(\)]{10,}$/, 'Invalid phone number format');

export const addressSchema = z
  .string()
  .min(1, 'Address is required')
  .min(5, 'Address must be at least 5 characters')
  .max(200, 'Address must be less than 200 characters');

export const currencySchema = z
  .number()
  .min(0, 'Amount must be positive')
  .max(999999.99, 'Amount is too large');

export const dateSchema = z
  .string()
  .min(1, 'Date is required')
  .refine(date => !isNaN(Date.parse(date)), 'Invalid date format');

export const idSchema = z.string().min(1, 'ID is required').uuid('Invalid ID format');

// Pagination schemas
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Filter schemas
export const dateRangeSchema = z.object({
  from: dateSchema.optional(),
  to: dateSchema.optional(),
});

export const searchSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  ...dateRangeSchema.shape,
});

// Base entity schema
export const baseEntitySchema = z.object({
  id: idSchema,
  createdAt: dateSchema,
  updatedAt: dateSchema,
  deletedAt: dateSchema.nullable().optional(),
});
