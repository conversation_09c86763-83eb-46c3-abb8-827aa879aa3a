/* Component-specific styles */

/* Loading spinner */
.loading-spinner {
  @apply border-t-primary animate-spin rounded-full border-2 border-gray-300;
}

/* Form styles */
.form-group {
  @apply space-y-2;
}

.form-error {
  @apply text-destructive text-sm;
}

.form-help {
  @apply text-muted-foreground text-sm;
}

/* Table styles */
.table-container {
  @apply overflow-x-auto;
}

.table-header {
  @apply bg-muted/50 font-medium;
}

.table-row {
  @apply hover:bg-muted/50 border-b transition-colors;
}

/* Card styles */
.metric-card {
  @apply bg-card text-card-foreground rounded-lg border shadow-sm;
}

.metric-value {
  @apply text-2xl font-bold;
}

.metric-change {
  @apply text-muted-foreground text-xs;
}

.metric-change.positive {
  @apply text-green-600;
}

.metric-change.negative {
  @apply text-red-600;
}

/* Navigation styles */
.nav-link {
  @apply flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors;
}

.nav-link.active {
  @apply bg-accent text-accent-foreground;
}

.nav-link:not(.active) {
  @apply text-muted-foreground hover:bg-accent hover:text-accent-foreground;
}

/* Button variants */
.btn-loading {
  @apply cursor-not-allowed opacity-50;
}

/* Responsive utilities */
.container-responsive {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-responsive {
  @apply grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}
