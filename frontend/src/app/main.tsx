import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import {
  createB<PERSON>er<PERSON><PERSON>er,
  RouterProvider,
  createRoutesFromElements,
  Route,
} from 'react-router-dom';
import { QueryProvider } from './providers/QueryProvider';
import { AuthProvider } from './providers/AuthProvider';
import App from './App';
import '@/styles/globals.css';

// Create router with future flags
const router = createBrowserRouter(
  createRoutesFromElements(
    <Route
      path="*"
      element={
        <QueryProvider>
          <AuthProvider>
            <App />
          </AuthProvider>
        </QueryProvider>
      }
    />
  ),
  {
    future: {
      v7_relativeSplatPath: true,
    },
  }
);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);
