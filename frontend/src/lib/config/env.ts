export const env = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '',
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  APP_NAME: import.meta.env.VITE_APP_NAME || 'Broku Sales Dashboard',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
} as const;

export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';
