import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { env } from '@/lib/config/env';
import { API_CONFIG } from '@/lib/config/constants';
import { getStoredToken, removeStoredToken } from '@/lib/storage/localStorage';

// Create axios instance
// Log initial configuration
console.log('API Client Configuration:', {
  baseURL: env.API_BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
});

const apiClient: AxiosInstance = axios.create({
  baseURL: env.API_BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  config => {
    console.log('Making API request:', {
      method: config.method,
      url: config.url,
      baseURL: config.baseURL,
      headers: config.headers,
    });
    const token = getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  error => {
    // Handle 401 errors by removing token and redirecting to login
    if (error.response?.status === 401) {
      removeStoredToken();
      window.location.href = '/login';
    }

    // Handle network errors
    if (!error.response) {
      console.error('Network Error Details:', {
        message: error.message,
        config: error.config,
        code: error.code,
        stack: error.stack,
      });
      error.message = 'Network error. Please check your connection.';
    }

    return Promise.reject(error);
  }
);

export { apiClient };

// Helper functions for common HTTP methods
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),

  post: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> => apiClient.post(url, data, config),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),

  patch: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> => apiClient.patch(url, data, config),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),
};
