import { STORAGE_KEYS } from '@/shared/utils/constants';

export function getStoredToken(): string | null {
  try {
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch {
    return null;
  }
}

export function setStoredToken(token: string): void {
  try {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
  } catch (error) {
    console.error('Failed to store auth token:', error);
  }
}

export function removeStoredToken(): void {
  try {
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch (error) {
    console.error('Failed to remove auth token:', error);
  }
}

export function getStoredUserPreferences(): any {
  try {
    const preferences = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
    return preferences ? JSON.parse(preferences) : null;
  } catch {
    return null;
  }
}

export function setStoredUserPreferences(preferences: any): void {
  try {
    localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
  } catch (error) {
    console.error('Failed to store user preferences:', error);
  }
}

export function getStoredTheme(): string | null {
  try {
    return localStorage.getItem(STORAGE_KEYS.THEME);
  } catch {
    return null;
  }
}

export function setStoredTheme(theme: string): void {
  try {
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
  } catch (error) {
    console.error('Failed to store theme:', error);
  }
}
