{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@broku/shared-types": ["../packages/shared-types/src"],
      "@broku/shared-validations": ["../packages/shared-validations/src"],
      "@broku/shared-utils": ["../packages/shared-utils/src"]
    }
  },
  "include": ["src"],
  "references": [
    { "path": "./tsconfig.node.json" },
    { "path": "../packages/shared-types" },
    { "path": "../packages/shared-validations" },
    { "path": "../packages/shared-utils" }
  ]
}
