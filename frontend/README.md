# Broku Sales Dashboard - Frontend

A modern React frontend application built with TypeScript, Vite, and Tailwind CSS for the Broku Sales Dashboard.

## Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: TanStack React Query
- **Forms**: React Hook Form + Zod validation
- **Tables**: TanStack Table
- **Routing**: React Router DOM
- **Testing**: Vitest + React Testing Library
- **Icons**: Lucide React
- **Charts**: Recharts

## Architecture

The frontend follows a features-based architecture with SOLID principles:

```
src/
├── app/                    # App configuration and providers
├── features/              # Feature modules (self-contained)
│   ├── auth/             # Authentication
│   ├── dashboard/        # Dashboard views
│   ├── user-management/  # User CRUD
│   ├── restaurant-management/
│   ├── sales/           # Sales entry and reports
│   ├── profile/         # User profile
│   └── settings/        # App settings
├── shared/               # Shared utilities and components
│   ├── components/      # Reusable UI components
│   ├── hooks/          # Custom hooks
│   ├── types/          # TypeScript types
│   ├── utils/          # Utility functions
│   └── validations/    # Zod schemas
├── lib/                 # External library configurations
└── styles/             # Global styles
```

## Features

- **Authentication**: Login/signup with JWT tokens
- **Role-based Access**: Admin and user roles with different permissions
- **Dashboard**: Admin and user dashboards with metrics and charts
- **User Management**: CRUD operations for users (admin only)
- **Restaurant Management**: CRUD operations for restaurants (admin only)
- **Sales Management**: Sales entry and reporting
- **Profile Management**: User profile and password management
- **Settings**: Application preferences and configuration

## Getting Started

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Set up environment variables**:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration.

3. **Start development server**:

   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage

## Environment Variables

- `VITE_API_BASE_URL` - Backend API URL (default: http://localhost:3000)
- `VITE_APP_NAME` - Application name
- `VITE_APP_VERSION` - Application version

## Development Guidelines

### Component Structure

Each feature follows this structure:

- `components/` - React components
- `hooks/` - Custom hooks for data fetching
- `services/` - API service functions
- `types/` - TypeScript interfaces
- `validations/` - Zod validation schemas
- `pages/` - Page components

### State Management

- Use TanStack React Query for server state
- Use React hooks for local component state
- Use Context API for global app state (auth)

### Styling

- Use Tailwind CSS utility classes
- Use shadcn/ui components for consistent design
- Follow the design system defined in globals.css

### Type Safety

- All components and functions are fully typed
- Use Zod for runtime validation
- Leverage TypeScript strict mode

## API Integration

The frontend communicates with the backend through:

- Axios HTTP client with interceptors
- Automatic token management
- Error handling and retry logic
- Request/response type safety
