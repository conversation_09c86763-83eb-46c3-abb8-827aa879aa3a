{"name": "@broku/shared-utils", "version": "1.0.0", "description": "Shared utility functions for Broku Sales Dashboard", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": ["typescript", "utilities", "shared"], "author": "", "license": "ISC", "dependencies": {"date-fns": "^4.1.0"}, "devDependencies": {"typescript": "^5.8.3"}, "files": ["dist"]}