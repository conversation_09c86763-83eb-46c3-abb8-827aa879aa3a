import { format, parseISO, isValid } from 'date-fns'

// Date formatting utilities
export function formatDate(date: string | Date, formatStr = 'MMM dd, yyyy'): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) {
      return 'Invalid date'
    }
    return format(dateObj, formatStr)
  } catch {
    return 'Invalid date'
  }
}

export function formatDateTime(date: string | Date): string {
  return formatDate(date, 'MMM dd, yyyy HH:mm')
}

export function formatTime(date: string | Date): string {
  return formatDate(date, 'HH:mm')
}

export function formatLastUpdated(date: Date): string {
  return formatDate(date, 'MMM dd, yyyy \'at\' HH:mm')
}

// Date utility functions
export function isDateInRange(date: Date, startDate: Date, endDate: Date): boolean {
  return date >= startDate && date <= endDate
}

export function getDaysAgo(days: number): Date {
  const date = new Date()
  date.setDate(date.getDate() - days)
  return date
}

export function getStartOfDay(date: Date): Date {
  const newDate = new Date(date.getTime())
  newDate.setHours(0, 0, 0, 0)
  return newDate
}

export function getEndOfDay(date: Date): Date {
  const newDate = new Date(date.getTime())
  newDate.setHours(23, 59, 59, 999)
  return newDate
}

// Date period types
export type DatePeriod = 'today' | 'week' | 'month' | 'quarter' | 'year' | '6m' | '12m' | '24m'

// Parse custom period strings like "30d", "4w", "6m"
function parseCustomPeriod(period: string): { value: number; unit: string } {
  const match = period.match(/^(\d+)([dwm])$/)
  if (!match || !match[1] || !match[2]) {
    throw new Error(`Invalid custom period format: ${period}`)
  }
  return {
    value: parseInt(match[1], 10),
    unit: match[2],
  }
}

/**
 * Calculate date range based on a specified period
 */
export function calculateDateRange(period: DatePeriod | string): {
  startDate: string
  endDate: string
} {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  if (typeof period === 'string') {
    // Handle custom period strings like "30d", "4w", "6m"
    const predefinedPeriods = ['today', 'week', 'month', 'quarter', 'year', '6m', '12m', '24m']
    if (predefinedPeriods.indexOf(period) === -1) {
      const { value, unit } = parseCustomPeriod(period)
      const startDate = new Date(today.getTime())

      switch (unit) {
        case 'd':
          startDate.setDate(today.getDate() - value)
          break
        case 'w':
          startDate.setDate(today.getDate() - value * 7)
          break
        case 'm':
          startDate.setMonth(today.getMonth() - value)
          break
        default:
          throw new Error(`Unsupported custom period unit: ${unit}`)
      }

      startDate.setHours(0, 0, 0, 0)
      today.setHours(23, 59, 59, 999)

      return {
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(today, 'yyyy-MM-dd')
      }
    }
  }

  // Handle predefined period types
  let startDate: Date
  const endDate = new Date(today.getTime())
  endDate.setHours(23, 59, 59, 999)

  // Validate period is a supported enum value
  const validPeriods = ['today', 'week', 'month', 'quarter', 'year', '6m', '12m', '24m']
  if (validPeriods.indexOf(period) === -1) {
    throw new Error(`Invalid period: "${period}". Supported periods: ${validPeriods.join(', ')}`)
  }

  startDate = new Date(today.getTime())

  try {
    switch (period) {
      case 'today':
        // For today, start and end are the same day
        break
      case 'week':
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1)
        break
      case 'quarter':
        startDate.setMonth(startDate.getMonth() - 3)
        break
      case 'year':
      case '12m':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      case '6m':
        startDate.setMonth(startDate.getMonth() - 6)
        break
      case '24m':
        startDate.setFullYear(startDate.getFullYear() - 2)
        break
      default:
        throw new Error(`Unhandled period: ${period}`)
    }

    startDate.setHours(0, 0, 0, 0)

    // Validate the calculated dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Invalid date calculation result')
    }

    return {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd')
    }
  } catch (error) {
    throw new Error(`Failed to calculate date range for period "${period}": ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
