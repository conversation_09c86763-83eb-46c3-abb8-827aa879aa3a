// Standard API response structure
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: Array<{
    field: string
    message: string
    code: string
  }>
}

// Error response structure
export interface ApiError {
  success: false
  message: string
  errors?: Array<{
    field: string
    message: string
    code: string
  }>
  statusCode?: number
}

// Success response structure
export interface ApiSuccess<T = any> {
  success: true
  message: string
  data: T
}

// Query parameters for filtering and pagination
export interface QueryParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}
