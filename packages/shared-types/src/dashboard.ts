import { Restaurant } from './common'

export interface DashboardMetrics {
  totalSales: number
  totalRevenue: number
  totalRestaurants: number
  totalUsers: number
  recentSales: Array<{
    id: number
    amount: number
    date: string
    restaurantName: string
  }>
  topRestaurants: Array<{
    restaurant: Restaurant
    totalSales: number
    totalRevenue: number
  }>
}

export interface ChartData {
  name: string
  value: number
  [key: string]: any
}

export interface RevenueData {
  month: string
  actual: number
  forecast: number
  achievement: number
}

export interface RestaurantPerformance {
  restaurantId: number
  name: string
  revenue: number
  target: number
  achievement: number
  risk: 'low' | 'medium' | 'high'
  trend: 'up' | 'down' | 'stable'
}

export interface OverviewData {
  totalRevenue: number
  forecastRevenue: number
  achievement: number
  chartData: RevenueData[]
  restaurants: RestaurantPerformance[]
  targets: Array<{
    id: string
    name: string
    target: number
    actual: number
    achievement: number
  }>
}
