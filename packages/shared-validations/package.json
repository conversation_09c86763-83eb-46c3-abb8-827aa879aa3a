{"name": "@broku/shared-validations", "version": "1.0.0", "description": "Shared Zod validation schemas for Broku Sales Dashboard", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "keywords": ["typescript", "zod", "validation", "shared"], "author": "", "license": "ISC", "dependencies": {"zod": "^3.25.67"}, "devDependencies": {"typescript": "^5.8.3"}, "files": ["dist"]}