import { z } from 'zod'
import { emailSchema, passwordSchema, nameSchema } from './base'

// Role validation
export const roleSchema = z.enum(['admin', 'staff', 'user'], {
  errorMap: () => ({ message: 'Role must be admin, staff, or user' })
})

// Username validation
export const usernameSchema = z
  .string()
  .min(1, 'Username is required')
  .min(3, 'Username must be at least 3 characters')
  .max(50, 'Username must be less than 50 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')

// Restaurant ID validation
export const restaurantIdSchema = z
  .number()
  .positive('Restaurant ID must be a positive number')
  .optional()

// Login request validation
export const loginSchema = z.object({
  username: usernameSchema,
  password: z.string().min(1, 'Password is required'),
})

// Register request validation
export const registerSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  fullName: nameSchema,
  role: roleSchema.default('user'),
  restaurantId: restaurantIdSchema,
})

// Change password validation
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string().min(1, 'Please confirm your new password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// Reset password validation
export const resetPasswordSchema = z.object({
  email: emailSchema,
})

// JWT payload validation
export const jwtPayloadSchema = z.object({
  userId: z.number(),
  username: z.string(),
  email: z.string(),
  role: roleSchema,
  restaurantId: z.number().optional(),
  iat: z.number().optional(),
  exp: z.number().optional(),
})
