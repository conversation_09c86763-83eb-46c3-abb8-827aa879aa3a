import { z } from 'zod'

// Common field validations
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Invalid email format')

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number')

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters')

export const phoneSchema = z
  .string()
  .min(1, 'Phone number is required')
  .regex(/^\+?[\d\s\-\(\)]{10,}$/, 'Invalid phone number format')

export const addressSchema = z
  .string()
  .min(1, 'Address is required')
  .min(5, 'Address must be at least 5 characters')
  .max(200, 'Address must be less than 200 characters')

// Currency validation (for monetary values)
export const currencySchema = z
  .number()
  .min(0, 'Amount must be non-negative')
  .max(999999.99, 'Amount must be less than 1,000,000')
  .multipleOf(0.01, 'Amount must have at most 2 decimal places')

// Alternative string-based currency validation for form inputs
export const currencyStringSchema = z
  .string()
  .regex(/^\d+(\.\d{1,2})?$/, 'Amount must be a valid decimal with at most 2 decimal places')
  .transform((val) => parseFloat(val))
  .refine((val) => val >= 0 && val <= 999999.99, 'Amount must be between 0 and 999,999.99')

export const dateSchema = z
  .string()
  .min(1, 'Date is required')
  .refine((date) => !isNaN(Date.parse(date)), 'Invalid date format')

export const idSchema = z
  .string()
  .min(1, 'ID is required')
  .uuid('Invalid ID format')

export const numericIdSchema = z
  .number()
  .positive('ID must be a positive number')

// Pagination schemas
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// Query parameters schema
export const queryParamsSchema = z.object({
  search: z.string().optional(),
  filters: z.record(z.any()).optional(),
}).merge(paginationSchema)
