import { z } from 'zod'
import { nameSchema, phoneSchema, addressSchema, emailSchema, idSchema, paginationSchema } from './base'

// Restaurant form validation schema
export const restaurantFormSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
})

// Restaurant update validation schema
export const restaurantUpdateSchema = z.object({
  name: nameSchema.optional(),
  address: addressSchema.optional(),
  phone: phoneSchema.optional(),
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
  isActive: z.boolean().optional(),
})

// Restaurant creation validation schema
export const createRestaurantSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional(),
  managerId: z.number().positive().optional(),
})

// Restaurant filters validation
export const restaurantFiltersSchema = z.object({
  isActive: z.boolean().optional(),
  managerId: z.number().positive().optional(),
  search: z.string().optional(),
}).merge(paginationSchema)

// Restaurant ID parameter validation
export const restaurantIdParamSchema = z.object({
  id: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val) && val > 0, {
    message: 'Restaurant ID must be a positive number',
  }),
})
