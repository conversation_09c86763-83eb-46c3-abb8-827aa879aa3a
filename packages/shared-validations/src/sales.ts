import { z } from 'zod'
import { currencySchema, dateSchema, paginationSchema } from './base'

// Sales creation validation
export const createSalesSchema = z.object({
  restaurantId: z.number().positive('Restaurant ID must be a positive number'),
  salesDate: dateSchema,
  cashAmount: currencySchema.default(0),
  cardAmount: currencySchema.default(0),
  onlinePaymentAmount: currencySchema.default(0),
  totalDiscounts: currencySchema.default(0),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
}).transform((data) => ({
  ...data,
  totalSales: data.cashAmount + data.cardAmount + data.onlinePaymentAmount,
})).refine(
  (data) => data.totalSales > 0,
  {
    message: 'Total sales must be greater than 0',
    path: ['totalSales'],
  }
).refine(
  (data) => data.totalDiscounts <= data.totalSales,
  {
    message: 'Total discounts cannot exceed total sales',
    path: ['totalDiscounts'],
  }
)

// Sales update validation
export const updateSalesSchema = z.object({
  salesDate: dateSchema.optional(),
  cashAmount: currencySchema.optional(),
  cardAmount: currencySchema.optional(),
  onlinePaymentAmount: currencySchema.optional(),
  totalDiscounts: currencySchema.optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
}).refine((data) => {
  // If any amount is provided, validate total sales > 0
  const amounts = [data.cashAmount, data.cardAmount, data.onlinePaymentAmount].filter(Boolean) as number[]
  if (amounts.length > 0) {
    const total = amounts.reduce((sum: number, amount: number) => sum + amount, 0)
    return total > 0
  }
  return true
}, {
  message: 'Total sales must be greater than 0',
  path: ['totalSales'],
})

// Sales query validation
export const salesQuerySchema = z.object({
  restaurantId: z.number().positive().optional(),
  startDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  minAmount: currencySchema.optional(),
  maxAmount: currencySchema.optional(),
}).merge(paginationSchema)

// Sales report validation
export const salesReportSchema = z.object({
  restaurantId: z.number().positive().optional(),
  startDate: dateSchema,
  endDate: dateSchema,
  groupBy: z.enum(['day', 'week', 'month']).default('day'),
  includeDetails: z.boolean().default(false),
})

// Sales analytics validation
export const salesAnalyticsSchema = z.object({
  restaurantId: z.number().positive().optional(),
  period: z.enum(['week', 'month', 'quarter', 'year']).default('month'),
  compareWith: z.enum(['previous', 'year']).optional(),
})

// Bulk sales operation validation
export const bulkSalesOperationSchema = z.object({
  salesIds: z.array(z.number().positive()).min(1, 'At least one sales record must be selected'),
  action: z.enum(['delete', 'export']),
})
