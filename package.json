{"name": "broku-sales-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "pnpm run --parallel dev", "dev:frontend": "pnpm --filter @broku/frontend dev", "dev:backend": "pnpm --filter @broku/backend dev", "build": "pnpm run build:shared && pnpm run --parallel build", "build:shared": "pnpm --filter '@broku/shared-*' build", "build:frontend": "pnpm --filter @broku/frontend build", "build:backend": "pnpm --filter @broku/backend build", "lint": "pnpm run --parallel lint", "lint:frontend": "pnpm --filter @broku/frontend lint", "lint:backend": "pnpm --filter @broku/backend lint", "format": "pnpm run --parallel format", "format:frontend": "pnpm --filter @broku/frontend format", "format:backend": "pnpm --filter @broku/backend format", "type-check": "tsc --build", "clean": "pnpm --filter '@broku/*' clean"}, "devDependencies": {"@eslint/js": "^9.30.1", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^9.30.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "typescript": "^5.8.3"}}