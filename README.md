# Broku Sales Dashboard

A comprehensive sales dashboard application built with modern web technologies, featuring role-based access control, real-time analytics, and comprehensive audit logging.

## 🏗️ Architecture

This project follows a **monorepo structure** with **clean architecture principles** and **features-based organization**:

```
broku-sales-dashboard/
├── frontend/                    # React frontend application
├── backend/                     # Express.js backend with clean architecture
├── packages/                    # Shared packages
│   ├── shared-types/           # Common TypeScript types
│   ├── shared-validations/     # Zod validation schemas
│   └── shared-utils/           # Utility functions
├── package.json                # Root workspace configuration
├── pnpm-workspace.yaml         # pnpm workspace configuration
└── tsconfig.base.json          # Base TypeScript configuration
```

## 🚀 Features

### Core Functionality
- **Authentication & Authorization**: JWT-based auth with role-based access control (Ad<PERSON>, Staff, User)
- **Dashboard Analytics**: Real-time metrics, charts, and KPI tracking
- **User Management**: Complete CRUD operations with role management
- **Restaurant Management**: Multi-location restaurant administration
- **Sales Management**: Sales entry, reporting, and analytics
- **Audit Logging**: Comprehensive activity tracking and compliance
- **Profile Management**: User profile and password management

### Advanced Features
- **Real-time Data**: Live dashboard updates with TanStack React Query
- **Progressive Disclosure**: Clean UI with advanced options hidden by default
- **Mobile-First Design**: Responsive design optimized for all devices
- **Type Safety**: End-to-end TypeScript with runtime validation
- **Error Handling**: Comprehensive error boundaries and recovery
- **Performance Optimization**: Optimized queries, caching, and lazy loading
- **WhatsApp Integration**: Real-time notifications and daily summaries via WhatsApp.

## 🛠️ Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + @shadcn/ui components
- **State Management**: TanStack React Query
- **Forms**: React Hook Form + Zod validation
- **Tables**: TanStack Table
- **Routing**: React Router DOM
- **Charts**: Recharts
- **Testing**: Vitest + React Testing Library
- **Icons**: Lucide React

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Architecture**: Clean Architecture (Presentation → Business → Data → Infrastructure)
- **Database**: MySQL with Drizzle ORM
- **Validation**: Zod schemas
- **Authentication**: JWT tokens with bcrypt
- **Logging**: Pino structured logging
- **Testing**: Jest/Vitest

### Shared Packages
- **@broku/shared-types**: Common TypeScript interfaces
- **@broku/shared-validations**: Zod validation schemas
- **@broku/shared-utils**: Utility functions

## 📋 Prerequisites

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0
- **MySQL** >= 8.0

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd broku-sales-dashboard
pnpm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=your_password
DATABASE_NAME=broku_sales

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1d

# Application URLs
VITE_API_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:5173
```

### 3. Database Setup

```bash
# Generate database schema
pnpm --filter @broku/backend db:generate

# Run migrations
pnpm --filter @broku/backend db:migrate

# Seed with sample data
pnpm --filter @broku/backend db:seed
```

### 4. Start Development

```bash
# Start both frontend and backend
pnpm dev

# Or start individually
pnpm dev:frontend  # http://localhost:5173
pnpm dev:backend   # http://localhost:3000
```

## 📜 Available Scripts

### Root Level Commands

```bash
pnpm dev              # Start both applications in development mode
pnpm build            # Build all applications and shared packages
pnpm lint             # Lint all applications
pnpm format           # Format code in all applications
pnpm type-check       # Run TypeScript type checking
pnpm clean            # Clean all build artifacts
```

### Workspace-Specific Commands

```bash
# Frontend commands
pnpm --filter @broku/frontend dev
pnpm --filter @broku/frontend build
pnpm --filter @broku/frontend test

# Backend commands
pnpm --filter @broku/backend dev
pnpm --filter @broku/backend build
pnpm --filter @broku/backend db:studio    # Open Drizzle Studio
pnpm --filter @broku/backend db:seed      # Seed database

# Shared packages
pnpm --filter '@broku/shared-*' build
```

## 🏛️ Architecture Details

### Backend Architecture

The backend follows **Clean Architecture** principles with clear separation of concerns:

```
backend/src/
├── presentation/           # Controllers, routes, middleware
│   ├── controllers/       # Request/response handling
│   ├── routes/           # API route definitions
│   ├── middleware/       # Auth, validation, error handling
│   └── router/           # Main router configuration
├── business/             # Business logic and use cases
│   ├── services/         # Business logic implementation
│   ├── validators/       # Input validation schemas
│   └── types/           # Business domain types
├── data/                # Data access layer
│   ├── models/          # Database schema definitions
│   ├── repositories/    # Data access patterns
│   └── migrations/      # Database migrations
└── infrastructure/      # External concerns
    ├── config/          # Configuration management
    ├── db/             # Database connection and seeding
    ├── logger/         # Logging configuration
    └── errors/         # Error handling utilities
```

### Frontend Architecture

The frontend uses **features-based architecture** with SOLID principles:

```
frontend/src/
├── app/                    # Application configuration
│   ├── providers/         # React context providers
│   └── App.tsx           # Main application component
├── features/              # Feature modules (self-contained)
│   ├── auth/             # Authentication
│   ├── dashboard/        # Dashboard views and analytics
│   ├── user-management/  # User CRUD operations
│   ├── restaurant-management/  # Restaurant management
│   ├── sales/           # Sales entry and reporting
│   ├── audit-logs/      # Audit log viewing
│   ├── profile/         # User profile management
│   └── overview/        # Overall revenue analytics
├── shared/               # Shared utilities and components
│   ├── components/      # Reusable UI components
│   ├── hooks/          # Custom React hooks
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   └── validations/    # Zod validation schemas
└── lib/                 # External library configurations
```

## 🗄️ Database Schema

### Core Tables

- **users**: User accounts with role-based permissions
- **restaurants**: Restaurant/location management
- **sales**: Sales transaction records
- **audit_logs**: Comprehensive activity logging

### Key Features

- **Soft Deletes**: All tables support soft deletion with `deleted_at` timestamps
- **Audit Trail**: Complete change tracking with old/new values
- **Relationships**: Proper foreign key constraints and relations
- **Indexing**: Optimized queries with strategic indexes

## 🔐 Authentication & Authorization

### Roles & Permissions

- **Admin**: Full system access, user management, all restaurants
- **Staff**: Limited administrative access, assigned restaurant only
- **User**: Basic access, own restaurant data only

### Security Features

- JWT token-based authentication
- Password hashing with bcrypt
- Role-based route protection
- Request validation and sanitization
- Audit logging for all actions

## 📊 API Documentation

### Core Endpoints

```
Authentication
POST   /auth/login          # User login
POST   /auth/register       # User registration
GET    /auth/me            # Get current user profile

Users
GET    /users              # List users (paginated, filtered)
POST   /users              # Create user (admin only)
PUT    /users/:id          # Update user
DELETE /users/:id          # Delete user (admin only)

Restaurants
GET    /restaurants        # List restaurants
POST   /restaurants        # Create restaurant (admin only)
PUT    /restaurants/:id    # Update restaurant
DELETE /restaurants/:id    # Delete restaurant (admin only)

Sales
GET    /sales              # List sales (filtered by restaurant)
POST   /sales              # Create sale entry
PUT    /sales/:id          # Update sale
DELETE /sales/:id          # Delete sale

Dashboard
GET    /dashboard/metrics  # Dashboard analytics
GET    /dashboard/charts   # Chart data

Audit Logs
GET    /audit-logs         # List audit logs (admin only)
GET    /audit-logs/stats   # Audit statistics

Analysis
GET    /analysis/hq        # Headquarters analytics (admin only)
GET    /analysis/restaurant/:id  # Restaurant analytics
```

### Response Format

All API responses follow a consistent format:

```json
{
  "success": boolean,
  "message": string,
  "data": any,
  "pagination"?: {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

## 🧪 Testing

### Frontend Testing

```bash
# Run tests
pnpm --filter @broku/frontend test

# Run tests with UI
pnpm --filter @broku/frontend test:ui

# Run tests with coverage
pnpm --filter @broku/frontend test:coverage
```

### Backend Testing

```bash
# Run tests
pnpm --filter @broku/backend test

# Run tests with coverage
pnpm --filter @broku/backend test:coverage
```

### Testing Strategy

- **Unit Tests**: Individual component and function testing
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Full user workflow testing
- **Type Safety**: Comprehensive TypeScript coverage

## 🚀 Deployment

### Production Build

```bash
# Build all applications
pnpm build

# Build specific applications
pnpm build:frontend
pnpm build:backend
```

### Environment Configuration

Ensure all environment variables are properly configured for production:

```env
NODE_ENV=production
PORT=3000
DATABASE_HOST=your-production-db-host
DATABASE_NAME=broku_sales_prod
JWT_SECRET=your-production-jwt-secret
CORS_ORIGIN=https://your-frontend-domain.com
```

### Database Migration

```bash
# Run migrations in production
pnpm --filter @broku/backend db:migrate
```

## 🔧 Development Workflow

### Code Quality

- **ESLint**: Consistent code style and error detection
- **Prettier**: Automatic code formatting
- **TypeScript**: Static type checking
- **Husky**: Pre-commit hooks (if configured)

### Best Practices

1. **Features-based Architecture**: Keep related code together
2. **Type Safety**: Use TypeScript throughout the stack
3. **Validation**: Validate data at API boundaries with Zod
4. **Error Handling**: Comprehensive error boundaries and logging
5. **Performance**: Optimize queries and implement caching
6. **Security**: Follow security best practices for authentication
7. **Testing**: Write tests for critical functionality

### Development Guidelines

1. **Component Design**: Follow progressive disclosure patterns
2. **API Design**: Maintain consistent response formats
3. **Database**: Use migrations for schema changes
4. **Logging**: Use structured logging with appropriate levels
5. **Documentation**: Keep README and code comments updated

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**
```bash
# Check database connection
pnpm --filter @broku/backend db:studio

# Reset database
pnpm --filter @broku/backend db:push
pnpm --filter @broku/backend db:seed
```

**Build Issues**
```bash
# Clean and rebuild
pnpm clean
pnpm install
pnpm build
```

**Type Errors**
```bash
# Check types across workspace
pnpm type-check
```

### Debug Mode

Enable debug logging by setting:
```env
LOG_LEVEL=debug
NODE_ENV=development
```

## 📚 Additional Resources

### Documentation

- [Frontend README](./frontend/README.md) - Detailed frontend documentation
- [Backend API Documentation](./backend/docs/) - API endpoint details
- [WhatsApp Integration](./backend/src/business/services/whatsapp/README.md) - WhatsApp service documentation
- [Database Schema](./backend/drizzle/) - Database migrations and schema

### Tools

- **Drizzle Studio**: Database management UI
- **React Query Devtools**: State management debugging
- **Postman Collection**: API testing (see `broku-sales-dashboard.postman_collection.json`)

### Key Dependencies

- [React](https://react.dev/) - Frontend framework
- [Express.js](https://expressjs.com/) - Backend framework
- [Drizzle ORM](https://orm.drizzle.team/) - Database ORM
- [TanStack React Query](https://tanstack.com/query) - Data fetching
- [Zod](https://zod.dev/) - Schema validation
- [@shadcn/ui](https://ui.shadcn.com/) - UI components
- [Tailwind CSS](https://tailwindcss.com/) - Styling

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Setup

1. Follow the Quick Start guide above
2. Create a new branch for your feature
3. Make your changes following the established patterns
4. Write tests for new functionality
5. Ensure all tests pass and code is properly formatted
6. Submit a pull request with a clear description

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern web technologies and best practices
- Follows clean architecture and SOLID principles
- Implements comprehensive security and audit logging
- Designed for scalability and maintainability

---

**Happy coding! 🚀**
